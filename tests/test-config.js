/**
 * 测试配置文件
 * Test Configuration
 */

const testConfig = {
  // 测试运行器配置
  runner: {
    timeout: 10000,        // 默认超时时间 10秒
    retries: 2,            // 默认重试次数
    parallel: false,       // 是否并行运行测试
    verbose: true,         // 详细输出
    bail: false,           // 遇到失败是否停止
    coverage: true         // 是否收集覆盖率
  },

  // 测试环境配置
  environment: {
    platform: 'miniprogram',
    mockWx: true,
    mockGetApp: true,
    mockConsole: false
  },

  // 测试套件配置
  suites: {
    unit: {
      pattern: 'tests/**/*.test.js',
      exclude: ['tests/e2e/**', 'tests/integration/**'],
      timeout: 5000
    },
    integration: {
      pattern: 'tests/integration/**/*.test.js',
      timeout: 15000
    },
    e2e: {
      pattern: 'tests/e2e/**/*.test.js',
      timeout: 30000
    },
    performance: {
      pattern: 'tests/performance/**/*.test.js',
      timeout: 10000
    }
  },

  // 覆盖率配置
  coverage: {
    enabled: true,
    threshold: {
      global: {
        branches: 70,
        functions: 80,
        lines: 80,
        statements: 80
      }
    },
    include: [
      'utils/**/*.js',
      'pages/**/*.js',
      '!pages/**/modules/**/*.js' // 排除模块文件，单独测试
    ],
    exclude: [
      'tests/**',
      'node_modules/**',
      'miniprogram_npm/**'
    ]
  },

  // 报告配置
  reporters: {
    console: {
      enabled: true,
      verbose: true
    },
    json: {
      enabled: true,
      outputFile: 'tests/reports/test-results.json'
    },
    html: {
      enabled: true,
      outputDir: 'tests/reports/html'
    },
    coverage: {
      enabled: true,
      outputDir: 'tests/reports/coverage'
    }
  },

  // Mock配置
  mocks: {
    wx: {
      // 微信小程序API模拟
      request: 'auto',
      showToast: 'auto',
      showModal: 'auto',
      navigateTo: 'auto',
      navigateBack: 'auto',
      chooseImage: 'auto',
      getStorageSync: 'auto',
      setStorageSync: 'auto',
      removeStorageSync: 'auto',
      getStorageInfoSync: 'auto',
      stopPullDownRefresh: 'auto'
    },
    getApp: {
      // getApp函数模拟
      globalData: {
        userInfo: { id: 1, name: 'Test User', token: 'test-token' },
        systemInfo: { platform: 'devtools' },
        targetTab: undefined
      }
    },
    console: {
      // 控制台输出模拟
      log: false,
      warn: false,
      error: true,
      info: false
    }
  },

  // 性能测试配置
  performance: {
    // 页面加载性能基准
    pageLoad: {
      maxTime: 2000,        // 最大加载时间 2秒
      warningTime: 1000     // 警告时间 1秒
    },
    // API响应性能基准
    apiResponse: {
      maxTime: 3000,        // 最大响应时间 3秒
      warningTime: 1500     // 警告时间 1.5秒
    },
    // 内存使用基准
    memory: {
      maxUsage: 50,         // 最大内存使用 50MB
      warningUsage: 30      // 警告内存使用 30MB
    },
    // 缓存性能基准
    cache: {
      minHitRate: 60,       // 最小命中率 60%
      targetHitRate: 80     // 目标命中率 80%
    }
  },

  // 安全测试配置
  security: {
    // 权限测试
    permissions: {
      testUnauthorized: true,
      testRoleBasedAccess: true,
      testDataIsolation: true
    },
    // 数据验证测试
    dataValidation: {
      testInputSanitization: true,
      testSQLInjection: true,
      testXSS: true
    },
    // API安全测试
    apiSecurity: {
      testAuthentication: true,
      testAuthorization: true,
      testRateLimit: true
    }
  },

  // 测试数据配置
  testData: {
    // 用户测试数据
    users: [
      {
        id: 1,
        name: 'Test Admin',
        role: 'admin',
        permissions: ['all']
      },
      {
        id: 2,
        name: 'Test User',
        role: 'user',
        permissions: ['read', 'write']
      },
      {
        id: 3,
        name: 'Test Guest',
        role: 'guest',
        permissions: ['read']
      }
    ],
    // 业务测试数据
    business: {
      healthRecords: [
        {
          id: 1,
          date: '2024-01-15',
          type: '日常检查',
          status: 'healthy',
          description: '鹅群状态良好'
        }
      ],
      materials: [
        {
          id: 1,
          name: '雏鹅专用饲料',
          stock: 120,
          status: 'normal',
          category: 'feed'
        }
      ],
      articles: [
        {
          id: 1,
          title: '雏鹅饲养管理要点',
          category: 'breeding',
          tags: ['雏鹅', '饲养']
        }
      ]
    }
  },

  // CI/CD配置
  ci: {
    // 持续集成环境检测
    isCI: process.env.CI === 'true',
    // 并行度配置
    parallel: process.env.CI === 'true' ? 4 : 1,
    // 超时配置
    timeout: process.env.CI === 'true' ? 60000 : 30000,
    // 重试配置
    retries: process.env.CI === 'true' ? 3 : 1
  }
};

module.exports = testConfig;
