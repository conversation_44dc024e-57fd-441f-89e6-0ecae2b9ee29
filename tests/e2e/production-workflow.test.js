/**
 * 生产流程端到端测试
 * Production Workflow End-to-End Tests
 */

const { suite, Assert, Mock } = require('../framework/test-runner.js');

// 模拟小程序环境
global.wx = {
  navigateTo: Mock.fn(),
  navigateBack: Mock.fn(),
  showToast: Mock.fn(),
  showModal: Mock.fn(),
  request: Mock.fn(),
  chooseImage: Mock.fn(),
  getStorageSync: Mock.fn(),
  setStorageSync: Mock.fn(),
  stopPullDownRefresh: Mock.fn()
};

global.getApp = Mock.fn(() => ({
  globalData: {
    userInfo: { id: 1, name: 'Test User' },
    targetTab: undefined
  }
}));

suite('生产流程端到端测试', (context) => {
  let productionPage;
  let ModuleManager;

  context.beforeAll(() => {
    // 导入生产页面模块
    ModuleManager = require('../../pages/production/modules/module-manager.js');
  });

  context.beforeEach(() => {
    // 重置所有mock
    Object.values(wx).forEach(mockFn => {
      if (mockFn.calls) {
        mockFn.calls = [];
        mockFn.callCount = 0;
      }
    });

    // 创建模拟页面对象
    productionPage = {
      data: {
        activeTab: 0,
        loading: false,
        refreshing: false
      },
      setData: Mock.fn((data, callback) => {
        Object.assign(productionPage.data, data);
        if (callback) callback();
      }),
      route: '/pages/production/production'
    };

    // 初始化模块管理器
    productionPage.moduleManager = new ModuleManager(productionPage);
  });

  context.test('应该正确初始化生产页面', () => {
    productionPage.moduleManager.initialize();

    Assert.true(productionPage.moduleManager.isInitialized, '模块管理器应该被初始化');
    Assert.equal(productionPage.moduleManager.modules.size, 4, '应该有4个模块');
    Assert.true(productionPage.moduleManager.hasModule('production'), '应该有生产模块');
    Assert.true(productionPage.moduleManager.hasModule('health'), '应该有健康模块');
    Assert.true(productionPage.moduleManager.hasModule('material'), '应该有物料模块');
    Assert.true(productionPage.moduleManager.hasModule('knowledge'), '应该有知识库模块');
  });

  context.test('应该正确切换到健康监测模块', () => {
    productionPage.moduleManager.initialize();
    
    const healthModule = productionPage.moduleManager.switchModule('health');
    
    Assert.true(healthModule !== null, '应该返回健康模块');
    Assert.equal(productionPage.moduleManager.getCurrentModule(), healthModule, '当前模块应该是健康模块');
  });

  context.test('应该完成完整的健康记录查看流程', async () => {
    productionPage.moduleManager.initialize();
    
    // 1. 切换到健康模块
    const healthModule = productionPage.moduleManager.switchModule('health');
    
    // 2. 模拟加载健康记录
    healthModule.fetchHealthRecords = Mock.fn().mockResolvedValue({
      success: true,
      data: [
        {
          id: 1,
          date: '2024-01-15',
          type: '日常检查',
          status: 'healthy',
          description: '鹅群状态良好'
        }
      ]
    });
    
    await healthModule.loadHealthRecords();
    
    // 3. 验证数据加载
    Assert.equal(healthModule.fetchHealthRecords.callCount, 1, '应该调用获取健康记录');
    Assert.equal(healthModule.healthRecords.length, 1, '应该有一条健康记录');
    Assert.equal(productionPage.data.loading, false, '加载状态应该完成');
    
    // 4. 生成健康报告
    await new Promise((resolve) => {
      healthModule.generateHealthReport(resolve);
    });
    
    Assert.true(typeof healthModule.reportData.overview === 'object', '应该生成健康报告');
    Assert.true(healthModule.reportData.overview.totalGeese > 0, '应该有总鹅数统计');
  });

  context.test('应该完成完整的AI诊断流程', async () => {
    productionPage.moduleManager.initialize();
    const healthModule = productionPage.moduleManager.switchModule('health');
    
    // 1. 初始化AI诊断
    healthModule.initAIDiagnosis();
    
    Assert.equal(productionPage.data.symptoms, '', '症状应该为空');
    Assert.equal(productionPage.data.uploadedImages.length, 0, '图片列表应该为空');
    Assert.equal(productionPage.data.diagnosisResult, null, '诊断结果应该为空');
    
    // 2. 输入症状
    healthModule.onSymptomsInput({
      detail: { value: '鹅只出现呼吸困难，食欲不振' }
    });
    
    Assert.equal(productionPage.data.symptoms, '鹅只出现呼吸困难，食欲不振', '症状应该正确设置');
    
    // 3. 上传图片
    wx.chooseImage.mockImplementation = (options) => {
      options.success({
        tempFilePaths: ['/temp/symptom1.jpg', '/temp/symptom2.jpg']
      });
    };
    
    healthModule.onUploadImage();
    
    Assert.equal(wx.chooseImage.callCount, 1, '应该调用图片选择');
    Assert.equal(productionPage.data.uploadedImages.length, 2, '应该有2张图片');
    
    // 4. 执行AI诊断
    healthModule.performAIDiagnosis = Mock.fn().mockResolvedValue({
      confidence: 85,
      diagnosis: '疑似呼吸道感染',
      description: '根据症状和图片分析，可能患有呼吸道感染',
      recommendations: ['隔离治疗', '使用抗生素', '密切观察'],
      severity: 'moderate'
    });
    
    await healthModule.onStartDiagnosis();
    
    Assert.equal(healthModule.performAIDiagnosis.callCount, 1, '应该调用AI诊断');
    Assert.true(productionPage.data.diagnosisResult !== null, '应该有诊断结果');
    Assert.equal(productionPage.data.diagnosisResult.diagnosis, '疑似呼吸道感染', '诊断结果应该正确');
    Assert.equal(productionPage.data.isDiagnosing, false, '诊断状态应该完成');
  });

  context.test('应该完成完整的物料管理流程', () => {
    productionPage.moduleManager.initialize();
    const materialModule = productionPage.moduleManager.switchModule('material');
    
    // 1. 初始化物料数据
    materialModule.initialize();
    
    Assert.true(Array.isArray(materialModule.materialList), '物料列表应该是数组');
    Assert.true(materialModule.materialList.length > 0, '应该有物料数据');
    Assert.true(typeof materialModule.materialStats === 'object', '应该有物料统计');
    
    // 2. 切换物料标签页
    materialModule.onMaterialTabChange({
      currentTarget: { dataset: { tab: 'feed' } }
    });
    
    Assert.equal(productionPage.data.activeMaterialTab, 'feed', '应该切换到饲料标签页');
    
    // 3. 搜索物料
    materialModule.onSearchMaterial({
      detail: { value: '饲料' }
    });
    
    const filteredList = productionPage.data.filteredMaterialList;
    Assert.true(Array.isArray(filteredList), '过滤列表应该是数组');
    
    // 4. 检查库存预警
    materialModule.onStockWarning();
    
    // 应该显示预警信息（通过showModal调用验证）
    Assert.true(wx.showModal.callCount >= 0, '库存预警功能应该正常工作');
  });

  context.test('应该完成完整的知识库搜索流程', async () => {
    productionPage.moduleManager.initialize();
    const knowledgeModule = productionPage.moduleManager.switchModule('knowledge');
    
    // 1. 初始化知识库
    knowledgeModule.fetchArticles = Mock.fn().mockResolvedValue({
      success: true,
      data: [
        {
          id: 1,
          title: '雏鹅饲养管理要点',
          summary: '雏鹅饲养的关键技术',
          category: 'breeding',
          tags: ['雏鹅', '饲养', '管理']
        },
        {
          id: 2,
          title: '鹅常见疾病预防',
          summary: '疾病预防的重要措施',
          category: 'disease',
          tags: ['疾病', '预防', '治疗']
        }
      ]
    });
    
    await knowledgeModule.loadArticles();
    
    Assert.equal(knowledgeModule.articles.length, 2, '应该加载2篇文章');
    
    // 2. 按分类过滤
    knowledgeModule.onCategoryChange({
      currentTarget: { dataset: { category: 'breeding' } }
    });
    
    Assert.equal(knowledgeModule.activeCategory, 'breeding', '应该切换到养殖技术分类');
    
    // 3. 搜索文章
    knowledgeModule.onSearchInput({
      detail: { value: '雏鹅' }
    });
    
    // 等待防抖搜索完成
    await new Promise(resolve => setTimeout(resolve, 350));
    
    const filteredArticles = knowledgeModule.filteredArticles;
    Assert.true(filteredArticles.length > 0, '应该有搜索结果');
    Assert.true(filteredArticles[0].title.includes('雏鹅'), '搜索结果应该包含关键词');
    
    // 4. 点击文章
    knowledgeModule.onArticleTap({
      currentTarget: { dataset: { id: 1 } }
    });
    
    Assert.equal(wx.navigateTo.callCount, 1, '应该跳转到文章详情页');
    Assert.true(wx.navigateTo.calls[0][0].url.includes('/pages/knowledge/detail'), '应该跳转到正确页面');
  });

  context.test('应该正确处理页面刷新流程', async () => {
    productionPage.moduleManager.initialize();
    
    // 模拟下拉刷新
    productionPage.data.refreshing = true;
    
    // 切换到健康模块并模拟刷新
    const healthModule = productionPage.moduleManager.switchModule('health');
    healthModule.refresh = Mock.fn().mockResolvedValue();
    
    await healthModule.refresh();
    
    Assert.equal(healthModule.refresh.callCount, 1, '应该调用刷新方法');
  });

  context.test('应该正确处理模块间数据共享', () => {
    productionPage.moduleManager.initialize();
    
    // 1. 在健康模块中生成报告数据
    const healthModule = productionPage.moduleManager.switchModule('health');
    const mockOverview = {
      totalGeese: 1000,
      healthyCount: 950,
      sickCount: 50
    };
    const mockTrendData = [
      { date: '01-15', healthy: 95.0, sick: 5.0 }
    ];
    
    healthModule.saveHealthReportDataForSharing(mockOverview, mockTrendData);
    
    // 2. 验证数据已保存到全局状态
    const app = getApp();
    Assert.true(typeof app.globalData.healthReportData === 'object', '健康数据应该保存到全局');
    Assert.deepEqual(app.globalData.healthReportData.overview, mockOverview, '概览数据应该正确');
    
    // 3. 其他模块应该能够访问这些数据
    const materialModule = productionPage.moduleManager.switchModule('material');
    Assert.true(typeof app.globalData.healthReportData === 'object', '物料模块应该能访问健康数据');
  });

  context.test('应该正确处理错误情况', async () => {
    productionPage.moduleManager.initialize();
    const healthModule = productionPage.moduleManager.switchModule('health');
    
    // 模拟网络错误
    healthModule.fetchHealthRecords = Mock.fn().mockRejectedValue(
      new Error('网络连接失败')
    );
    
    await healthModule.loadHealthRecords();
    
    Assert.equal(wx.showToast.callCount, 1, '应该显示错误提示');
    Assert.equal(productionPage.data.loading, false, '加载状态应该完成');
    Assert.true(wx.showToast.calls[0][0].title.includes('失败'), '应该显示失败信息');
  });

  context.test('应该正确清理资源', () => {
    productionPage.moduleManager.initialize();
    
    // 切换几个模块
    productionPage.moduleManager.switchModule('health');
    productionPage.moduleManager.switchModule('material');
    productionPage.moduleManager.switchModule('knowledge');
    
    // 销毁模块管理器
    productionPage.moduleManager.destroy();
    
    Assert.equal(productionPage.moduleManager.modules.size, 0, '所有模块应该被清理');
    Assert.equal(productionPage.moduleManager.currentModule, null, '当前模块应该为空');
    Assert.equal(productionPage.moduleManager.isInitialized, false, '初始化状态应该重置');
  });
});
