/**
 * 前端-云函数-数据库完整对接测试
 * Frontend-CloudFunction-Database Integration Test
 * 
 * 测试目标：
 * - 验证前端到云函数的完整调用链路
 * - 验证云函数到数据库的数据操作
 * - 验证多租户数据隔离在完整链路中的效果
 * - 验证错误处理和异常情况
 */

const { DataHelper } = require('../utils/data-scheduler');
const { smartApiCall } = require('../utils/api');

/**
 * 集成测试套件
 */
class IntegrationTestSuite {
  constructor() {
    this.testResults = [];
    this.passedTests = 0;
    this.failedTests = 0;
    this.mockData = this.initMockData();
  }

  /**
   * 初始化模拟数据
   */
  initMockData() {
    return {
      tenantA: {
        id: 'tenant_a',
        name: '测试租户A',
        admin: {
          _id: 'user_a_admin',
          tenant_id: 'tenant_a',
          role: 'admin',
          nickname: '租户A管理员',
          openid: 'openid_a_admin'
        },
        user: {
          _id: 'user_a_user',
          tenant_id: 'tenant_a',
          role: 'user',
          nickname: '租户A用户',
          openid: 'openid_a_user'
        }
      },
      tenantB: {
        id: 'tenant_b',
        name: '测试租户B',
        admin: {
          _id: 'user_b_admin',
          tenant_id: 'tenant_b',
          role: 'admin',
          nickname: '租户B管理员',
          openid: 'openid_b_admin'
        }
      }
    };
  }

  /**
   * 运行所有集成测试
   */
  async runAllTests() {
    console.log('🔗 开始前端-云函数-数据库集成测试...\n');

    // 基础功能测试
    await this.testBasicCRUDOperations();
    
    // 多租户隔离测试
    await this.testMultiTenantIsolation();
    
    // 权限控制测试
    await this.testPermissionControl();
    
    // 错误处理测试
    await this.testErrorHandling();
    
    // 性能测试
    await this.testPerformance();
    
    // API标准化测试
    await this.testAPIStandardization();

    this.printTestSummary();
  }

  /**
   * 测试基础CRUD操作
   */
  async testBasicCRUDOperations() {
    console.log('📝 测试基础CRUD操作...');

    let createdMaterialId = null;

    // 测试创建物料
    await this.runTest('创建物料', async () => {
      const materialData = {
        name: '测试饲料',
        material_type: 'feed',
        brand: '测试品牌',
        unit: 'kg',
        current_stock: 100,
        min_stock: 10,
        unit_price: 3.5
      };

      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'create',
        data: materialData
      }, this.mockData.tenantA.admin);

      this.assert(result.success, '创建操作应该成功');
      this.assert(result.data._id, '应该返回创建的记录ID');
      this.assert(result.data.tenant_id === 'tenant_a', '应该自动设置租户ID');
      
      createdMaterialId = result.data._id;
    });

    // 测试查询物料列表
    await this.runTest('查询物料列表', async () => {
      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'list',
        page: 1,
        limit: 20
      }, this.mockData.tenantA.admin);

      this.assert(result.success, '查询操作应该成功');
      this.assert(Array.isArray(result.data.list), '应该返回数组格式的列表');
      this.assert(result.data.pagination, '应该包含分页信息');
    });

    // 测试获取物料详情
    await this.runTest('获取物料详情', async () => {
      if (!createdMaterialId) {
        throw new Error('需要先创建物料');
      }

      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'detail',
        data: { id: createdMaterialId }
      }, this.mockData.tenantA.admin);

      this.assert(result.success, '查询详情应该成功');
      this.assert(result.data._id === createdMaterialId, '应该返回正确的物料详情');
      this.assert(result.data.name === '测试饲料', '物料名称应该正确');
    });

    // 测试更新物料
    await this.runTest('更新物料', async () => {
      if (!createdMaterialId) {
        throw new Error('需要先创建物料');
      }

      const updateData = {
        id: createdMaterialId,
        name: '更新后的测试饲料',
        unit_price: 4.0
      };

      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'update',
        data: updateData
      }, this.mockData.tenantA.admin);

      this.assert(result.success, '更新操作应该成功');
      this.assert(result.data.name === '更新后的测试饲料', '物料名称应该已更新');
      this.assert(result.data.unit_price === 4.0, '单价应该已更新');
    });

    // 测试库存操作
    await this.runTest('库存操作', async () => {
      if (!createdMaterialId) {
        throw new Error('需要先创建物料');
      }

      const stockData = {
        id: createdMaterialId,
        operation: 'in',
        quantity: 50,
        remark: '测试入库'
      };

      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'stock_operation',
        data: stockData
      }, this.mockData.tenantA.admin);

      this.assert(result.success, '库存操作应该成功');
      this.assert(result.data.newStock === 150, '库存应该正确更新');
    });

    // 测试删除物料
    await this.runTest('删除物料', async () => {
      if (!createdMaterialId) {
        throw new Error('需要先创建物料');
      }

      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'delete',
        data: { id: createdMaterialId }
      }, this.mockData.tenantA.admin);

      this.assert(result.success, '删除操作应该成功');
    });
  }

  /**
   * 测试多租户隔离
   */
  async testMultiTenantIsolation() {
    console.log('🏢 测试多租户隔离...');

    let tenantAMaterialId = null;
    let tenantBMaterialId = null;

    // 租户A创建物料
    await this.runTest('租户A创建物料', async () => {
      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'create',
        data: {
          name: '租户A的饲料',
          material_type: 'feed',
          unit: 'kg',
          current_stock: 100
        }
      }, this.mockData.tenantA.admin);

      this.assert(result.success, '租户A创建物料应该成功');
      this.assert(result.data.tenant_id === 'tenant_a', '物料应该属于租户A');
      tenantAMaterialId = result.data._id;
    });

    // 租户B创建物料
    await this.runTest('租户B创建物料', async () => {
      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'create',
        data: {
          name: '租户B的饲料',
          material_type: 'feed',
          unit: 'kg',
          current_stock: 80
        }
      }, this.mockData.tenantB.admin);

      this.assert(result.success, '租户B创建物料应该成功');
      this.assert(result.data.tenant_id === 'tenant_b', '物料应该属于租户B');
      tenantBMaterialId = result.data._id;
    });

    // 测试租户A无法访问租户B的数据
    await this.runTest('租户A无法访问租户B数据', async () => {
      if (!tenantBMaterialId) {
        throw new Error('需要先创建租户B的物料');
      }

      try {
        const result = await this.mockCloudFunctionCall('materialManagement', {
          action: 'detail',
          data: { id: tenantBMaterialId }
        }, this.mockData.tenantA.admin);

        // 应该返回"资源不存在"错误，而不是实际数据
        this.assert(!result.success, '跨租户访问应该失败');
        this.assert(result.code === 404, '应该返回404错误');
      } catch (error) {
        // 预期的错误
        this.assert(error.message.includes('不存在'), '应该提示资源不存在');
      }
    });

    // 测试租户A只能看到自己的数据
    await this.runTest('租户A只能看到自己的数据', async () => {
      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'list'
      }, this.mockData.tenantA.admin);

      this.assert(result.success, '查询应该成功');
      
      const tenantAMaterials = result.data.list.filter(item => item.tenant_id === 'tenant_a');
      const tenantBMaterials = result.data.list.filter(item => item.tenant_id === 'tenant_b');
      
      this.assert(tenantAMaterials.length > 0, '应该能看到租户A的数据');
      this.assert(tenantBMaterials.length === 0, '不应该看到租户B的数据');
    });
  }

  /**
   * 测试权限控制
   */
  async testPermissionControl() {
    console.log('🔐 测试权限控制...');

    // 测试管理员权限
    await this.runTest('管理员可以查看所有租户数据', async () => {
      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'list'
      }, this.mockData.tenantA.admin);

      this.assert(result.success, '管理员查询应该成功');
    });

    // 测试普通用户权限限制
    await this.runTest('普通用户权限限制', async () => {
      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'list'
      }, this.mockData.tenantA.user);

      this.assert(result.success, '普通用户查询应该成功');
      
      // 普通用户应该只能看到自己创建的数据
      const userMaterials = result.data.list.filter(item => item.user_id === 'user_a_user');
      this.assert(result.data.list.length === userMaterials.length, '普通用户只能看到自己的数据');
    });
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    console.log('❌ 测试错误处理...');

    // 测试参数验证错误
    await this.runTest('参数验证错误处理', async () => {
      try {
        await this.mockCloudFunctionCall('materialManagement', {
          action: 'create',
          data: {} // 缺少必要参数
        }, this.mockData.tenantA.admin);
        
        this.assert(false, '应该抛出参数验证错误');
      } catch (error) {
        this.assert(error.code === 400, '应该返回400错误码');
        this.assert(error.message.includes('缺少必要参数'), '错误信息应该明确');
      }
    });

    // 测试权限不足错误
    await this.runTest('权限不足错误处理', async () => {
      const noPermissionUser = {
        ...this.mockData.tenantA.user,
        role: 'viewer',
        permissions: []
      };

      try {
        await this.mockCloudFunctionCall('materialManagement', {
          action: 'create',
          data: {
            name: '测试物料',
            material_type: 'feed',
            unit: 'kg'
          }
        }, noPermissionUser);
        
        this.assert(false, '应该抛出权限不足错误');
      } catch (error) {
        this.assert(error.code === 403, '应该返回403错误码');
        this.assert(error.message.includes('权限不足'), '错误信息应该明确');
      }
    });

    // 测试资源不存在错误
    await this.runTest('资源不存在错误处理', async () => {
      try {
        await this.mockCloudFunctionCall('materialManagement', {
          action: 'detail',
          data: { id: 'non_existent_id' }
        }, this.mockData.tenantA.admin);
        
        this.assert(false, '应该抛出资源不存在错误');
      } catch (error) {
        this.assert(error.code === 404, '应该返回404错误码');
        this.assert(error.message.includes('不存在'), '错误信息应该明确');
      }
    });
  }

  /**
   * 测试性能
   */
  async testPerformance() {
    console.log('⚡ 测试性能...');

    await this.runTest('批量操作性能', async () => {
      const startTime = Date.now();
      const batchSize = 10;
      const promises = [];

      for (let i = 0; i < batchSize; i++) {
        promises.push(
          this.mockCloudFunctionCall('materialManagement', {
            action: 'create',
            data: {
              name: `性能测试物料${i}`,
              material_type: 'feed',
              unit: 'kg',
              current_stock: 100
            }
          }, this.mockData.tenantA.admin)
        );
      }

      const results = await Promise.all(promises);
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      const avgTime = totalTime / batchSize;

      this.assert(results.every(r => r.success), '所有批量操作都应该成功');
      this.assert(avgTime < 1000, `平均响应时间应该小于1秒，实际：${avgTime}ms`);
      
      console.log(`    批量创建${batchSize}个物料，总耗时：${totalTime}ms，平均：${avgTime}ms`);
    });
  }

  /**
   * 测试API标准化
   */
  async testAPIStandardization() {
    console.log('📋 测试API标准化...');

    await this.runTest('标准响应格式', async () => {
      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'statistics'
      }, this.mockData.tenantA.admin);

      // 检查标准响应格式
      this.assert(typeof result.success === 'boolean', '应该包含success字段');
      this.assert(typeof result.code === 'number', '应该包含code字段');
      this.assert(typeof result.message === 'string', '应该包含message字段');
      this.assert(result.timestamp, '应该包含timestamp字段');
      this.assert(result.version, '应该包含version字段');
      this.assert(result.requestId, '应该包含requestId字段');
      this.assert(typeof result.executionTime === 'number', '应该包含executionTime字段');
    });

    await this.runTest('分页响应格式', async () => {
      const result = await this.mockCloudFunctionCall('materialManagement', {
        action: 'list',
        page: 1,
        limit: 10
      }, this.mockData.tenantA.admin);

      this.assert(result.success, '查询应该成功');
      this.assert(Array.isArray(result.data.list), '应该包含list数组');
      this.assert(result.data.pagination, '应该包含pagination对象');
      this.assert(typeof result.data.pagination.page === 'number', '分页信息应该包含page');
      this.assert(typeof result.data.pagination.limit === 'number', '分页信息应该包含limit');
      this.assert(typeof result.data.pagination.total === 'number', '分页信息应该包含total');
      this.assert(typeof result.data.pagination.pages === 'number', '分页信息应该包含pages');
    });
  }

  /**
   * 模拟云函数调用
   */
  async mockCloudFunctionCall(functionName, event, user) {
    // 模拟API标准化处理器的行为
    const startTime = Date.now();
    
    try {
      // 模拟用户认证
      if (!user) {
        throw { code: 401, message: '用户未认证' };
      }

      // 模拟权限检查
      if (user.role === 'viewer' && ['create', 'update', 'delete'].includes(event.action)) {
        throw { code: 403, message: '权限不足' };
      }

      // 模拟业务逻辑处理
      const result = await this.mockBusinessLogic(functionName, event, user);
      
      // 返回标准格式响应
      return {
        success: true,
        code: 200,
        message: '操作成功',
        data: result,
        timestamp: new Date().toISOString(),
        version: 'v2',
        requestId: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        executionTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        code: error.code || 500,
        message: error.message || '系统内部错误',
        timestamp: new Date().toISOString(),
        version: 'v2',
        requestId: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * 模拟业务逻辑处理
   */
  async mockBusinessLogic(functionName, event, user) {
    const { action, data } = event;
    
    // 模拟数据存储
    if (!this.mockDatabase) {
      this.mockDatabase = {
        tenant_materials: [],
        material_stock_records: []
      };
    }

    switch (action) {
      case 'create':
        if (!data.name || !data.material_type || !data.unit) {
          throw { code: 400, message: '缺少必要参数：name, material_type, unit' };
        }
        
        const newMaterial = {
          _id: `material_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          ...data,
          tenant_id: user.tenant_id,
          user_id: user._id,
          created_at: new Date(),
          updated_at: new Date()
        };
        
        this.mockDatabase.tenant_materials.push(newMaterial);
        return newMaterial;

      case 'list':
        const materials = this.mockDatabase.tenant_materials.filter(item => {
          if (item.tenant_id !== user.tenant_id) return false;
          if (user.role === 'user' && item.user_id !== user._id) return false;
          return true;
        });
        
        return {
          list: materials,
          pagination: {
            page: event.page || 1,
            limit: event.limit || 20,
            total: materials.length,
            pages: Math.ceil(materials.length / (event.limit || 20)),
            hasNext: false,
            hasPrev: false
          }
        };

      case 'detail':
        if (!data.id) {
          throw { code: 400, message: '缺少必要参数：id' };
        }
        
        const material = this.mockDatabase.tenant_materials.find(item => 
          item._id === data.id && item.tenant_id === user.tenant_id
        );
        
        if (!material) {
          throw { code: 404, message: '物料不存在或无权限访问' };
        }
        
        return material;

      case 'statistics':
        const tenantMaterials = this.mockDatabase.tenant_materials.filter(item => 
          item.tenant_id === user.tenant_id
        );
        
        return {
          totalMaterials: tenantMaterials.length,
          lowStockCount: tenantMaterials.filter(item => 
            item.current_stock <= (item.min_stock || 0)
          ).length,
          totalValue: tenantMaterials.reduce((sum, item) => 
            sum + (item.current_stock || 0) * (item.unit_price || 0), 0
          )
        };

      default:
        throw { code: 400, message: '不支持的操作类型' };
    }
  }

  /**
   * 运行单个测试
   */
  async runTest(testName, testFunction) {
    try {
      await testFunction();
      this.passedTests++;
      console.log(`  ✅ ${testName}`);
      this.testResults.push({ name: testName, status: 'PASSED' });
    } catch (error) {
      this.failedTests++;
      console.log(`  ❌ ${testName}: ${error.message}`);
      this.testResults.push({ name: testName, status: 'FAILED', error: error.message });
    }
  }

  /**
   * 断言函数
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  /**
   * 打印测试总结
   */
  printTestSummary() {
    console.log('\n📊 集成测试总结:');
    console.log(`总测试数: ${this.passedTests + this.failedTests}`);
    console.log(`通过: ${this.passedTests}`);
    console.log(`失败: ${this.failedTests}`);
    console.log(`成功率: ${((this.passedTests / (this.passedTests + this.failedTests)) * 100).toFixed(2)}%`);
    
    if (this.failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(result => result.status === 'FAILED')
        .forEach(result => {
          console.log(`  - ${result.name}: ${result.error}`);
        });
    } else {
      console.log('\n🎉 所有集成测试都通过了！前端-云函数-数据库对接正常。');
    }
  }
}

// 导出测试套件
module.exports = {
  IntegrationTestSuite
};

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  (async () => {
    const testSuite = new IntegrationTestSuite();
    await testSuite.runAllTests();
  })();
}
