/**
 * 多租户数据隔离测试用例
 * Multi-Tenant Data Isolation Test Cases
 * 
 * 测试目标：
 * - 验证租户间数据严格隔离
 * - 验证用户权限控制
 * - 验证跨租户访问防护
 * - 验证数据安全规则
 */

const { DataIsolationMiddleware, DataIsolationQueryBuilder } = require('../utils/data-isolation');
const { apiHandler } = require('../utils/api-standard');

/**
 * 测试数据准备
 */
const TEST_DATA = {
  // 租户A的用户
  tenantA_admin: {
    _id: 'user_a_admin',
    tenant_id: 'tenant_a',
    role: 'admin',
    nickname: '租户A管理员',
    permissions: ['FLOCK_VIEW_ALL', 'FLOCK_EDIT']
  },
  tenantA_user: {
    _id: 'user_a_user',
    tenant_id: 'tenant_a',
    role: 'user',
    nickname: '租户A普通用户',
    permissions: ['FLOCK_VIEW_OWN']
  },
  
  // 租户B的用户
  tenantB_admin: {
    _id: 'user_b_admin',
    tenant_id: 'tenant_b',
    role: 'admin',
    nickname: '租户B管理员',
    permissions: ['FLOCK_VIEW_ALL', 'FLOCK_EDIT']
  },
  tenantB_user: {
    _id: 'user_b_user',
    tenant_id: 'tenant_b',
    role: 'user',
    nickname: '租户B普通用户',
    permissions: ['FLOCK_VIEW_OWN']
  },
  
  // 平台管理员
  platform_admin: {
    _id: 'platform_admin',
    tenant_id: null,
    role: 'platform_admin',
    nickname: '平台管理员',
    permissions: ['PLATFORM_TENANT_VIEW', 'FLOCK_VIEW_ALL']
  },
  
  // 测试数据
  flocks: [
    {
      _id: 'flock_a1',
      tenant_id: 'tenant_a',
      user_id: 'user_a_admin',
      name: '租户A鹅群1',
      breed: '白鹅',
      count: 100
    },
    {
      _id: 'flock_a2',
      tenant_id: 'tenant_a',
      user_id: 'user_a_user',
      name: '租户A鹅群2',
      breed: '灰鹅',
      count: 50
    },
    {
      _id: 'flock_b1',
      tenant_id: 'tenant_b',
      user_id: 'user_b_admin',
      name: '租户B鹅群1',
      breed: '白鹅',
      count: 80
    },
    {
      _id: 'flock_b2',
      tenant_id: 'tenant_b',
      user_id: 'user_b_user',
      name: '租户B鹅群2',
      breed: '狮头鹅',
      count: 30
    }
  ]
};

/**
 * 测试套件类
 */
class DataIsolationTestSuite {
  constructor() {
    this.testResults = [];
    this.passedTests = 0;
    this.failedTests = 0;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始多租户数据隔离测试...\n');

    // 基础隔离测试
    await this.testBasicTenantIsolation();
    await this.testUserLevelIsolation();
    await this.testCrossTenantAccessPrevention();
    
    // 权限控制测试
    await this.testPermissionBasedAccess();
    await this.testRoleBasedAccess();
    
    // 平台级数据访问测试
    await this.testPlatformDataAccess();
    
    // 数据写入安全测试
    await this.testDataWriteSecurity();
    
    // 查询构建器测试
    await this.testQueryBuilderSecurity();

    this.printTestSummary();
  }

  /**
   * 测试基础租户隔离
   */
  async testBasicTenantIsolation() {
    console.log('📋 测试基础租户隔离...');

    // 测试租户A管理员只能看到租户A的数据
    await this.runTest('租户A管理员查询鹅群', async () => {
      const builder = new DataIsolationQueryBuilder(TEST_DATA.tenantA_admin, 'flocks');
      const query = builder.buildReadQuery({});
      
      // 验证查询条件包含正确的tenant_id
      this.assert(query.tenant_id === 'tenant_a', '查询条件应包含租户A的ID');
      
      // 模拟查询结果过滤
      const filteredFlocks = TEST_DATA.flocks.filter(flock => 
        flock.tenant_id === query.tenant_id
      );
      
      this.assert(filteredFlocks.length === 2, '租户A应该有2个鹅群');
      this.assert(filteredFlocks.every(f => f.tenant_id === 'tenant_a'), '所有结果都应属于租户A');
    });

    // 测试租户B管理员只能看到租户B的数据
    await this.runTest('租户B管理员查询鹅群', async () => {
      const builder = new DataIsolationQueryBuilder(TEST_DATA.tenantB_admin, 'flocks');
      const query = builder.buildReadQuery({});
      
      this.assert(query.tenant_id === 'tenant_b', '查询条件应包含租户B的ID');
      
      const filteredFlocks = TEST_DATA.flocks.filter(flock => 
        flock.tenant_id === query.tenant_id
      );
      
      this.assert(filteredFlocks.length === 2, '租户B应该有2个鹅群');
      this.assert(filteredFlocks.every(f => f.tenant_id === 'tenant_b'), '所有结果都应属于租户B');
    });
  }

  /**
   * 测试用户级隔离
   */
  async testUserLevelIsolation() {
    console.log('👤 测试用户级隔离...');

    // 测试普通用户只能看到自己的数据
    await this.runTest('租户A普通用户查询鹅群', async () => {
      const builder = new DataIsolationQueryBuilder(TEST_DATA.tenantA_user, 'flocks');
      const query = builder.buildReadQuery({});
      
      // 普通用户应该同时有tenant_id和user_id限制
      this.assert(query.tenant_id === 'tenant_a', '查询条件应包含租户A的ID');
      this.assert(query.user_id === 'user_a_user', '查询条件应包含用户ID');
      
      const filteredFlocks = TEST_DATA.flocks.filter(flock => 
        flock.tenant_id === query.tenant_id && flock.user_id === query.user_id
      );
      
      this.assert(filteredFlocks.length === 1, '普通用户应该只能看到自己的1个鹅群');
      this.assert(filteredFlocks[0].name === '租户A鹅群2', '应该是用户自己创建的鹅群');
    });
  }

  /**
   * 测试跨租户访问防护
   */
  async testCrossTenantAccessPrevention() {
    console.log('🚫 测试跨租户访问防护...');

    await this.runTest('防止跨租户数据访问', async () => {
      // 租户A用户尝试访问租户B的数据
      const builder = new DataIsolationQueryBuilder(TEST_DATA.tenantA_admin, 'flocks');
      const query = builder.buildReadQuery({ _id: 'flock_b1' }); // 尝试访问租户B的鹅群
      
      // 查询条件应该自动添加租户限制
      this.assert(query.tenant_id === 'tenant_a', '查询条件应强制限制为租户A');
      this.assert(query._id === 'flock_b1', '原始查询条件应保留');
      
      // 模拟数据库查询结果：由于tenant_id不匹配，应该返回空结果
      const filteredFlocks = TEST_DATA.flocks.filter(flock => 
        flock.tenant_id === query.tenant_id && flock._id === query._id
      );
      
      this.assert(filteredFlocks.length === 0, '跨租户访问应该返回空结果');
    });
  }

  /**
   * 测试基于权限的访问控制
   */
  async testPermissionBasedAccess() {
    console.log('🔐 测试基于权限的访问控制...');

    await this.runTest('权限不足时拒绝访问', async () => {
      try {
        // 创建一个没有查看权限的用户
        const noPermissionUser = {
          ...TEST_DATA.tenantA_user,
          permissions: [] // 没有任何权限
        };
        
        const builder = new DataIsolationQueryBuilder(noPermissionUser, 'flocks');
        
        // 应该抛出权限不足的错误
        let errorThrown = false;
        try {
          builder.buildReadQuery({});
        } catch (error) {
          errorThrown = true;
          this.assert(error.message.includes('权限不足'), '应该抛出权限不足错误');
        }
        
        this.assert(errorThrown, '应该抛出权限错误');
      } catch (error) {
        throw error;
      }
    });
  }

  /**
   * 测试基于角色的访问控制
   */
  async testRoleBasedAccess() {
    console.log('👑 测试基于角色的访问控制...');

    await this.runTest('管理员可以查看所有租户数据', async () => {
      const builder = new DataIsolationQueryBuilder(TEST_DATA.tenantA_admin, 'flocks');
      const query = builder.buildReadQuery({});
      
      // 管理员应该可以查看租户内所有数据，不限制user_id
      this.assert(query.tenant_id === 'tenant_a', '查询条件应包含租户ID');
      this.assert(!query.user_id, '管理员查询不应限制user_id');
    });

    await this.runTest('普通用户只能查看自己的数据', async () => {
      const builder = new DataIsolationQueryBuilder(TEST_DATA.tenantA_user, 'flocks');
      const query = builder.buildReadQuery({});
      
      // 普通用户应该同时受到tenant_id和user_id限制
      this.assert(query.tenant_id === 'tenant_a', '查询条件应包含租户ID');
      this.assert(query.user_id === 'user_a_user', '普通用户查询应限制user_id');
    });
  }

  /**
   * 测试平台级数据访问
   */
  async testPlatformDataAccess() {
    console.log('🌐 测试平台级数据访问...');

    await this.runTest('平台管理员可以访问平台级数据', async () => {
      const builder = new DataIsolationQueryBuilder(TEST_DATA.platform_admin, 'goose_prices');
      const query = builder.buildReadQuery({});
      
      // 平台级数据查询不应该有租户限制
      this.assert(!query.tenant_id, '平台级数据查询不应有租户限制');
    });

    await this.runTest('普通用户可以读取公开的平台数据', async () => {
      const builder = new DataIsolationQueryBuilder(TEST_DATA.tenantA_user, 'goose_prices');
      const query = builder.buildReadQuery({});
      
      // 普通用户访问平台数据应该有可见性限制
      this.assert(query.status === 'published', '应该限制为已发布状态');
      this.assert(query.visibility === 'public', '应该限制为公开可见');
    });
  }

  /**
   * 测试数据写入安全
   */
  async testDataWriteSecurity() {
    console.log('✍️ 测试数据写入安全...');

    await this.runTest('租户数据写入自动添加租户ID', async () => {
      const testData = {
        name: '新鹅群',
        breed: '白鹅',
        count: 100
      };
      
      const validatedData = DataIsolationMiddleware.validateWriteData(
        TEST_DATA.tenantA_admin, 
        'flocks', 
        testData
      );
      
      this.assert(validatedData.tenant_id === 'tenant_a', '应该自动添加租户ID');
      this.assert(validatedData.name === '新鹅群', '原始数据应该保留');
    });

    await this.runTest('防止跨租户数据写入', async () => {
      const maliciousData = {
        name: '恶意鹅群',
        tenant_id: 'tenant_b', // 尝试写入到其他租户
        count: 100
      };
      
      const validatedData = DataIsolationMiddleware.validateWriteData(
        TEST_DATA.tenantA_admin, 
        'flocks', 
        maliciousData
      );
      
      // 应该强制覆盖为正确的租户ID
      this.assert(validatedData.tenant_id === 'tenant_a', '应该强制使用正确的租户ID');
    });
  }

  /**
   * 测试查询构建器安全性
   */
  async testQueryBuilderSecurity() {
    console.log('🔍 测试查询构建器安全性...');

    await this.runTest('查询构建器自动注入安全条件', async () => {
      const builder = new DataIsolationQueryBuilder(TEST_DATA.tenantA_admin, 'flocks');
      const originalQuery = { breed: '白鹅' };
      const secureQuery = builder.buildReadQuery(originalQuery);
      
      // 应该保留原始查询条件
      this.assert(secureQuery.breed === '白鹅', '应该保留原始查询条件');
      
      // 应该自动注入安全条件
      this.assert(secureQuery.tenant_id === 'tenant_a', '应该自动注入租户ID');
    });

    await this.runTest('复杂查询条件的安全处理', async () => {
      const builder = new DataIsolationQueryBuilder(TEST_DATA.tenantA_user, 'flocks');
      const complexQuery = {
        $or: [
          { breed: '白鹅' },
          { breed: '灰鹅' }
        ],
        count: { $gte: 50 }
      };
      
      const secureQuery = builder.buildReadQuery(complexQuery);
      
      // 复杂查询条件应该保留
      this.assert(secureQuery.$or, '复杂查询条件应该保留');
      this.assert(secureQuery.count, '数值查询条件应该保留');
      
      // 安全条件应该正确注入
      this.assert(secureQuery.tenant_id === 'tenant_a', '应该注入租户ID');
      this.assert(secureQuery.user_id === 'user_a_user', '应该注入用户ID');
    });
  }

  /**
   * 运行单个测试
   */
  async runTest(testName, testFunction) {
    try {
      await testFunction();
      this.passedTests++;
      console.log(`  ✅ ${testName}`);
      this.testResults.push({ name: testName, status: 'PASSED' });
    } catch (error) {
      this.failedTests++;
      console.log(`  ❌ ${testName}: ${error.message}`);
      this.testResults.push({ name: testName, status: 'FAILED', error: error.message });
    }
  }

  /**
   * 断言函数
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  /**
   * 打印测试总结
   */
  printTestSummary() {
    console.log('\n📊 测试总结:');
    console.log(`总测试数: ${this.passedTests + this.failedTests}`);
    console.log(`通过: ${this.passedTests}`);
    console.log(`失败: ${this.failedTests}`);
    console.log(`成功率: ${((this.passedTests / (this.passedTests + this.failedTests)) * 100).toFixed(2)}%`);
    
    if (this.failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(result => result.status === 'FAILED')
        .forEach(result => {
          console.log(`  - ${result.name}: ${result.error}`);
        });
    } else {
      console.log('\n🎉 所有测试都通过了！多租户数据隔离机制工作正常。');
    }
  }
}

/**
 * 集成测试：模拟真实API调用
 */
async function runIntegrationTest() {
  console.log('\n🔗 运行集成测试...');
  
  // 模拟云函数调用
  const mockCloudFunction = apiHandler(async (params, context) => {
    const { action, data } = params;
    const { user, secureQuery } = context;
    
    if (action === 'list') {
      // 使用安全查询
      const query = secureQuery('flocks', data.filters || {});
      
      // 模拟数据库查询
      const results = TEST_DATA.flocks.filter(flock => {
        if (query.tenant_id && flock.tenant_id !== query.tenant_id) return false;
        if (query.user_id && flock.user_id !== query.user_id) return false;
        return true;
      });
      
      return { list: results, total: results.length };
    }
    
    throw new Error('不支持的操作');
  });
  
  // 测试租户A管理员调用
  try {
    const event = {
      action: 'list',
      data: { filters: {} }
    };
    const context = { OPENID: 'openid_a_admin' };
    
    // 模拟用户信息获取
    const originalGetUserInfo = require('../utils/auth').getCurrentUserInfo;
    require('../utils/auth').getCurrentUserInfo = () => Promise.resolve(TEST_DATA.tenantA_admin);
    
    const result = await mockCloudFunction(event, context);
    
    console.log('  ✅ 租户A管理员API调用测试通过');
    console.log(`     返回${result.data.list.length}条记录，都属于租户A`);
    
    // 恢复原函数
    require('../utils/auth').getCurrentUserInfo = originalGetUserInfo;
    
  } catch (error) {
    console.log(`  ❌ 集成测试失败: ${error.message}`);
  }
}

/**
 * 性能测试
 */
async function runPerformanceTest() {
  console.log('\n⚡ 运行性能测试...');
  
  const iterations = 1000;
  const startTime = Date.now();
  
  for (let i = 0; i < iterations; i++) {
    const builder = new DataIsolationQueryBuilder(TEST_DATA.tenantA_admin, 'flocks');
    builder.buildReadQuery({ breed: '白鹅' });
  }
  
  const endTime = Date.now();
  const avgTime = (endTime - startTime) / iterations;
  
  console.log(`  ✅ 查询构建器性能测试完成`);
  console.log(`     ${iterations}次调用，平均耗时: ${avgTime.toFixed(2)}ms`);
  
  if (avgTime < 1) {
    console.log('     🚀 性能表现优秀！');
  } else if (avgTime < 5) {
    console.log('     👍 性能表现良好');
  } else {
    console.log('     ⚠️  性能需要优化');
  }
}

// 导出测试套件
module.exports = {
  DataIsolationTestSuite,
  runIntegrationTest,
  runPerformanceTest,
  TEST_DATA
};

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  (async () => {
    const testSuite = new DataIsolationTestSuite();
    await testSuite.runAllTests();
    await runIntegrationTest();
    await runPerformanceTest();
  })();
}
