/**
 * 缓存管理器测试套件
 * Cache Manager Test Suite
 */

const { suite, Assert, Mock } = require('../framework/test-runner.js');

// 模拟微信小程序存储API
global.wx = {
  getStorageSync: Mock.fn(),
  setStorageSync: Mock.fn(),
  removeStorageSync: Mock.fn(),
  getStorageInfoSync: Mock.fn(() => ({
    keys: [],
    currentSize: 0,
    limitSize: 10240
  }))
};

suite('缓存管理器测试', (context) => {
  let AdvancedCacheManager;
  let cacheManager;

  context.beforeAll(() => {
    // 导入缓存管理器
    const cacheModule = require('../../utils/performance/advanced-cache-manager.js');
    AdvancedCacheManager = cacheModule.AdvancedCacheManager;
  });

  context.beforeEach(() => {
    // 创建新的缓存管理器实例
    cacheManager = new AdvancedCacheManager();
    
    // 重置mock
    wx.getStorageSync.calls = [];
    wx.setStorageSync.calls = [];
    wx.removeStorageSync.calls = [];
    wx.getStorageInfoSync.calls = [];
  });

  context.test('应该正确初始化缓存管理器', () => {
    Assert.true(cacheManager instanceof AdvancedCacheManager, '应该是AdvancedCacheManager实例');
    Assert.true(cacheManager.memoryCache instanceof Map, '内存缓存应该是Map');
    Assert.true(cacheManager.storageCache instanceof Map, '存储缓存应该是Map');
    Assert.true(typeof cacheManager.cacheStats === 'object', '缓存统计应该是对象');
    Assert.equal(cacheManager.maxMemorySize, 50, '最大内存缓存大小应该是50');
    Assert.equal(cacheManager.defaultTTL, 5 * 60 * 1000, '默认TTL应该是5分钟');
  });

  context.test('应该正确处理内存缓存命中', async () => {
    const key = 'test-key';
    const value = { data: 'test-value' };
    
    // 设置内存缓存
    cacheManager.setToMemory(key, value, 60000, 'normal');
    
    const fetchFunction = Mock.fn().mockResolvedValue('should-not-be-called');
    
    const result = await cacheManager.get(key, fetchFunction);
    
    Assert.deepEqual(result, value, '应该返回缓存的值');
    Assert.equal(fetchFunction.callCount, 0, '不应该调用获取函数');
    Assert.equal(cacheManager.cacheStats.hits, 1, '命中次数应该增加');
    Assert.equal(cacheManager.cacheStats.misses, 0, '未命中次数应该为0');
  });

  context.test('应该正确处理缓存未命中', async () => {
    const key = 'non-existent-key';
    const expectedValue = { data: 'fetched-value' };
    
    const fetchFunction = Mock.fn().mockResolvedValue(expectedValue);
    
    const result = await cacheManager.get(key, fetchFunction);
    
    Assert.deepEqual(result, expectedValue, '应该返回获取的值');
    Assert.equal(fetchFunction.callCount, 1, '应该调用获取函数');
    Assert.equal(cacheManager.cacheStats.hits, 0, '命中次数应该为0');
    Assert.equal(cacheManager.cacheStats.misses, 1, '未命中次数应该增加');
  });

  context.test('应该正确处理存储缓存', async () => {
    const key = 'storage-key';
    const value = { data: 'storage-value' };
    
    // 模拟存储缓存存在
    wx.getStorageSync.mockReturnValue({
      data: value,
      expireTime: Date.now() + 60000
    });
    
    const fetchFunction = Mock.fn().mockResolvedValue('should-not-be-called');
    
    const result = await cacheManager.get(key, fetchFunction, {
      useMemory: false,
      useStorage: true
    });
    
    Assert.deepEqual(result, value, '应该返回存储缓存的值');
    Assert.equal(fetchFunction.callCount, 0, '不应该调用获取函数');
    Assert.equal(wx.getStorageSync.callCount, 1, '应该调用getStorageSync');
  });

  context.test('应该正确处理过期的缓存', async () => {
    const key = 'expired-key';
    const expiredValue = { data: 'expired-value' };
    const freshValue = { data: 'fresh-value' };
    
    // 设置过期的内存缓存
    cacheManager.memoryCache.set(key, {
      data: expiredValue,
      expireTime: Date.now() - 1000, // 已过期
      lastAccess: Date.now() - 1000,
      accessCount: 1,
      priority: 'normal'
    });
    
    const fetchFunction = Mock.fn().mockResolvedValue(freshValue);
    
    const result = await cacheManager.get(key, fetchFunction);
    
    Assert.deepEqual(result, freshValue, '应该返回新获取的值');
    Assert.equal(fetchFunction.callCount, 1, '应该调用获取函数');
    Assert.false(cacheManager.memoryCache.has(key), '过期缓存应该被删除');
  });

  context.test('应该正确执行缓存淘汰策略', () => {
    // 填满内存缓存
    for (let i = 0; i < cacheManager.maxMemorySize + 5; i++) {
      cacheManager.setToMemory(`key-${i}`, { data: i }, 60000, 'normal');
    }
    
    Assert.true(cacheManager.memoryCache.size <= cacheManager.maxMemorySize, 
      '内存缓存大小应该不超过最大限制');
  });

  context.test('应该正确处理优先级缓存', () => {
    const lowPriorityKey = 'low-priority';
    const highPriorityKey = 'high-priority';
    
    cacheManager.setToMemory(lowPriorityKey, { data: 'low' }, 60000, 'low');
    cacheManager.setToMemory(highPriorityKey, { data: 'high' }, 60000, 'high');
    
    // 填满缓存触发淘汰
    for (let i = 0; i < cacheManager.maxMemorySize; i++) {
      cacheManager.setToMemory(`filler-${i}`, { data: i }, 60000, 'normal');
    }
    
    // 高优先级缓存应该被保留更久
    const lowExists = cacheManager.memoryCache.has(lowPriorityKey);
    const highExists = cacheManager.memoryCache.has(highPriorityKey);
    
    if (!lowExists && !highExists) {
      // 如果都被淘汰了，这也是可能的，但高优先级应该最后被淘汰
      Assert.true(true, '缓存淘汰策略正常工作');
    } else {
      Assert.true(highExists || !lowExists, '高优先级缓存应该比低优先级缓存保留更久');
    }
  });

  context.test('应该正确预热缓存', async () => {
    const cacheConfigs = [
      {
        key: 'config1',
        fetchFunction: Mock.fn().mockResolvedValue({ data: 'config1' }),
        ttl: 60000
      },
      {
        key: 'config2',
        fetchFunction: Mock.fn().mockResolvedValue({ data: 'config2' }),
        ttl: 60000
      }
    ];
    
    await cacheManager.warmup(cacheConfigs);
    
    Assert.equal(cacheConfigs[0].fetchFunction.callCount, 1, '第一个配置应该被调用');
    Assert.equal(cacheConfigs[1].fetchFunction.callCount, 1, '第二个配置应该被调用');
    Assert.true(cacheManager.memoryCache.has('config1'), '第一个配置应该被缓存');
    Assert.true(cacheManager.memoryCache.has('config2'), '第二个配置应该被缓存');
  });

  context.test('应该正确批量预取', async () => {
    const keys = ['prefetch1', 'prefetch2', 'prefetch3'];
    const fetchFunctions = [
      Mock.fn().mockResolvedValue({ data: 'prefetch1' }),
      Mock.fn().mockResolvedValue({ data: 'prefetch2' }),
      Mock.fn().mockRejectedValue(new Error('prefetch3 failed'))
    ];
    
    const results = await cacheManager.prefetch(keys, fetchFunctions);
    
    Assert.equal(results.length, 3, '应该返回3个结果');
    Assert.equal(results[0].status, 'fulfilled', '第一个预取应该成功');
    Assert.equal(results[1].status, 'fulfilled', '第二个预取应该成功');
    Assert.equal(results[2].status, 'rejected', '第三个预取应该失败');
  });

  context.test('应该正确清理过期缓存', () => {
    const now = Date.now();
    
    // 添加有效和过期的缓存
    cacheManager.memoryCache.set('valid', {
      data: 'valid-data',
      expireTime: now + 60000
    });
    cacheManager.memoryCache.set('expired', {
      data: 'expired-data',
      expireTime: now - 1000
    });
    
    // 模拟存储缓存
    wx.getStorageInfoSync.mockReturnValue({
      keys: ['cache_valid_storage', 'cache_expired_storage']
    });
    
    wx.getStorageSync.mockImplementation((key) => {
      if (key === 'cache_valid_storage') {
        return { data: 'valid', expireTime: now + 60000 };
      }
      if (key === 'cache_expired_storage') {
        return { data: 'expired', expireTime: now - 1000 };
      }
      return null;
    });
    
    cacheManager.cleanup();
    
    Assert.true(cacheManager.memoryCache.has('valid'), '有效缓存应该保留');
    Assert.false(cacheManager.memoryCache.has('expired'), '过期缓存应该被删除');
    Assert.equal(wx.removeStorageSync.callCount, 1, '应该删除过期的存储缓存');
  });

  context.test('应该正确获取缓存统计', () => {
    // 模拟一些缓存操作
    cacheManager.cacheStats.hits = 10;
    cacheManager.cacheStats.misses = 5;
    cacheManager.cacheStats.totalRequests = 15;
    
    const stats = cacheManager.getStats();
    
    Assert.equal(stats.hits, 10, '命中次数应该正确');
    Assert.equal(stats.misses, 5, '未命中次数应该正确');
    Assert.equal(stats.totalRequests, 15, '总请求次数应该正确');
    Assert.equal(stats.hitRate, '66.67%', '命中率应该正确计算');
    Assert.true(typeof stats.memorySize === 'number', '内存缓存大小应该是数字');
    Assert.true(typeof stats.storageSize === 'number', '存储缓存大小应该是数字');
  });

  context.test('应该正确清空所有缓存', () => {
    // 添加一些缓存
    cacheManager.memoryCache.set('test1', { data: 'test1' });
    cacheManager.storageCache.set('test2', true);
    cacheManager.cacheStats.hits = 10;
    
    // 模拟存储缓存
    wx.getStorageInfoSync.mockReturnValue({
      keys: ['cache_test1', 'cache_test2', 'other_key']
    });
    
    cacheManager.clear();
    
    Assert.equal(cacheManager.memoryCache.size, 0, '内存缓存应该被清空');
    Assert.equal(cacheManager.storageCache.size, 0, '存储缓存应该被清空');
    Assert.equal(cacheManager.cacheStats.hits, 0, '统计应该被重置');
    Assert.equal(wx.removeStorageSync.callCount, 2, '应该删除所有缓存相关的存储');
  });

  context.test('应该正确删除特定缓存', () => {
    const key = 'test-delete';
    
    cacheManager.memoryCache.set(key, { data: 'test' });
    cacheManager.storageCache.set(key, true);
    
    cacheManager.delete(key);
    
    Assert.false(cacheManager.memoryCache.has(key), '内存缓存应该被删除');
    Assert.false(cacheManager.storageCache.has(key), '存储缓存记录应该被删除');
    Assert.equal(wx.removeStorageSync.callCount, 1, '应该删除存储缓存');
    Assert.equal(wx.removeStorageSync.calls[0][0], `cache_${key}`, '应该删除正确的存储键');
  });
});
