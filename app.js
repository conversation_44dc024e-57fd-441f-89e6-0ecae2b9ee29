// 引入统一Loading管理器
const unifiedLoading = require('./utils/unified-loading.js');
const formatUtils = require('./utils/format.js');

App({
  onLaunch: function () {
    // 初始化云开发
    this.initCloudEnvironment();

    // 初始化应用
    try { const logger = require('./utils/logger.js'); logger.debug && logger.debug('智慧养鹅云开发小程序启动'); } catch(_) {}

    // 初始化全局统一Loading管理器
    this.globalData.unifiedLoading = unifiedLoading;

    // 初始化格式化工具到全局数据
    this.globalData.formatUtils = formatUtils;

    // 处理SharedArrayBuffer警告 - 仅在开发环境
    this.handleDeprecationWarnings();

    // 初始化环境配置
    this.initializeEnvironmentConfig();

    // 缓存租户代码到本地，供日志与请求兜底使用
    try {
      const tenantConfig = require('./utils/tenant-config.js');
    } catch (error) {
      // 静默处理租户配置加载失败
    }

    // 完全禁用实时上报功能以避免开发环境警告和渲染层错误
    // 直接提供一个静默的空实现，不调用原生API
    wx.reportRealtimeAction = function(...args) {
      // 静默忽略所有调用，不产生任何错误或警告
      return Promise.resolve();
    };

    // 延迟检查登录状态，确保应用完全初始化
    setTimeout(() => {
      const token = wx.getStorageSync('access_token'); // 统一使用access_token
      if (token) {
        // 验证token有效性
        this.validateToken();
      } else {
        // 如果没有token，初始化默认测试用户
        this.initializeDefaultTestUser();
      }
    }, 100);
  },

  // 初始化云开发环境
  initCloudEnvironment: function() {
    try {
      // 初始化云开发
      if (!wx.cloud) {
        console.error('请使用 2.2.3 或以上的基础库以使用云能力');
        return;
      }

      wx.cloud.init({
        env: 'your-env-id', // 云开发环境ID，需要替换为实际的环境ID
        traceUser: true
      });

      // 设置全局云开发实例
      this.globalData.cloud = wx.cloud;
      this.globalData.db = wx.cloud.database();

      console.log('云开发环境初始化成功');

      // 初始化云开发相关的全局数据
      this.globalData.isCloudEnabled = true;
      this.globalData.cloudEnv = 'your-env-id';

    } catch (error) {
      console.error('云开发环境初始化失败:', error);
      this.globalData.isCloudEnabled = false;
    }
  },

  // 验证token有效性
  validateToken: function() {
    try {
      const { auth } = require('./utils/api.js');
      
      // 验证token有效性
      auth.getUserInfo().then((userInfo) => {
        if (userInfo && userInfo.id) {
          this.globalData.userInfo = userInfo;
          // 同时保存到本地存储，统一使用user_info
          wx.setStorageSync('user_info', userInfo);
          try { const logger = require('./utils/logger.js'); logger.debug && logger.debug('[App] 验证token成功，用户信息已更新', userInfo); } catch(_) {}
        } else {
          // token无效，清除本地存储
          wx.removeStorageSync('access_token'); // 统一使用access_token
          wx.removeStorageSync('refresh_token');
          wx.removeStorageSync('user_info');
          this.globalData.userInfo = null;
          try { const logger = require('./utils/logger.js'); logger.warn && logger.warn('[App] token无效，已清除本地存储'); } catch(_) {}
        }
      }).catch((err) => {
        try { const logger = require('./utils/logger.js'); logger.warn && logger.warn('验证token失败，可能是网络问题或服务器未启动', err); } catch(_) {}
        // 尝试从本地存储获取用户信息
        const localUserInfo = wx.getStorageSync('user_info'); // 统一使用user_info
        if (localUserInfo) {
          this.globalData.userInfo = localUserInfo;
        }
      });
    } catch (error) {
      try { const logger = require('./utils/logger.js'); logger.error && logger.error('validateToken方法执行失败', error); } catch(_) {}
    }
  },

  // 初始化默认测试用户
  initializeDefaultTestUser: function() {
    try {
      console.log('[App] 初始化默认测试用户');
      const { initializeDefaultUser } = require('./utils/mock-users.js');
      initializeDefaultUser();
    } catch (error) {
      console.warn('[App] 初始化默认用户失败:', error);
    }
  },

  // 初始化环境配置
  initializeEnvironmentConfig: function() {
    try {
      // 引入环境配置管理器
      const { environmentConfig } = require('./utils/environment-config.js');
      
      // 设置全局API配置
      this.globalData.baseUrl = environmentConfig.getApiBaseUrl();
      this.globalData.apiBaseUrl = environmentConfig.getApiBaseUrl();
      this.globalData.apiBase = environmentConfig.getApiBaseUrl() + '/api/v1'; // 添加缺失的apiBase
      this.globalData.environment = environmentConfig.getEnvironment();
      this.globalData.environmentName = environmentConfig.getEnvironmentName();
      
      // 存储环境配置管理器实例
      this.globalData.environmentConfig = environmentConfig;
      
      // 输出环境信息
      try { const logger = require('./utils/logger.js'); logger.debug && logger.debug('环境配置初始化完成', {
        environment: environmentConfig.getEnvironment(),
        environmentName: environmentConfig.getEnvironmentName(),
        baseUrl: this.globalData.baseUrl,
        apiBaseUrl: this.globalData.apiBaseUrl,
        apiBase: this.globalData.apiBase
      }); } catch(_) {}
      
      // 生成域名配置指南（开发环境）
      if (environmentConfig.isDevelopment()) {
        try { const logger = require('./utils/logger.js'); logger.debug && logger.debug('微信小程序域名配置指南'); logger.debug && logger.debug(environmentConfig.generateWechatDomainConfigGuide()); } catch(_) {}
      }
      
    } catch (error) {
      try { const logger = require('./utils/logger.js'); logger.error && logger.error('环境配置初始化失败', error); } catch(_) {}
      // 设置默认配置
      this.globalData.baseUrl = 'http://localhost:3001/api/v1';
      this.globalData.apiBaseUrl = 'http://localhost:3001';
      this.globalData.apiBase = 'http://localhost:3001/api/v1'; // 添加默认的apiBase
      this.globalData.environment = 'development';
      this.globalData.environmentName = '开发环境';
    }
  },

  // 处理开发环境弃用警告
  handleDeprecationWarnings: function() {
    // 处理SharedArrayBuffer弃用警告
    if (typeof SharedArrayBuffer !== 'undefined') {
      try {
        // 捕获并静默处理SharedArrayBuffer相关警告
        const originalConsoleWarn = console.warn;
        console.warn = function(...args) {
          const message = args.join(' ');
          // 过滤SharedArrayBuffer警告（仅在开发环境）
          if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) {
            if (message.includes('SharedArrayBuffer') || 
                message.includes('cross-origin isolation')) {
              return; // 静默忽略这些警告
            }
          }
          originalConsoleWarn.apply(console, args);
        };
      } catch (error) {
        // 如果无法处理，静默忽略
      }
    }
  },


  
  // 检查权限的方法
  checkPermission(requiredRoles) {
    const userInfo = wx.getStorageSync('user_info') || this.globalData.userInfo;
    if (!Array.isArray(requiredRoles) || requiredRoles.length === 0) return true;
    return requiredRoles.includes(userInfo?.roleCode || this.globalData.userRole);
  },

  // 统一的登录状态检查方法
  isLoggedIn() {
    const token = wx.getStorageSync('access_token');
    const userInfo = wx.getStorageSync('user_info');
    return !!(token && userInfo);
  },

  // 统一的登录状态清除方法
  clearLoginState() {
    wx.removeStorageSync('access_token');
    wx.removeStorageSync('refresh_token');
    wx.removeStorageSync('user_info');
    this.globalData.userInfo = {};
    this.globalData.token = '';
  },

  // 统一的登录跳转方法
  redirectToLogin() {
    wx.reLaunch({
      url: '/pages/login/login'
    });
  },

  // 全局数据
  globalData: {
    // API配置 - 使用环境配置管理器自动切换
    baseUrl: '', // 将由环境配置管理器动态设置
    apiBaseUrl: '', // 将由环境配置管理器动态设置
    apiBase: '', // API基础路径，包含版本号

    // 用户信息
    userInfo: {},
    token: '',
    userRole: 'user', // 默认角色

    // 应用配置
    appVersion: '1.0.0',
    env: 'development', // development | production

    // AI配置相关
    aiConfig: null, // 从后台获取的AI配置
    aiConfigLastUpdate: null, // AI配置最后更新时间

    // 其他全局数据
    selectedTenant: null,
    api: {},
    loadingManager: null
  }
});