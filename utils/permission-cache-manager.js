/**
 * 权限缓存管理器
 * Permission Cache Manager
 * 
 * 实现多层权限缓存策略，大幅提升权限检查性能
 */

class PermissionCacheManager {
  constructor() {
    // 内存缓存 - 最快访问
    this.memoryCache = new Map();
    
    // 本地存储缓存键前缀
    this.localCachePrefix = 'perm_cache_';
    
    // 缓存配置
    this.config = {
      // 内存缓存过期时间（5分钟）
      memoryTTL: 5 * 60 * 1000,
      
      // 本地存储缓存过期时间（30分钟）
      localTTL: 30 * 60 * 1000,
      
      // 最大内存缓存条目数
      maxMemoryEntries: 100,
      
      // 权限检查结果缓存时间（2分钟）
      permissionCheckTTL: 2 * 60 * 1000
    };
    
    // 缓存统计
    this.stats = {
      memoryHits: 0,
      localHits: 0,
      misses: 0,
      totalRequests: 0
    };
    
    // 定期清理过期缓存
    this.startCleanupTimer();
  }

  /**
   * 获取用户权限（带缓存）
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 用户权限信息
   */
  async getUserPermissions(userId) {
    this.stats.totalRequests++;
    
    const cacheKey = `user_permissions_${userId}`;
    
    // 1. 检查内存缓存
    const memoryResult = this.getFromMemory(cacheKey);
    if (memoryResult) {
      this.stats.memoryHits++;
      return memoryResult;
    }
    
    // 2. 检查本地存储缓存
    const localResult = this.getFromLocal(cacheKey);
    if (localResult) {
      this.stats.localHits++;
      // 将结果放入内存缓存
      this.setToMemory(cacheKey, localResult, this.config.memoryTTL);
      return localResult;
    }
    
    // 3. 缓存未命中，从服务器获取
    this.stats.misses++;
    const permissions = await this.fetchUserPermissionsFromServer(userId);
    
    // 4. 缓存结果
    this.setToMemory(cacheKey, permissions, this.config.memoryTTL);
    this.setToLocal(cacheKey, permissions, this.config.localTTL);
    
    return permissions;
  }

  /**
   * 检查权限（带缓存）
   * @param {string} userId 用户ID
   * @param {string|Array} permission 权限或权限数组
   * @param {Object} options 选项
   * @returns {Promise<boolean>} 是否有权限
   */
  async checkPermission(userId, permission, options = {}) {
    const { requireAll = false, resourceId = null, resourceOwnerId = null } = options;
    
    // 构建缓存键
    const cacheKey = this.buildPermissionCacheKey(userId, permission, options);
    
    // 检查权限检查结果缓存
    const cachedResult = this.getFromMemory(cacheKey);
    if (cachedResult !== null && cachedResult !== undefined) {
      this.stats.memoryHits++;
      return cachedResult;
    }
    
    // 获取用户权限
    const userPermissions = await this.getUserPermissions(userId);
    
    // 执行权限检查
    let hasRequiredPermission = false;
    
    if (Array.isArray(permission)) {
      if (requireAll) {
        hasRequiredPermission = permission.every(perm => 
          this.hasPermissionDirect(userPermissions.role, perm)
        );
      } else {
        hasRequiredPermission = permission.some(perm => 
          this.hasPermissionDirect(userPermissions.role, perm)
        );
      }
    } else {
      hasRequiredPermission = this.hasPermissionDirect(userPermissions.role, permission);
    }
    
    // 检查资源级权限
    if (hasRequiredPermission && resourceId && resourceOwnerId) {
      if (!userPermissions.isAdmin) {
        hasRequiredPermission = resourceOwnerId === userId;
      }
    }
    
    // 缓存权限检查结果
    this.setToMemory(cacheKey, hasRequiredPermission, this.config.permissionCheckTTL);
    
    return hasRequiredPermission;
  }

  /**
   * 批量权限检查（优化版）
   * @param {string} userId 用户ID
   * @param {Array} permissionChecks 权限检查数组
   * @returns {Promise<Array>} 权限检查结果数组
   */
  async batchCheckPermissions(userId, permissionChecks) {
    // 获取用户权限（只需要一次）
    const userPermissions = await this.getUserPermissions(userId);
    
    const results = [];
    
    for (const check of permissionChecks) {
      const { permission, options = {} } = check;
      const cacheKey = this.buildPermissionCacheKey(userId, permission, options);
      
      // 检查缓存
      let result = this.getFromMemory(cacheKey);
      
      if (result === null || result === undefined) {
        // 执行权限检查
        result = this.executePermissionCheck(userPermissions, permission, options);
        
        // 缓存结果
        this.setToMemory(cacheKey, result, this.config.permissionCheckTTL);
      }
      
      results.push(result);
    }
    
    return results;
  }

  /**
   * 内存缓存操作
   */
  getFromMemory(key) {
    const item = this.memoryCache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.memoryCache.delete(key);
      return null;
    }
    
    return item.data;
  }

  setToMemory(key, data, ttl) {
    // 检查内存缓存大小限制
    if (this.memoryCache.size >= this.config.maxMemoryEntries) {
      this.evictOldestMemoryEntry();
    }
    
    this.memoryCache.set(key, {
      data,
      expiry: Date.now() + ttl,
      timestamp: Date.now()
    });
  }

  /**
   * 本地存储缓存操作
   */
  getFromLocal(key) {
    try {
      const item = wx.getStorageSync(this.localCachePrefix + key);
      if (!item) return null;
      
      if (Date.now() > item.expiry) {
        wx.removeStorageSync(this.localCachePrefix + key);
        return null;
      }
      
      return item.data;
    } catch (error) {
      console.warn('本地缓存读取失败:', error);
      return null;
    }
  }

  setToLocal(key, data, ttl) {
    try {
      wx.setStorageSync(this.localCachePrefix + key, {
        data,
        expiry: Date.now() + ttl,
        timestamp: Date.now()
      });
    } catch (error) {
      console.warn('本地缓存写入失败:', error);
    }
  }

  /**
   * 构建权限检查缓存键
   */
  buildPermissionCacheKey(userId, permission, options) {
    const permStr = Array.isArray(permission) ? permission.join(',') : permission;
    const optStr = JSON.stringify(options);
    return `perm_check_${userId}_${permStr}_${btoa(optStr)}`;
  }

  /**
   * 直接权限检查（不使用缓存）
   */
  hasPermissionDirect(userRole, permission) {
    const { hasPermission } = require('./role-permission');
    return hasPermission(userRole, permission);
  }

  /**
   * 执行权限检查逻辑
   */
  executePermissionCheck(userPermissions, permission, options) {
    const { requireAll = false, resourceId = null, resourceOwnerId = null } = options;
    
    let hasRequiredPermission = false;
    
    if (Array.isArray(permission)) {
      if (requireAll) {
        hasRequiredPermission = permission.every(perm => 
          this.hasPermissionDirect(userPermissions.role, perm)
        );
      } else {
        hasRequiredPermission = permission.some(perm => 
          this.hasPermissionDirect(userPermissions.role, perm)
        );
      }
    } else {
      hasRequiredPermission = this.hasPermissionDirect(userPermissions.role, permission);
    }
    
    // 检查资源级权限
    if (hasRequiredPermission && resourceId && resourceOwnerId) {
      if (!userPermissions.isAdmin) {
        hasRequiredPermission = resourceOwnerId === userPermissions.userId;
      }
    }
    
    return hasRequiredPermission;
  }

  /**
   * 从服务器获取用户权限
   */
  async fetchUserPermissionsFromServer(userId) {
    const { getCurrentUserPermissions } = require('./role-permission');
    return await getCurrentUserPermissions();
  }

  /**
   * 清除用户权限缓存
   */
  clearUserPermissions(userId) {
    const cacheKey = `user_permissions_${userId}`;
    this.memoryCache.delete(cacheKey);
    wx.removeStorageSync(this.localCachePrefix + cacheKey);
  }

  /**
   * 清除所有权限检查缓存
   */
  clearPermissionChecks(userId) {
    // 清除内存中的权限检查缓存
    for (const [key] of this.memoryCache) {
      if (key.startsWith(`perm_check_${userId}_`)) {
        this.memoryCache.delete(key);
      }
    }
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.memoryCache.clear();
    
    // 清除本地存储中的权限缓存
    try {
      const storageInfo = wx.getStorageInfoSync();
      storageInfo.keys.forEach(key => {
        if (key.startsWith(this.localCachePrefix)) {
          wx.removeStorageSync(key);
        }
      });
    } catch (error) {
      console.warn('清除本地缓存失败:', error);
    }
  }

  /**
   * 淘汰最旧的内存缓存条目
   */
  evictOldestMemoryEntry() {
    let oldestKey = null;
    let oldestTimestamp = Infinity;
    
    for (const [key, item] of this.memoryCache) {
      if (item.timestamp < oldestTimestamp) {
        oldestTimestamp = item.timestamp;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.memoryCache.delete(oldestKey);
    }
  }

  /**
   * 启动定期清理定时器
   */
  startCleanupTimer() {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 5 * 60 * 1000);
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    const now = Date.now();
    
    // 清理内存缓存
    for (const [key, item] of this.memoryCache) {
      if (now > item.expiry) {
        this.memoryCache.delete(key);
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const hitRate = this.stats.totalRequests > 0 
      ? ((this.stats.memoryHits + this.stats.localHits) / this.stats.totalRequests * 100).toFixed(2)
      : 0;
    
    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      memoryCacheSize: this.memoryCache.size
    };
  }
}

// 创建全局实例
const permissionCacheManager = new PermissionCacheManager();

module.exports = permissionCacheManager;
