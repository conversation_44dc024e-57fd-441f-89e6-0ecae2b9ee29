/**
 * 数据隔离机制工具类
 * Data Isolation Mechanism Utilities
 * 
 * 功能：
 * - 多租户数据隔离控制
 * - 查询条件自动注入
 * - 数据访问权限验证
 * - 跨租户数据访问控制
 */

const { ROLES, PERMISSIONS, hasPermission } = require('./role-permission');

/**
 * 数据隔离级别枚举
 */
const ISOLATION_LEVELS = {
  PLATFORM: 'platform',     // 平台级数据（所有租户可见）
  TENANT: 'tenant',         // 租户级数据（租户内隔离）
  USER: 'user',             // 用户级数据（用户私有）
  SHARED: 'shared'          // 共享数据（有条件共享）
};

/**
 * 集合隔离配置
 */
const COLLECTION_ISOLATION_CONFIG = {
  // 平台级集合
  'goose_prices': {
    level: ISOLATION_LEVELS.PLATFORM,
    readPermissions: [],
    writePermissions: ['PLATFORM_GOOSE_PRICE_MANAGE']
  },
  'platform_announcements': {
    level: ISOLATION_LEVELS.PLATFORM,
    readPermissions: [],
    writePermissions: ['PLATFORM_ANNOUNCEMENT_MANAGE']
  },
  'platform_knowledge_base': {
    level: ISOLATION_LEVELS.PLATFORM,
    readPermissions: [],
    writePermissions: ['PLATFORM_KNOWLEDGE_MANAGE']
  },
  'tenants': {
    level: ISOLATION_LEVELS.PLATFORM,
    readPermissions: ['PLATFORM_TENANT_VIEW'],
    writePermissions: ['PLATFORM_TENANT_MANAGE']
  },
  'ai_model_config': {
    level: ISOLATION_LEVELS.PLATFORM,
    readPermissions: ['PLATFORM_AI_CONFIG'],
    writePermissions: ['PLATFORM_AI_CONFIG']
  },
  
  // 租户级集合
  'flocks': {
    level: ISOLATION_LEVELS.TENANT,
    readPermissions: ['FLOCK_VIEW_ALL', 'FLOCK_VIEW_OWN'],
    writePermissions: ['FLOCK_CREATE', 'FLOCK_EDIT']
  },
  'health_records': {
    level: ISOLATION_LEVELS.TENANT,
    readPermissions: ['HEALTH_VIEW_ALL', 'HEALTH_VIEW_OWN'],
    writePermissions: ['HEALTH_CREATE', 'HEALTH_EDIT']
  },
  'tenant_materials': {
    level: ISOLATION_LEVELS.TENANT,
    readPermissions: ['MATERIAL_VIEW_ALL', 'MATERIAL_VIEW_OWN'],
    writePermissions: ['MATERIAL_CREATE', 'MATERIAL_EDIT']
  },
  'tenant_finance_records': {
    level: ISOLATION_LEVELS.TENANT,
    readPermissions: ['FINANCE_VIEW_ALL', 'FINANCE_VIEW_OWN'],
    writePermissions: ['FINANCE_CREATE', 'FINANCE_EDIT']
  },
  'production_records': {
    level: ISOLATION_LEVELS.TENANT,
    readPermissions: ['PRODUCTION_VIEW_ALL', 'PRODUCTION_VIEW_OWN'],
    writePermissions: ['PRODUCTION_CREATE', 'PRODUCTION_EDIT']
  },
  
  // 用户级集合
  'user_preferences': {
    level: ISOLATION_LEVELS.USER,
    readPermissions: [],
    writePermissions: []
  },
  'user_notifications': {
    level: ISOLATION_LEVELS.USER,
    readPermissions: [],
    writePermissions: []
  }
};

/**
 * 数据隔离查询构建器
 */
class DataIsolationQueryBuilder {
  constructor(user, collection) {
    this.user = user;
    this.collection = collection;
    this.config = COLLECTION_ISOLATION_CONFIG[collection] || {
      level: ISOLATION_LEVELS.TENANT,
      readPermissions: [],
      writePermissions: []
    };
  }

  /**
   * 构建读取查询条件
   */
  buildReadQuery(baseQuery = {}) {
    const isolationQuery = this.getIsolationQuery('read');
    return {
      ...baseQuery,
      ...isolationQuery
    };
  }

  /**
   * 构建写入查询条件
   */
  buildWriteQuery(baseQuery = {}) {
    const isolationQuery = this.getIsolationQuery('write');
    return {
      ...baseQuery,
      ...isolationQuery
    };
  }

  /**
   * 获取隔离查询条件
   */
  getIsolationQuery(operation) {
    const { level } = this.config;
    const permissions = operation === 'read' ? this.config.readPermissions : this.config.writePermissions;

    // 检查权限
    if (!this.checkPermissions(permissions)) {
      throw new Error(`权限不足：无法${operation === 'read' ? '读取' : '写入'}${this.collection}数据`);
    }

    switch (level) {
      case ISOLATION_LEVELS.PLATFORM:
        return this.buildPlatformQuery();
      case ISOLATION_LEVELS.TENANT:
        return this.buildTenantQuery();
      case ISOLATION_LEVELS.USER:
        return this.buildUserQuery();
      case ISOLATION_LEVELS.SHARED:
        return this.buildSharedQuery();
      default:
        return this.buildTenantQuery();
    }
  }

  /**
   * 构建平台级查询条件
   */
  buildPlatformQuery() {
    // 平台级数据通常不需要额外的隔离条件
    // 但需要检查用户是否有平台管理权限
    if (!this.isPlatformAdmin()) {
      // 非平台管理员只能读取公开的平台数据
      return {
        status: 'published',
        visibility: 'public'
      };
    }
    return {};
  }

  /**
   * 构建租户级查询条件
   */
  buildTenantQuery() {
    if (!this.user.tenant_id) {
      throw new Error('用户未绑定租户，无法访问租户数据');
    }

    const query = {
      tenant_id: this.user.tenant_id
    };

    // 如果用户没有查看所有数据的权限，只能查看自己的数据
    if (!this.hasViewAllPermission()) {
      query.user_id = this.user._id;
    }

    return query;
  }

  /**
   * 构建用户级查询条件
   */
  buildUserQuery() {
    return {
      user_id: this.user._id,
      tenant_id: this.user.tenant_id
    };
  }

  /**
   * 构建共享数据查询条件
   */
  buildSharedQuery() {
    // 共享数据的访问逻辑可以根据具体业务需求定制
    return {
      $or: [
        { tenant_id: this.user.tenant_id },
        { shared_with_tenants: this.user.tenant_id },
        { visibility: 'public' }
      ]
    };
  }

  /**
   * 检查权限
   */
  checkPermissions(requiredPermissions) {
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    return requiredPermissions.some(permission => 
      hasPermission(this.user.role, permission, this.user.permissions)
    );
  }

  /**
   * 检查是否为平台管理员
   */
  isPlatformAdmin() {
    return [ROLES.SUPER_ADMIN, ROLES.PLATFORM_ADMIN].includes(this.user.role);
  }

  /**
   * 检查是否有查看所有数据的权限
   */
  hasViewAllPermission() {
    const viewAllPermissions = [
      'FLOCK_VIEW_ALL',
      'HEALTH_VIEW_ALL',
      'MATERIAL_VIEW_ALL',
      'FINANCE_VIEW_ALL',
      'PRODUCTION_VIEW_ALL'
    ];

    return viewAllPermissions.some(permission => 
      hasPermission(this.user.role, permission, this.user.permissions)
    );
  }
}

/**
 * 数据隔离中间件
 */
class DataIsolationMiddleware {
  /**
   * 创建安全的数据库查询
   */
  static createSecureQuery(user, collection, baseQuery = {}) {
    const builder = new DataIsolationQueryBuilder(user, collection);
    return builder.buildReadQuery(baseQuery);
  }

  /**
   * 验证写入数据的安全性
   */
  static validateWriteData(user, collection, data) {
    const builder = new DataIsolationQueryBuilder(user, collection);
    const config = COLLECTION_ISOLATION_CONFIG[collection];

    if (!config) {
      throw new Error(`未知的集合：${collection}`);
    }

    // 检查写入权限
    if (!builder.checkPermissions(config.writePermissions)) {
      throw new Error(`权限不足：无法写入${collection}数据`);
    }

    // 根据隔离级别验证数据
    switch (config.level) {
      case ISOLATION_LEVELS.TENANT:
        return this.validateTenantData(user, data);
      case ISOLATION_LEVELS.USER:
        return this.validateUserData(user, data);
      case ISOLATION_LEVELS.PLATFORM:
        return this.validatePlatformData(user, data);
      default:
        return this.validateTenantData(user, data);
    }
  }

  /**
   * 验证租户级数据
   */
  static validateTenantData(user, data) {
    if (!user.tenant_id) {
      throw new Error('用户未绑定租户');
    }

    // 确保数据包含正确的租户ID
    const validatedData = {
      ...data,
      tenant_id: user.tenant_id
    };

    // 如果用户没有管理权限，确保数据包含用户ID
    if (!hasPermission(user.role, 'ADMIN_PERMISSIONS')) {
      validatedData.user_id = user._id;
    }

    return validatedData;
  }

  /**
   * 验证用户级数据
   */
  static validateUserData(user, data) {
    return {
      ...data,
      user_id: user._id,
      tenant_id: user.tenant_id
    };
  }

  /**
   * 验证平台级数据
   */
  static validatePlatformData(user, data) {
    if (!this.isPlatformAdmin(user)) {
      throw new Error('权限不足：只有平台管理员可以操作平台级数据');
    }

    return {
      ...data,
      created_by: user._id,
      tenant_id: null // 平台级数据的tenant_id为null
    };
  }

  /**
   * 检查是否为平台管理员
   */
  static isPlatformAdmin(user) {
    return [ROLES.SUPER_ADMIN, ROLES.PLATFORM_ADMIN].includes(user.role);
  }
}

/**
 * 数据访问日志记录器
 */
class DataAccessLogger {
  /**
   * 记录数据访问日志
   */
  static async logDataAccess(user, collection, operation, query, result) {
    try {
      const logData = {
        user_id: user._id,
        tenant_id: user.tenant_id,
        collection: collection,
        operation: operation, // read, write, update, delete
        query: JSON.stringify(query),
        result_count: result.length || 1,
        timestamp: new Date(),
        ip_address: user.ip_address || 'unknown',
        user_agent: user.user_agent || 'unknown'
      };

      // 这里可以将日志写入专门的日志集合
      // await db.collection('data_access_logs').add({ data: logData });
      
      console.log('数据访问日志:', logData);
    } catch (error) {
      console.error('记录数据访问日志失败:', error);
    }
  }
}

module.exports = {
  ISOLATION_LEVELS,
  COLLECTION_ISOLATION_CONFIG,
  DataIsolationQueryBuilder,
  DataIsolationMiddleware,
  DataAccessLogger
};
