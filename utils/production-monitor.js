/**
 * 智慧养鹅SaaS平台 - 生产环境监控系统
 * 健康检查、性能监控、错误告警一体化解决方案
 */

// 微信小程序环境兼容性处理
let os, fs, path, promisify;
try {
  if (typeof wx === 'undefined') {
    // Node.js 环境
    os = require('os');
    fs = require('fs');
    path = require('path');
    promisify = require('util').promisify;
  } else {
    // 微信小程序环境 - 使用兼容性垫片
    const MiniProgramCompatibility = require('./miniprogram-compatibility.js');
    os = null;
    fs = null;
    path = null;
    promisify = null;
  }
} catch (error) {
  console.warn('模块加载失败，使用兼容模式:', error.message);
}

class ProductionMonitor {
  constructor(options = {}) {
    this.config = {
      // 健康检查配置
      healthCheck: {
        interval: options.healthCheckInterval || 30000, // 30秒
        timeout: options.healthTimeout || 5000,
        endpoints: options.endpoints || []
      },
      
      // 性能监控配置
      performance: {
        enabled: options.performanceEnabled !== false,
        sampleRate: options.sampleRate || 1.0, // 100%采样
        slowThreshold: options.slowThreshold || 1000, // 1秒
      },
      
      // 错误告警配置
      alerting: {
        enabled: options.alertingEnabled !== false,
        errorThreshold: options.errorThreshold || 10, // 10个错误/分钟
        webhookUrl: options.webhookUrl,
        emailRecipients: options.emailRecipients || []
      },
      
      // 日志配置
      logging: {
        level: options.logLevel || 'info',
        file: options.logFile || 'logs/monitor.log',
        maxSize: options.maxLogSize || '10MB',
        maxFiles: options.maxLogFiles || 5
      }
    };
    
    this.metrics = {
      requests: { total: 0, errors: 0 },
      response_times: [],
      memory: [],
      cpu: [],
      errors: [],
      last_reset: Date.now()
    };
    
    this.alerts = new Map();
    this.isShuttingDown = false;
    
    this.init();
  }
  
  /**
   * 初始化监控系统
   */
  init() {
    console.log('🔍 初始化生产环境监控系统...');
    
    // 启动健康检查
    this.startHealthCheck();
    
    // 启动性能监控
    this.startPerformanceMonitoring();
    
    // 启动系统资源监控
    this.startSystemMonitoring();
    
    // 设置优雅关闭
    this.setupGracefulShutdown();
    
    console.log('✅ 监控系统启动成功');
  }
  
  /**
   * Express中间件 - 请求监控
   */
  middleware() {
    return (req, res, next) => {
      const startTime = Date.now();
      const originalSend = res.send;
      
      // 请求计数
      this.metrics.requests.total++;
      
      // 监控响应
      res.send = function(data) {
        const responseTime = Date.now() - startTime;
        
        // 记录响应时间
        this.recordResponseTime(responseTime);
        
        // 记录错误
        if (res.statusCode >= 400) {
          this.recordError(req, res, responseTime);
        }
        
        // 慢查询告警
        if (responseTime > this.config.performance.slowThreshold) {
          this.recordSlowRequest(req, responseTime);
        }
        
        return originalSend.call(this, data);
      }.bind(this);
      
      next();
    };
  }
  
  /**
   * 健康检查
   */
  async startHealthCheck() {
    const checkHealth = async () => {
      if (this.isShuttingDown) return;
      
      try {
        const health = await this.performHealthCheck();
        
        if (!health.healthy) {
          this.triggerAlert('HEALTH_CHECK_FAILED', {
            message: '系统健康检查失败',
            details: health.details
          });
        }
        
        // 记录健康状态
        this.recordHealthStatus(health);
        
      } catch (error) {
        console.error('健康检查失败:', error);
        this.triggerAlert('HEALTH_CHECK_ERROR', {
          message: '健康检查执行失败',
          error: error.message
        });
      }
      
      // 下次检查
      setTimeout(checkHealth, this.config.healthCheck.interval);
    };
    
    checkHealth();
  }
  
  /**
   * 执行健康检查
   */
  async performHealthCheck() {
    const checks = [];
    
    // 数据库健康检查
    if (this.database) {
      checks.push(this.checkDatabase());
    }
    
    // 内存使用检查
    checks.push(this.checkMemory());
    
    // 磁盘空间检查
    checks.push(this.checkDiskSpace());
    
    // 外部服务检查
    for (const endpoint of this.config.healthCheck.endpoints) {
      checks.push(this.checkEndpoint(endpoint));
    }
    
    const results = await Promise.allSettled(checks);
    const failed = results.filter(r => r.status === 'rejected' || !r.value.healthy);
    
    return {
      healthy: failed.length === 0,
      timestamp: new Date().toISOString(),
      checks: results.map(r => r.value || { healthy: false, error: r.reason }),
      summary: {
        total: results.length,
        passed: results.length - failed.length,
        failed: failed.length
      }
    };
  }
  
  /**
   * 数据库健康检查
   */
  async checkDatabase() {
    try {
      const { healthCheck } = require('../database/connection-optimized');
      const result = await Promise.race([
        healthCheck(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('数据库健康检查超时')), 
          this.config.healthCheck.timeout)
        )
      ]);
      
      return {
        name: 'database',
        healthy: result.healthy,
        responseTime: result.responseTime,
        details: result.error ? { error: result.error } : null
      };
    } catch (error) {
      return {
        name: 'database',
        healthy: false,
        error: error.message
      };
    }
  }
  
  /**
   * 内存使用检查
   */
  async checkMemory() {
    const used = this.getMockMemoryUsage();
    const total = os ? os.totalmem() : 100 * 1024 * 1024; // 100MB 默认值
    const free = os ? os.freemem() : 50 * 1024 * 1024; // 50MB 默认值
    const usedPercent = ((total - free) / total) * 100;
    
    return {
      name: 'memory',
      healthy: usedPercent < 90, // 90%阈值
      details: {
        heap: {
          used: Math.round(used.heapUsed / 1024 / 1024) + 'MB',
          total: Math.round(used.heapTotal / 1024 / 1024) + 'MB'
        },
        system: {
          used: Math.round((total - free) / 1024 / 1024) + 'MB',
          free: Math.round(free / 1024 / 1024) + 'MB',
          usedPercent: Math.round(usedPercent * 100) / 100
        }
      }
    };
  }
  
  /**
   * 磁盘空间检查
   */
  async checkDiskSpace() {
    try {
      const stats = await promisify(fs.statvfs || fs.stat)('.');
      const free = stats.bavail * stats.bsize;
      const total = stats.blocks * stats.bsize;
      const used = total - free;
      const usedPercent = (used / total) * 100;
      
      return {
        name: 'disk',
        healthy: usedPercent < 85, // 85%阈值
        details: {
          total: Math.round(total / 1024 / 1024 / 1024) + 'GB',
          used: Math.round(used / 1024 / 1024 / 1024) + 'GB',
          free: Math.round(free / 1024 / 1024 / 1024) + 'GB',
          usedPercent: Math.round(usedPercent * 100) / 100
        }
      };
    } catch (error) {
      return {
        name: 'disk',
        healthy: false,
        error: error.message
      };
    }
  }
  
  /**
   * 外部端点检查
   */
  async checkEndpoint(endpoint) {
    const startTime = Date.now();
    
    try {
      const response = await fetch(endpoint.url, {
        method: endpoint.method || 'GET',
        timeout: this.config.healthCheck.timeout,
        headers: endpoint.headers || {}
      });
      
      const responseTime = Date.now() - startTime;
      const healthy = response.ok && responseTime < (endpoint.maxResponseTime || 5000);
      
      return {
        name: `endpoint_${endpoint.name || endpoint.url}`,
        healthy,
        details: {
          url: endpoint.url,
          status: response.status,
          responseTime,
          ok: response.ok
        }
      };
    } catch (error) {
      return {
        name: `endpoint_${endpoint.name || endpoint.url}`,
        healthy: false,
        error: error.message,
        responseTime: Date.now() - startTime
      };
    }
  }
  
  /**
   * 性能监控
   */
  startPerformanceMonitoring() {
    if (!this.config.performance.enabled) return;
    
    console.log('📊 启动性能监控...');
    
    // 定时清理旧数据
    setInterval(() => {
      this.cleanupOldMetrics();
    }, 60000); // 每分钟清理一次
    
    // 定时生成性能报告
    setInterval(() => {
      this.generatePerformanceReport();
    }, 300000); // 每5分钟生成一次
  }
  
  /**
   * 系统资源监控
   */
  startSystemMonitoring() {
    const monitorSystem = () => {
      if (this.isShuttingDown) return;
      
      const cpuUsage = this.getMockCPUUsage();
      const memUsage = this.getMockMemoryUsage();
      
      // 记录CPU使用率
      this.metrics.cpu.push({
        timestamp: Date.now(),
        user: cpuUsage.user,
        system: cpuUsage.system
      });
      
      // 记录内存使用
      this.metrics.memory.push({
        timestamp: Date.now(),
        heap: memUsage.heapUsed,
        rss: memUsage.rss,
        external: memUsage.external
      });
      
      // 内存泄漏检测
      if (this.metrics.memory.length > 10) {
        const recent = this.metrics.memory.slice(-10);
        const trend = this.calculateMemoryTrend(recent);
        
        if (trend > 0.1) { // 内存持续增长10%以上
          this.triggerAlert('MEMORY_LEAK_SUSPECTED', {
            message: '检测到可能的内存泄漏',
            trend: `${(trend * 100).toFixed(2)}%`,
            current: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB'
          });
        }
      }
      
      setTimeout(monitorSystem, 30000); // 30秒监控一次
    };
    
    monitorSystem();
  }
  
  /**
   * 记录响应时间
   */
  recordResponseTime(responseTime) {
    this.metrics.response_times.push({
      timestamp: Date.now(),
      time: responseTime
    });
    
    // 保持最近1000条记录
    if (this.metrics.response_times.length > 1000) {
      this.metrics.response_times.shift();
    }
  }
  
  /**
   * 记录错误
   */
  recordError(req, res, responseTime) {
    this.metrics.requests.errors++;
    
    const error = {
      timestamp: Date.now(),
      method: req.method,
      url: req.url,
      status: res.statusCode,
      responseTime,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    };
    
    this.metrics.errors.push(error);
    
    // 错误率告警
    const errorRate = this.calculateErrorRate();
    if (errorRate > this.config.alerting.errorThreshold) {
      this.triggerAlert('HIGH_ERROR_RATE', {
        message: `错误率过高: ${errorRate.toFixed(2)}%`,
        recent_errors: this.metrics.errors.slice(-5)
      });
    }
  }
  
  /**
   * 触发告警
   */
  triggerAlert(type, data) {
    const alertKey = `${type}_${Date.now() - (Date.now() % 300000)}`; // 5分钟内去重
    
    if (this.alerts.has(alertKey)) {
      return; // 避免重复告警
    }
    
    this.alerts.set(alertKey, true);
    
    const alert = {
      type,
      timestamp: new Date().toISOString(),
      data,
      environment: typeof wx !== 'undefined' && wx.getSystemInfoSync().platform === 'devtools' ? 'development' : 'production',
      server: {
        hostname: os ? os.hostname() : 'miniprogram-host',
        pid: Date.now(),
        uptime: this.getMockUptime()
      }
    };
    
    console.error('🚨 触发告警:', JSON.stringify(alert, null, 2));
    
    // 发送告警通知
    this.sendAlert(alert);
  }
  
  /**
   * 发送告警通知
   */
  async sendAlert(alert) {
    if (!this.config.alerting.enabled) return;
    
    try {
      // Webhook通知
      if (this.config.alerting.webhookUrl) {
        await fetch(this.config.alerting.webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(alert)
        });
      }
      
      // 邮件通知 (需要配置邮件服务)
      if (this.config.alerting.emailRecipients.length > 0) {
        await this.sendEmailAlert(alert);
      }
      
    } catch (error) {
      console.error('发送告警失败:', error);
    }
  }
  
  /**
   * 生成监控报告
   */
  getMonitoringReport() {
    const now = Date.now();
    const uptime = this.getMockUptime();
    
    // 计算平均响应时间
    const recentResponses = this.metrics.response_times.filter(
      r => now - r.timestamp < 3600000 // 最近1小时
    );
    const avgResponseTime = recentResponses.length > 0 
      ? recentResponses.reduce((sum, r) => sum + r.time, 0) / recentResponses.length 
      : 0;
    
    // 计算错误率
    const errorRate = this.calculateErrorRate();
    
    return {
      timestamp: new Date().toISOString(),
      uptime: {
        seconds: Math.floor(uptime),
        human: this.formatUptime(uptime)
      },
      requests: {
        total: this.metrics.requests.total,
        errors: this.metrics.requests.errors,
        errorRate: `${errorRate.toFixed(2)}%`,
        successRate: `${(100 - errorRate).toFixed(2)}%`
      },
      performance: {
        avgResponseTime: Math.round(avgResponseTime),
        slowRequests: this.metrics.response_times.filter(r => r.time > 1000).length
      },
      memory: {
        current: Math.round(this.getMockMemoryUsage().heapUsed / 1024 / 1024) + 'MB',
        trend: this.getMemoryTrend()
      },
      alerts: {
        active: this.alerts.size,
        types: Array.from(this.alerts.keys()).map(k => k.split('_')[0])
      }
    };
  }
  
  /**
   * 计算错误率
   */
  calculateErrorRate() {
    if (this.metrics.requests.total === 0) return 0;
    return (this.metrics.requests.errors / this.metrics.requests.total) * 100;
  }
  
  /**
   * 优雅关闭
   */
  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      console.log(`\n🛑 收到${signal}信号，开始优雅关闭...`);
      
      this.isShuttingDown = true;
      
      // 生成最终报告
      const finalReport = this.getMonitoringReport();
      console.log('📊 最终监控报告:', JSON.stringify(finalReport, null, 2));
      
      console.log('✅ 监控系统已安全关闭');
      // process.exit removed for miniprogram compatibility
    };
    
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
  }
  
  // 工具方法
  formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  }
  
  cleanupOldMetrics() {
    const cutoff = Date.now() - 3600000; // 1小时前
    
    this.metrics.response_times = this.metrics.response_times.filter(
      r => r.timestamp > cutoff
    );
    
    this.metrics.errors = this.metrics.errors.filter(
      e => e.timestamp > cutoff
    );
    
    this.metrics.memory = this.metrics.memory.filter(
      m => m.timestamp > cutoff
    );
    
    this.metrics.cpu = this.metrics.cpu.filter(
      c => c.timestamp > cutoff
    );
  }
  
  /**
   * 记录健康状态
   */
  recordHealthStatus(health) {
    if (!this.healthHistory) {
      this.healthHistory = [];
    }
    
    this.healthHistory.push({
      timestamp: Date.now(),
      healthy: health.healthy,
      details: health.summary
    });
    
    // 保持最近100条记录
    if (this.healthHistory.length > 100) {
      this.healthHistory.shift();
    }
  }
  
  /**
   * 获取内存趋势
   */
  getMemoryTrend() {
    if (this.metrics.memory.length < 2) {
      return '稳定';
    }
    
    const recent = this.metrics.memory.slice(-5);
    const first = recent[0].heap;
    const last = recent[recent.length - 1].heap;
    const change = ((last - first) / first) * 100;
    
    if (change > 5) return '上升';
    if (change < -5) return '下降';
    return '稳定';
  }
  
  /**
   * 计算内存趋势
   */
  calculateMemoryTrend(memoryData) {
    if (memoryData.length < 2) return 0;

    const first = memoryData[0].heap;
    const last = memoryData[memoryData.length - 1].heap;

    return (last - first) / first;
  }

  /**
   * 微信小程序兼容性方法 - 模拟内存使用情况
   */
  getMockMemoryUsage() {
    return {
      rss: 50 * 1024 * 1024, // 50MB
      heapTotal: 30 * 1024 * 1024, // 30MB
      heapUsed: 20 * 1024 * 1024, // 20MB
      external: 5 * 1024 * 1024, // 5MB
      arrayBuffers: 1 * 1024 * 1024 // 1MB
    };
  }

  /**
   * 微信小程序兼容性方法 - 模拟CPU使用情况
   */
  getMockCPUUsage() {
    return {
      user: Math.floor(Math.random() * 1000000),
      system: Math.floor(Math.random() * 500000)
    };
  }

  /**
   * 微信小程序兼容性方法 - 模拟运行时间
   */
  getMockUptime() {
    // 返回一个模拟的运行时间（秒）
    return Math.floor(Date.now() / 1000) % 86400; // 当天的秒数
  }
}

module.exports = ProductionMonitor;