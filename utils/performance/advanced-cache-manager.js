/**
 * 高级缓存管理器
 * Advanced Cache Manager
 */

class AdvancedCacheManager {
  constructor() {
    this.memoryCache = new Map();
    this.storageCache = new Map();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalRequests: 0
    };
    this.maxMemorySize = 50; // 最大内存缓存条目数
    this.maxStorageSize = 200; // 最大存储缓存条目数
    this.defaultTTL = 5 * 60 * 1000; // 默认5分钟TTL
    this.cleanupInterval = 60 * 1000; // 1分钟清理一次
    
    this.startCleanupTimer();
  }

  /**
   * 智能缓存策略
   */
  async get(key, fetchFunction, options = {}) {
    const {
      ttl = this.defaultTTL,
      useMemory = true,
      useStorage = true,
      priority = 'normal' // low, normal, high
    } = options;

    this.cacheStats.totalRequests++;

    // 1. 尝试从内存缓存获取
    if (useMemory) {
      const memoryResult = this.getFromMemory(key);
      if (memoryResult) {
        this.cacheStats.hits++;
        return memoryResult;
      }
    }

    // 2. 尝试从存储缓存获取
    if (useStorage) {
      const storageResult = await this.getFromStorage(key);
      if (storageResult) {
        // 提升到内存缓存
        if (useMemory) {
          this.setToMemory(key, storageResult, ttl, priority);
        }
        this.cacheStats.hits++;
        return storageResult;
      }
    }

    // 3. 缓存未命中，执行获取函数
    this.cacheStats.misses++;
    
    try {
      const data = await fetchFunction();
      
      // 4. 存储到缓存
      if (useMemory) {
        this.setToMemory(key, data, ttl, priority);
      }
      if (useStorage) {
        await this.setToStorage(key, data, ttl);
      }
      
      return data;
    } catch (error) {
      console.error('缓存获取数据失败:', error);
      throw error;
    }
  }

  /**
   * 从内存缓存获取
   */
  getFromMemory(key) {
    const item = this.memoryCache.get(key);
    if (!item) {
      return null;
    }

    if (Date.now() > item.expireTime) {
      this.memoryCache.delete(key);
      return null;
    }

    // 更新访问时间和频率
    item.lastAccess = Date.now();
    item.accessCount++;
    
    return item.data;
  }

  /**
   * 设置到内存缓存
   */
  setToMemory(key, data, ttl, priority) {
    // 检查是否需要清理空间
    if (this.memoryCache.size >= this.maxMemorySize) {
      this.evictMemoryCache();
    }

    const item = {
      data: data,
      expireTime: Date.now() + ttl,
      lastAccess: Date.now(),
      accessCount: 1,
      priority: priority,
      size: this.estimateSize(data)
    };

    this.memoryCache.set(key, item);
  }

  /**
   * 从存储缓存获取
   */
  async getFromStorage(key) {
    try {
      const item = wx.getStorageSync(`cache_${key}`);
      if (!item) {
        return null;
      }

      if (Date.now() > item.expireTime) {
        wx.removeStorageSync(`cache_${key}`);
        return null;
      }

      return item.data;
    } catch (error) {
      console.warn('从存储缓存获取失败:', error);
      return null;
    }
  }

  /**
   * 设置到存储缓存
   */
  async setToStorage(key, data, ttl) {
    try {
      // 检查存储空间
      if (this.storageCache.size >= this.maxStorageSize) {
        await this.evictStorageCache();
      }

      const item = {
        data: data,
        expireTime: Date.now() + ttl,
        createdAt: Date.now()
      };

      wx.setStorageSync(`cache_${key}`, item);
      this.storageCache.set(key, true); // 记录存在性
    } catch (error) {
      console.warn('设置存储缓存失败:', error);
    }
  }

  /**
   * 内存缓存淘汰策略 (LRU + 优先级)
   */
  evictMemoryCache() {
    const items = Array.from(this.memoryCache.entries());
    
    // 按优先级和最近访问时间排序
    items.sort((a, b) => {
      const [keyA, itemA] = a;
      const [keyB, itemB] = b;
      
      // 优先级权重
      const priorityWeight = { low: 1, normal: 2, high: 3 };
      const scoreA = priorityWeight[itemA.priority] * itemA.accessCount + 
                    (Date.now() - itemA.lastAccess) / 1000;
      const scoreB = priorityWeight[itemB.priority] * itemB.accessCount + 
                    (Date.now() - itemB.lastAccess) / 1000;
      
      return scoreA - scoreB;
    });

    // 删除最低分的25%
    const evictCount = Math.ceil(items.length * 0.25);
    for (let i = 0; i < evictCount; i++) {
      const [key] = items[i];
      this.memoryCache.delete(key);
      this.cacheStats.evictions++;
    }
  }

  /**
   * 存储缓存淘汰策略
   */
  async evictStorageCache() {
    try {
      const storageInfo = wx.getStorageInfoSync();
      const cacheKeys = storageInfo.keys.filter(key => key.startsWith('cache_'));
      
      // 按创建时间排序，删除最旧的
      const cacheItems = [];
      for (const key of cacheKeys) {
        try {
          const item = wx.getStorageSync(key);
          if (item && item.createdAt) {
            cacheItems.push({ key, createdAt: item.createdAt });
          }
        } catch (error) {
          // 忽略损坏的缓存项
        }
      }

      cacheItems.sort((a, b) => a.createdAt - b.createdAt);
      
      // 删除最旧的25%
      const evictCount = Math.ceil(cacheItems.length * 0.25);
      for (let i = 0; i < evictCount; i++) {
        const { key } = cacheItems[i];
        wx.removeStorageSync(key);
        this.storageCache.delete(key.replace('cache_', ''));
        this.cacheStats.evictions++;
      }
    } catch (error) {
      console.warn('存储缓存淘汰失败:', error);
    }
  }

  /**
   * 估算数据大小
   */
  estimateSize(data) {
    try {
      return JSON.stringify(data).length;
    } catch (error) {
      return 1000; // 默认估算值
    }
  }

  /**
   * 预热缓存
   */
  async warmup(cacheConfigs) {
    console.log('开始缓存预热...');
    
    const promises = cacheConfigs.map(async (config) => {
      try {
        await this.get(config.key, config.fetchFunction, {
          ttl: config.ttl,
          priority: 'high'
        });
        console.log(`缓存预热完成: ${config.key}`);
      } catch (error) {
        console.error(`缓存预热失败: ${config.key}`, error);
      }
    });

    await Promise.allSettled(promises);
    console.log('缓存预热完成');
  }

  /**
   * 批量预取
   */
  async prefetch(keys, fetchFunctions, options = {}) {
    const promises = keys.map((key, index) => {
      return this.get(key, fetchFunctions[index], {
        ...options,
        priority: 'low' // 预取使用低优先级
      });
    });

    return Promise.allSettled(promises);
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now();
    
    // 清理内存缓存
    for (const [key, item] of this.memoryCache.entries()) {
      if (now > item.expireTime) {
        this.memoryCache.delete(key);
      }
    }

    // 清理存储缓存
    try {
      const storageInfo = wx.getStorageInfoSync();
      const cacheKeys = storageInfo.keys.filter(key => key.startsWith('cache_'));
      
      for (const key of cacheKeys) {
        try {
          const item = wx.getStorageSync(key);
          if (item && now > item.expireTime) {
            wx.removeStorageSync(key);
            this.storageCache.delete(key.replace('cache_', ''));
          }
        } catch (error) {
          // 删除损坏的缓存项
          wx.removeStorageSync(key);
        }
      }
    } catch (error) {
      console.warn('清理存储缓存失败:', error);
    }
  }

  /**
   * 启动清理定时器
   */
  startCleanupTimer() {
    setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
  }

  /**
   * 获取缓存统计
   */
  getStats() {
    const hitRate = this.cacheStats.totalRequests > 0 
      ? (this.cacheStats.hits / this.cacheStats.totalRequests * 100).toFixed(2)
      : 0;

    return {
      ...this.cacheStats,
      hitRate: `${hitRate}%`,
      memorySize: this.memoryCache.size,
      storageSize: this.storageCache.size,
      memoryUsage: Array.from(this.memoryCache.values())
        .reduce((total, item) => total + item.size, 0)
    };
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.memoryCache.clear();
    this.storageCache.clear();
    
    // 清空存储缓存
    try {
      const storageInfo = wx.getStorageInfoSync();
      const cacheKeys = storageInfo.keys.filter(key => key.startsWith('cache_'));
      cacheKeys.forEach(key => {
        wx.removeStorageSync(key);
      });
    } catch (error) {
      console.warn('清空存储缓存失败:', error);
    }

    // 重置统计
    this.cacheStats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalRequests: 0
    };
  }

  /**
   * 删除特定缓存
   */
  delete(key) {
    this.memoryCache.delete(key);
    this.storageCache.delete(key);
    
    try {
      wx.removeStorageSync(`cache_${key}`);
    } catch (error) {
      console.warn('删除存储缓存失败:', error);
    }
  }
}

// 创建全局实例
const advancedCacheManager = new AdvancedCacheManager();

module.exports = {
  AdvancedCacheManager,
  advancedCacheManager
};
