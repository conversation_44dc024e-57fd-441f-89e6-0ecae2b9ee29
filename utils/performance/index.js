/**
 * 性能优化系统统一导出
 * Performance Optimization System
 */

const { advancedCacheManager } = require('./advanced-cache-manager.js');
const { databaseOptimizer } = require('./database-optimizer.js');
const { renderOptimizer } = require('./render-optimizer.js');

/**
 * 性能优化管理器
 */
class PerformanceManager {
  constructor() {
    this.cache = advancedCacheManager;
    this.database = databaseOptimizer;
    this.render = renderOptimizer;
    this.isInitialized = false;
    this.performanceMetrics = {
      pageLoadTimes: new Map(),
      apiResponseTimes: new Map(),
      renderTimes: new Map(),
      cacheHitRates: new Map()
    };
  }

  /**
   * 初始化性能优化系统
   */
  async initialize(config = {}) {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('正在初始化性能优化系统...');

      // 预热关键缓存
      await this.warmupCaches(config.warmupCaches || []);

      // 启动性能监控
      this.startPerformanceMonitoring();

      this.isInitialized = true;
      console.log('性能优化系统初始化完成');

      return {
        success: true,
        message: '性能优化系统初始化成功'
      };

    } catch (error) {
      console.error('性能优化系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 预热缓存
   */
  async warmupCaches(cacheConfigs) {
    if (cacheConfigs.length === 0) {
      // 默认预热配置
      cacheConfigs = [
        {
          key: 'user_profile',
          fetchFunction: () => this.fetchUserProfile(),
          ttl: 600000 // 10分钟
        },
        {
          key: 'system_config',
          fetchFunction: () => this.fetchSystemConfig(),
          ttl: 1800000 // 30分钟
        },
        {
          key: 'common_data',
          fetchFunction: () => this.fetchCommonData(),
          ttl: 300000 // 5分钟
        }
      ];
    }

    await this.cache.warmup(cacheConfigs);
  }

  /**
   * 启动性能监控
   */
  startPerformanceMonitoring() {
    // 监控页面加载性能
    this.monitorPagePerformance();
    
    // 监控API性能
    this.monitorAPIPerformance();
    
    // 监控渲染性能
    this.monitorRenderPerformance();
    
    // 定期生成性能报告
    setInterval(() => {
      this.generatePerformanceReport();
    }, 300000); // 5分钟生成一次报告
  }

  /**
   * 监控页面性能
   */
  monitorPagePerformance() {
    const originalPage = Page;
    const self = this;

    Page = function(options) {
      const originalOnLoad = options.onLoad || function() {};
      const originalOnShow = options.onShow || function() {};

      options.onLoad = function(...args) {
        const startTime = Date.now();
        this._pageLoadStart = startTime;
        
        const result = originalOnLoad.apply(this, args);
        
        // 添加性能优化方法到页面实例
        this.optimizedSetData = (data, options) => {
          return self.render.batchSetData(this, data, options);
        };
        
        this.optimizedRequest = (key, fetchFunction, options) => {
          return self.cache.get(key, fetchFunction, options);
        };
        
        return result;
      };

      options.onShow = function(...args) {
        if (this._pageLoadStart) {
          const loadTime = Date.now() - this._pageLoadStart;
          self.recordPageLoadTime(this.route || 'unknown', loadTime);
        }
        
        return originalOnShow.apply(this, args);
      };

      return originalPage(options);
    };
  }

  /**
   * 监控API性能
   */
  monitorAPIPerformance() {
    // 这里可以拦截wx.request或其他API调用
    if (typeof wx !== 'undefined' && wx.request) {
      const originalRequest = wx.request;
      const self = this;

      wx.request = function(options) {
        const startTime = Date.now();
        const url = options.url;

        const originalSuccess = options.success || function() {};
        const originalFail = options.fail || function() {};

        options.success = function(res) {
          const responseTime = Date.now() - startTime;
          self.recordAPIResponseTime(url, responseTime, true);
          return originalSuccess(res);
        };

        options.fail = function(error) {
          const responseTime = Date.now() - startTime;
          self.recordAPIResponseTime(url, responseTime, false);
          return originalFail(error);
        };

        return originalRequest(options);
      };
    }
  }

  /**
   * 监控渲染性能
   */
  monitorRenderPerformance() {
    // 监控setData性能
    const self = this;
    
    // 这里可以通过代理或其他方式监控setData调用
    // 由于小程序限制，这里提供一个示例实现
  }

  /**
   * 记录页面加载时间
   */
  recordPageLoadTime(route, loadTime) {
    if (!this.performanceMetrics.pageLoadTimes.has(route)) {
      this.performanceMetrics.pageLoadTimes.set(route, []);
    }
    
    const times = this.performanceMetrics.pageLoadTimes.get(route);
    times.push({
      time: loadTime,
      timestamp: Date.now()
    });
    
    // 只保留最近50次记录
    if (times.length > 50) {
      times.shift();
    }

    console.log(`[性能监控] 页面加载: ${route}, 耗时: ${loadTime}ms`);
  }

  /**
   * 记录API响应时间
   */
  recordAPIResponseTime(url, responseTime, success) {
    const key = this.extractAPIKey(url);
    
    if (!this.performanceMetrics.apiResponseTimes.has(key)) {
      this.performanceMetrics.apiResponseTimes.set(key, []);
    }
    
    const times = this.performanceMetrics.apiResponseTimes.get(key);
    times.push({
      time: responseTime,
      success: success,
      timestamp: Date.now()
    });
    
    // 只保留最近100次记录
    if (times.length > 100) {
      times.shift();
    }

    if (responseTime > 3000) { // 超过3秒的慢请求
      console.warn(`[性能监控] 慢API请求: ${key}, 耗时: ${responseTime}ms`);
    }
  }

  /**
   * 提取API键名
   */
  extractAPIKey(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.pathname;
    } catch (error) {
      return url;
    }
  }

  /**
   * 生成性能报告
   */
  generatePerformanceReport() {
    const report = {
      timestamp: new Date().toISOString(),
      pagePerformance: this.analyzePagePerformance(),
      apiPerformance: this.analyzeAPIPerformance(),
      cachePerformance: this.analyzeCachePerformance(),
      renderPerformance: this.analyzeRenderPerformance(),
      recommendations: this.generateRecommendations()
    };

    console.log('[性能报告]', report);
    
    // 可以将报告发送到监控系统
    this.sendPerformanceReport(report);
    
    return report;
  }

  /**
   * 分析页面性能
   */
  analyzePagePerformance() {
    const analysis = {};
    
    for (const [route, times] of this.performanceMetrics.pageLoadTimes.entries()) {
      if (times.length === 0) continue;
      
      const loadTimes = times.map(t => t.time);
      analysis[route] = {
        avgLoadTime: this.calculateAverage(loadTimes),
        maxLoadTime: Math.max(...loadTimes),
        minLoadTime: Math.min(...loadTimes),
        sampleCount: times.length,
        recentTrend: this.calculateTrend(times.slice(-10))
      };
    }
    
    return analysis;
  }

  /**
   * 分析API性能
   */
  analyzeAPIPerformance() {
    const analysis = {};
    
    for (const [api, times] of this.performanceMetrics.apiResponseTimes.entries()) {
      if (times.length === 0) continue;
      
      const responseTimes = times.map(t => t.time);
      const successCount = times.filter(t => t.success).length;
      
      analysis[api] = {
        avgResponseTime: this.calculateAverage(responseTimes),
        maxResponseTime: Math.max(...responseTimes),
        minResponseTime: Math.min(...responseTimes),
        successRate: ((successCount / times.length) * 100).toFixed(2) + '%',
        sampleCount: times.length,
        recentTrend: this.calculateTrend(times.slice(-20))
      };
    }
    
    return analysis;
  }

  /**
   * 分析缓存性能
   */
  analyzeCachePerformance() {
    return this.cache.getStats();
  }

  /**
   * 分析渲染性能
   */
  analyzeRenderPerformance() {
    return this.render.getRenderStats();
  }

  /**
   * 生成优化建议
   */
  generateRecommendations() {
    const recommendations = [];
    
    // 检查页面加载性能
    const pageAnalysis = this.analyzePagePerformance();
    for (const [route, stats] of Object.entries(pageAnalysis)) {
      if (stats.avgLoadTime > 2000) {
        recommendations.push({
          type: 'page_optimization',
          priority: 'high',
          message: `页面 ${route} 平均加载时间过长 (${stats.avgLoadTime}ms)，建议优化`,
          suggestions: [
            '减少首屏数据量',
            '启用懒加载',
            '优化图片资源',
            '使用缓存策略'
          ]
        });
      }
    }
    
    // 检查API性能
    const apiAnalysis = this.analyzeAPIPerformance();
    for (const [api, stats] of Object.entries(apiAnalysis)) {
      if (stats.avgResponseTime > 3000) {
        recommendations.push({
          type: 'api_optimization',
          priority: 'medium',
          message: `API ${api} 响应时间过长 (${stats.avgResponseTime}ms)`,
          suggestions: [
            '添加缓存策略',
            '优化数据库查询',
            '考虑数据分页',
            '使用CDN加速'
          ]
        });
      }
    }
    
    // 检查缓存命中率
    const cacheStats = this.analyzeCachePerformance();
    const hitRate = parseFloat(cacheStats.hitRate);
    if (hitRate < 60) {
      recommendations.push({
        type: 'cache_optimization',
        priority: 'medium',
        message: `缓存命中率较低 (${cacheStats.hitRate})`,
        suggestions: [
          '调整缓存策略',
          '增加缓存时间',
          '预热关键数据',
          '优化缓存键设计'
        ]
      });
    }
    
    return recommendations;
  }

  /**
   * 计算平均值
   */
  calculateAverage(numbers) {
    if (numbers.length === 0) return 0;
    return Math.round(numbers.reduce((sum, num) => sum + num, 0) / numbers.length);
  }

  /**
   * 计算趋势
   */
  calculateTrend(dataPoints) {
    if (dataPoints.length < 2) return 'stable';
    
    const recent = dataPoints.slice(-5);
    const earlier = dataPoints.slice(-10, -5);
    
    if (recent.length === 0 || earlier.length === 0) return 'stable';
    
    const recentAvg = this.calculateAverage(recent.map(d => d.time));
    const earlierAvg = this.calculateAverage(earlier.map(d => d.time));
    
    const change = ((recentAvg - earlierAvg) / earlierAvg) * 100;
    
    if (change > 10) return 'degrading';
    if (change < -10) return 'improving';
    return 'stable';
  }

  /**
   * 发送性能报告
   */
  async sendPerformanceReport(report) {
    try {
      if (typeof wx !== 'undefined' && wx.request) {
        wx.request({
          url: '/api/v2/monitoring/performance-report',
          method: 'POST',
          data: report,
          fail: (error) => {
            console.warn('发送性能报告失败:', error);
          }
        });
      }
    } catch (error) {
      console.warn('发送性能报告异常:', error);
    }
  }

  /**
   * 获取默认数据的模拟方法
   */
  async fetchUserProfile() {
    // 模拟获取用户配置
    return { id: 1, name: 'User', preferences: {} };
  }

  async fetchSystemConfig() {
    // 模拟获取系统配置
    return { version: '1.0', features: [] };
  }

  async fetchCommonData() {
    // 模拟获取通用数据
    return { categories: [], constants: {} };
  }

  /**
   * 获取系统状态
   */
  getSystemStatus() {
    return {
      initialized: this.isInitialized,
      cache: this.cache.getStats(),
      database: this.database.getQueryStats(),
      render: this.render.getRenderStats(),
      performance: {
        pageMetrics: this.performanceMetrics.pageLoadTimes.size,
        apiMetrics: this.performanceMetrics.apiResponseTimes.size
      }
    };
  }

  /**
   * 销毁性能优化系统
   */
  destroy() {
    this.render.cleanup();
    this.database.clear();
    this.cache.clear();
    this.performanceMetrics.pageLoadTimes.clear();
    this.performanceMetrics.apiResponseTimes.clear();
    this.isInitialized = false;
    console.log('性能优化系统已销毁');
  }
}

// 创建全局实例
const performanceManager = new PerformanceManager();

// 便捷API
const PerformanceAPI = {
  // 初始化
  init: (config) => performanceManager.initialize(config),
  
  // 缓存操作
  cache: (key, fetchFunction, options) => performanceManager.cache.get(key, fetchFunction, options),
  
  // 数据库查询优化
  optimizeQuery: (key, queryFunction, options) => performanceManager.database.optimizeQuery(key, queryFunction, options),
  
  // 渲染优化
  batchSetData: (page, data, options) => performanceManager.render.batchSetData(page, data, options),
  
  // 获取性能报告
  getReport: () => performanceManager.generatePerformanceReport(),
  
  // 获取系统状态
  getStatus: () => performanceManager.getSystemStatus(),
  
  // 销毁
  destroy: () => performanceManager.destroy()
};

// 主要导出
module.exports = {
  // 核心类
  PerformanceManager,
  
  // 全局实例
  performanceManager,
  
  // 子模块
  advancedCacheManager,
  databaseOptimizer,
  renderOptimizer,
  
  // 便捷API
  PerformanceAPI,
  
  // 便捷方法
  ...PerformanceAPI,
  
  // 重构信息
  __version: '2.0',
  __modules: [
    'utils/performance/advanced-cache-manager.js',
    'utils/performance/database-optimizer.js',
    'utils/performance/render-optimizer.js'
  ]
};
