/**
 * 数据库查询优化器
 * Database Query Optimizer
 */

class DatabaseOptimizer {
  constructor() {
    this.queryCache = new Map();
    this.queryStats = new Map();
    this.slowQueryThreshold = 1000; // 1秒
    this.batchSize = 100;
    this.maxCacheSize = 500;
  }

  /**
   * 优化查询执行
   */
  async optimizeQuery(queryKey, queryFunction, options = {}) {
    const {
      useCache = true,
      cacheTTL = 300000, // 5分钟
      enableBatch = false,
      batchKey = null,
      timeout = 10000
    } = options;

    const startTime = Date.now();

    try {
      // 1. 检查缓存
      if (useCache) {
        const cached = this.getFromCache(queryKey);
        if (cached) {
          this.recordQueryStats(queryKey, Date.now() - startTime, true);
          return cached;
        }
      }

      // 2. 批量查询优化
      if (enableBatch && batchKey) {
        return this.handleBatchQuery(batchKey, queryKey, queryFunction, options);
      }

      // 3. 执行查询
      const result = await this.executeWithTimeout(queryFunction, timeout);
      
      // 4. 缓存结果
      if (useCache && result) {
        this.setToCache(queryKey, result, cacheTTL);
      }

      // 5. 记录统计
      const duration = Date.now() - startTime;
      this.recordQueryStats(queryKey, duration, false);

      // 6. 检查慢查询
      if (duration > this.slowQueryThreshold) {
        this.reportSlowQuery(queryKey, duration);
      }

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordQueryStats(queryKey, duration, false, error);
      throw error;
    }
  }

  /**
   * 批量查询处理
   */
  async handleBatchQuery(batchKey, queryKey, queryFunction, options) {
    if (!this.batchQueues) {
      this.batchQueues = new Map();
    }

    if (!this.batchQueues.has(batchKey)) {
      this.batchQueues.set(batchKey, {
        queries: [],
        timer: null,
        promise: null
      });
    }

    const batch = this.batchQueues.get(batchKey);
    
    return new Promise((resolve, reject) => {
      // 添加到批次队列
      batch.queries.push({
        key: queryKey,
        function: queryFunction,
        resolve,
        reject,
        options
      });

      // 如果达到批次大小或设置定时器
      if (batch.queries.length >= this.batchSize) {
        this.executeBatch(batchKey);
      } else if (!batch.timer) {
        batch.timer = setTimeout(() => {
          this.executeBatch(batchKey);
        }, 50); // 50ms 批次延迟
      }
    });
  }

  /**
   * 执行批次查询
   */
  async executeBatch(batchKey) {
    const batch = this.batchQueues.get(batchKey);
    if (!batch || batch.queries.length === 0) {
      return;
    }

    // 清除定时器
    if (batch.timer) {
      clearTimeout(batch.timer);
      batch.timer = null;
    }

    const queries = batch.queries.splice(0);
    
    try {
      // 并行执行所有查询
      const results = await Promise.allSettled(
        queries.map(query => query.function())
      );

      // 处理结果
      results.forEach((result, index) => {
        const query = queries[index];
        
        if (result.status === 'fulfilled') {
          // 缓存成功结果
          if (query.options.useCache !== false) {
            this.setToCache(query.key, result.value, query.options.cacheTTL);
          }
          query.resolve(result.value);
        } else {
          query.reject(result.reason);
        }
      });

    } catch (error) {
      // 批次执行失败，拒绝所有查询
      queries.forEach(query => {
        query.reject(error);
      });
    }
  }

  /**
   * 带超时的查询执行
   */
  async executeWithTimeout(queryFunction, timeout) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`查询超时: ${timeout}ms`));
      }, timeout);

      queryFunction()
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * 分页查询优化
   */
  async optimizePaginatedQuery(baseQueryKey, pageSize, currentPage, totalQueryFunction, dataQueryFunction, options = {}) {
    const {
      prefetchPages = 1,
      useCache = true,
      cacheTTL = 300000
    } = options;

    // 1. 获取总数（优先从缓存）
    const totalKey = `${baseQueryKey}_total`;
    const total = await this.optimizeQuery(totalKey, totalQueryFunction, {
      useCache,
      cacheTTL: cacheTTL * 2 // 总数缓存时间更长
    });

    // 2. 获取当前页数据
    const currentPageKey = `${baseQueryKey}_page_${currentPage}`;
    const currentData = await this.optimizeQuery(currentPageKey, 
      () => dataQueryFunction(currentPage, pageSize), 
      { useCache, cacheTTL }
    );

    // 3. 预取下一页数据
    if (prefetchPages > 0) {
      const totalPages = Math.ceil(total / pageSize);
      const prefetchPromises = [];

      for (let i = 1; i <= prefetchPages; i++) {
        const nextPage = currentPage + i;
        if (nextPage <= totalPages) {
          const nextPageKey = `${baseQueryKey}_page_${nextPage}`;
          
          // 异步预取，不等待结果
          prefetchPromises.push(
            this.optimizeQuery(nextPageKey, 
              () => dataQueryFunction(nextPage, pageSize), 
              { useCache, cacheTTL }
            ).catch(error => {
              console.warn(`预取页面 ${nextPage} 失败:`, error);
            })
          );
        }
      }

      // 不等待预取完成
      Promise.allSettled(prefetchPromises);
    }

    return {
      data: currentData,
      total,
      currentPage,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  /**
   * 查询结果聚合优化
   */
  async optimizeAggregateQuery(queries, options = {}) {
    const {
      useCache = true,
      cacheTTL = 300000,
      enableParallel = true
    } = options;

    if (enableParallel) {
      // 并行执行所有查询
      const results = await Promise.allSettled(
        queries.map(query => 
          this.optimizeQuery(query.key, query.function, {
            useCache,
            cacheTTL,
            ...query.options
          })
        )
      );

      // 处理结果
      const aggregatedResult = {};
      results.forEach((result, index) => {
        const query = queries[index];
        if (result.status === 'fulfilled') {
          aggregatedResult[query.resultKey || query.key] = result.value;
        } else {
          console.error(`聚合查询失败 (${query.key}):`, result.reason);
          aggregatedResult[query.resultKey || query.key] = null;
        }
      });

      return aggregatedResult;
    } else {
      // 串行执行
      const result = {};
      for (const query of queries) {
        try {
          result[query.resultKey || query.key] = await this.optimizeQuery(
            query.key, 
            query.function, 
            { useCache, cacheTTL, ...query.options }
          );
        } catch (error) {
          console.error(`聚合查询失败 (${query.key}):`, error);
          result[query.resultKey || query.key] = null;
        }
      }
      return result;
    }
  }

  /**
   * 缓存操作
   */
  getFromCache(key) {
    const item = this.queryCache.get(key);
    if (!item) {
      return null;
    }

    if (Date.now() > item.expireTime) {
      this.queryCache.delete(key);
      return null;
    }

    return item.data;
  }

  setToCache(key, data, ttl) {
    // 检查缓存大小
    if (this.queryCache.size >= this.maxCacheSize) {
      this.evictCache();
    }

    this.queryCache.set(key, {
      data,
      expireTime: Date.now() + ttl,
      createdAt: Date.now()
    });
  }

  evictCache() {
    // LRU淘汰策略
    const entries = Array.from(this.queryCache.entries());
    entries.sort((a, b) => a[1].createdAt - b[1].createdAt);
    
    // 删除最旧的25%
    const evictCount = Math.ceil(entries.length * 0.25);
    for (let i = 0; i < evictCount; i++) {
      this.queryCache.delete(entries[i][0]);
    }
  }

  /**
   * 记录查询统计
   */
  recordQueryStats(queryKey, duration, fromCache, error = null) {
    if (!this.queryStats.has(queryKey)) {
      this.queryStats.set(queryKey, {
        totalCalls: 0,
        cacheHits: 0,
        totalDuration: 0,
        avgDuration: 0,
        maxDuration: 0,
        minDuration: Infinity,
        errors: 0,
        lastCall: null
      });
    }

    const stats = this.queryStats.get(queryKey);
    stats.totalCalls++;
    stats.lastCall = Date.now();

    if (fromCache) {
      stats.cacheHits++;
    } else {
      stats.totalDuration += duration;
      stats.avgDuration = stats.totalDuration / (stats.totalCalls - stats.cacheHits);
      stats.maxDuration = Math.max(stats.maxDuration, duration);
      stats.minDuration = Math.min(stats.minDuration, duration);
    }

    if (error) {
      stats.errors++;
    }
  }

  /**
   * 报告慢查询
   */
  reportSlowQuery(queryKey, duration) {
    console.warn(`[数据库优化] 慢查询检测: ${queryKey}, 耗时: ${duration}ms`);
    
    // 可以集成到监控系统
    if (typeof wx !== 'undefined' && wx.request) {
      wx.request({
        url: '/api/v2/monitoring/slow-queries',
        method: 'POST',
        data: {
          queryKey,
          duration,
          timestamp: Date.now(),
          threshold: this.slowQueryThreshold
        },
        fail: (error) => {
          console.warn('上报慢查询失败:', error);
        }
      });
    }
  }

  /**
   * 获取查询统计报告
   */
  getQueryStats() {
    const stats = {};
    
    for (const [key, stat] of this.queryStats.entries()) {
      stats[key] = {
        ...stat,
        cacheHitRate: stat.totalCalls > 0 ? 
          ((stat.cacheHits / stat.totalCalls) * 100).toFixed(2) + '%' : '0%',
        errorRate: stat.totalCalls > 0 ? 
          ((stat.errors / stat.totalCalls) * 100).toFixed(2) + '%' : '0%'
      };
    }

    return {
      queries: stats,
      summary: {
        totalQueries: this.queryStats.size,
        cacheSize: this.queryCache.size,
        avgCacheHitRate: this.calculateAvgCacheHitRate()
      }
    };
  }

  calculateAvgCacheHitRate() {
    let totalCalls = 0;
    let totalHits = 0;

    for (const stat of this.queryStats.values()) {
      totalCalls += stat.totalCalls;
      totalHits += stat.cacheHits;
    }

    return totalCalls > 0 ? 
      ((totalHits / totalCalls) * 100).toFixed(2) + '%' : '0%';
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.queryCache.entries()) {
      if (now > item.expireTime) {
        this.queryCache.delete(key);
      }
    }
  }

  /**
   * 清空所有缓存和统计
   */
  clear() {
    this.queryCache.clear();
    this.queryStats.clear();
    if (this.batchQueues) {
      this.batchQueues.clear();
    }
  }
}

// 创建全局实例
const databaseOptimizer = new DatabaseOptimizer();

module.exports = {
  DatabaseOptimizer,
  databaseOptimizer
};
