/**
 * 优化版API客户端 - 目标响应时间 < 300ms
 */

// 兼容性导入
let PerformanceAPI;
try {
  PerformanceAPI = require('./performance/index.js').PerformanceAPI;
} catch (error) {
  // 如果性能模块不存在，使用简单的替代实现
  PerformanceAPI = {
    recordMetric: (type, duration) => {
      console.log(`📊 性能指标: ${type} - ${duration}ms`);
    }
  };
}

const { ENDPOINTS } = require('../constants/core.constants.js');

class OptimizedAPIClient {
  constructor() {
    this.baseURL = ENDPOINTS.AUTH.LOGIN.split('/auth')[0];
    this.timeout = 8000; // 8秒超时
    this.retryCount = 2;
    this.cache = new Map();
    this.requestQueue = new Map();
    this.performanceMonitor = PerformanceAPI;
  }

  /**
   * 智能请求方法 - 支持缓存、去重、重试
   */
  async request(options) {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(options);
    
    try {
      // 1. 检查缓存
      if (options.useCache !== false) {
        const cached = await this.getFromCache(cacheKey);
        if (cached) {
          this.recordPerformance('cache_hit', Date.now() - startTime);
          return cached;
        }
      }
      
      // 2. 请求去重
      if (this.requestQueue.has(cacheKey)) {
        return await this.requestQueue.get(cacheKey);
      }
      
      // 3. 发起请求
      const requestPromise = this.executeRequest(options);
      this.requestQueue.set(cacheKey, requestPromise);
      
      const result = await requestPromise;
      
      // 4. 缓存结果
      if (options.useCache !== false && result.success) {
        await this.setCache(cacheKey, result, options.cacheTTL || 300000);
      }
      
      this.recordPerformance('api_request', Date.now() - startTime);
      return result;
      
    } catch (error) {
      this.recordPerformance('api_error', Date.now() - startTime);
      throw error;
    } finally {
      this.requestQueue.delete(cacheKey);
    }
  }

  /**
   * 执行实际请求
   */
  async executeRequest(options) {
    const { url, method = 'GET', data, headers = {} } = options;
    
    return new Promise((resolve, reject) => {
      const requestOptions = {
        url: url.startsWith('http') ? url : `${this.baseURL}${url}`,
        method,
        data,
        header: {
          'Content-Type': 'application/json',
          ...headers
        },
        timeout: this.timeout,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve({
              success: true,
              data: res.data,
              statusCode: res.statusCode
            });
          } else {
            resolve({
              success: false,
              error: `HTTP ${res.statusCode}`,
              statusCode: res.statusCode
            });
          }
        },
        fail: (error) => {
          reject(new Error(`请求失败: ${error.errMsg || error.message}`));
        }
      };
      
      wx.request(requestOptions);
    });
  }

  /**
   * 批量请求
   */
  async batchRequest(requests) {
    const startTime = Date.now();
    
    try {
      const results = await Promise.allSettled(
        requests.map(req => this.request(req))
      );
      
      this.recordPerformance('batch_request', Date.now() - startTime);
      return results;
    } catch (error) {
      this.recordPerformance('batch_error', Date.now() - startTime);
      throw error;
    }
  }

  /**
   * 缓存管理
   */
  generateCacheKey(options) {
    return `${options.method || 'GET'}:${options.url}:${JSON.stringify(options.data || {})}`;
  }

  async getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && cached.expiry > Date.now()) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  async setCache(key, data, ttl) {
    this.cache.set(key, {
      data,
      expiry: Date.now() + ttl
    });
    
    // 限制缓存大小
    if (this.cache.size > 100) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
  }

  /**
   * 性能记录
   */
  recordPerformance(type, duration) {
    this.performanceMonitor.recordMetric(type, duration);
    
    if (duration > 300) {
      console.warn(`⚠️ 慢请求检测: ${type} 耗时 ${duration}ms`);
    }
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.cache.clear();
  }
}

// 创建全局实例
const apiClient = new OptimizedAPIClient();

// 便捷方法
const API = {
  get: (url, options = {}) => apiClient.request({ url, method: 'GET', ...options }),
  post: (url, data, options = {}) => apiClient.request({ url, method: 'POST', data, ...options }),
  put: (url, data, options = {}) => apiClient.request({ url, method: 'PUT', data, ...options }),
  delete: (url, options = {}) => apiClient.request({ url, method: 'DELETE', ...options }),
  batch: (requests) => apiClient.batchRequest(requests),
  clearCache: () => apiClient.clearCache()
};

module.exports = {
  OptimizedAPIClient,
  apiClient,
  API
};