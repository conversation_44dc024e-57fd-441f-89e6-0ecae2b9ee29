/**
 * 微信小程序 Polyfills
 * 提供在小程序环境中缺失的Web API实现
 */

class MiniProgramPolyfills {
  /**
   * 初始化所有polyfills
   */
  static init() {
    this.initBase64();
    this.initConsole();
    this.initURL();
    this.initLocalStorage();
    console.log('🔧 微信小程序 Polyfills 初始化完成');
  }

  /**
   * Base64 编码/解码 polyfill
   */
  static initBase64() {
    if (typeof global !== 'undefined' && !global.btoa) {
      global.btoa = this.btoa;
      global.atob = this.atob;
    }
    
    if (typeof window !== 'undefined' && !window.btoa) {
      window.btoa = this.btoa;
      window.atob = this.atob;
    }
  }

  /**
   * Base64 编码实现
   */
  static btoa(str) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    let i = 0;
    
    // 将字符串转换为UTF-8字节
    const utf8Bytes = this.stringToUTF8Bytes(str);
    
    while (i < utf8Bytes.length) {
      const a = utf8Bytes[i++] || 0;
      const b = utf8Bytes[i++] || 0;
      const c = utf8Bytes[i++] || 0;
      
      const bitmap = (a << 16) | (b << 8) | c;
      
      result += chars.charAt((bitmap >> 18) & 63);
      result += chars.charAt((bitmap >> 12) & 63);
      result += i - 2 < utf8Bytes.length ? chars.charAt((bitmap >> 6) & 63) : '=';
      result += i - 1 < utf8Bytes.length ? chars.charAt(bitmap & 63) : '=';
    }
    
    return result;
  }

  /**
   * Base64 解码实现
   */
  static atob(base64) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    let i = 0;
    
    // 移除非base64字符
    base64 = base64.replace(/[^A-Za-z0-9+/]/g, '');
    
    while (i < base64.length) {
      const encoded1 = chars.indexOf(base64.charAt(i++));
      const encoded2 = chars.indexOf(base64.charAt(i++));
      const encoded3 = chars.indexOf(base64.charAt(i++));
      const encoded4 = chars.indexOf(base64.charAt(i++));
      
      const bitmap = (encoded1 << 18) | (encoded2 << 12) | (encoded3 << 6) | encoded4;
      
      result += String.fromCharCode((bitmap >> 16) & 255);
      if (encoded3 !== 64) result += String.fromCharCode((bitmap >> 8) & 255);
      if (encoded4 !== 64) result += String.fromCharCode(bitmap & 255);
    }
    
    return result;
  }

  /**
   * 字符串转UTF-8字节数组
   */
  static stringToUTF8Bytes(str) {
    const bytes = [];
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i);
      if (code < 0x80) {
        bytes.push(code);
      } else if (code < 0x800) {
        bytes.push(0xC0 | (code >> 6));
        bytes.push(0x80 | (code & 0x3F));
      } else if (code < 0x10000) {
        bytes.push(0xE0 | (code >> 12));
        bytes.push(0x80 | ((code >> 6) & 0x3F));
        bytes.push(0x80 | (code & 0x3F));
      }
    }
    return bytes;
  }

  /**
   * 控制台增强 polyfill
   */
  static initConsole() {
    if (typeof console !== 'undefined') {
      // 增强console.table (小程序不支持)
      if (!console.table) {
        console.table = function(data) {
          if (Array.isArray(data)) {
            data.forEach((item, index) => {
              console.log(`[${index}]`, item);
            });
          } else if (typeof data === 'object') {
            Object.keys(data).forEach(key => {
              console.log(`[${key}]`, data[key]);
            });
          } else {
            console.log(data);
          }
        };
      }

      // 增强console.group (小程序不支持)
      if (!console.group) {
        console.group = function(label) {
          console.log(`📁 ${label || 'Group'}`);
        };
        console.groupEnd = function() {
          console.log('📁 Group End');
        };
        console.groupCollapsed = console.group;
      }
    }
  }

  /**
   * URL polyfill (简化版)
   */
  static initURL() {
    if (typeof global !== 'undefined' && !global.URL) {
      global.URL = this.URLPolyfill;
    }
  }

  /**
   * 简化的URL构造函数
   */
  static URLPolyfill = class URL {
    constructor(url, base) {
      if (base) {
        url = this.resolveURL(base, url);
      }
      
      const match = url.match(/^(https?:)\/\/([^\/]+)(\/[^?#]*)?(\?[^#]*)?(#.*)?$/);
      if (match) {
        this.protocol = match[1];
        this.host = match[2];
        this.pathname = match[3] || '/';
        this.search = match[4] || '';
        this.hash = match[5] || '';
        this.href = url;
      } else {
        throw new Error('Invalid URL');
      }
    }

    resolveURL(base, relative) {
      if (relative.startsWith('http')) return relative;
      if (relative.startsWith('/')) return base.replace(/\/[^\/]*$/, '') + relative;
      return base.replace(/\/[^\/]*$/, '/') + relative;
    }

    toString() {
      return this.href;
    }
  };

  /**
   * LocalStorage polyfill (使用微信小程序存储)
   */
  static initLocalStorage() {
    if (typeof wx !== 'undefined' && typeof localStorage === 'undefined') {
      global.localStorage = {
        getItem: (key) => {
          try {
            return wx.getStorageSync(key) || null;
          } catch (error) {
            return null;
          }
        },
        
        setItem: (key, value) => {
          try {
            wx.setStorageSync(key, value);
          } catch (error) {
            console.warn('localStorage.setItem failed:', error);
          }
        },
        
        removeItem: (key) => {
          try {
            wx.removeStorageSync(key);
          } catch (error) {
            console.warn('localStorage.removeItem failed:', error);
          }
        },
        
        clear: () => {
          try {
            wx.clearStorageSync();
          } catch (error) {
            console.warn('localStorage.clear failed:', error);
          }
        },
        
        key: (index) => {
          try {
            const info = wx.getStorageInfoSync();
            return info.keys[index] || null;
          } catch (error) {
            return null;
          }
        },
        
        get length() {
          try {
            const info = wx.getStorageInfoSync();
            return info.keys.length;
          } catch (error) {
            return 0;
          }
        }
      };
    }
  }

  /**
   * 获取安全的系统信息
   */
  static getSafeSystemInfo() {
    if (typeof wx === 'undefined') {
      return { platform: 'unknown' };
    }

    try {
      // 优先使用新API
      let info = {};
      
      if (typeof wx.getDeviceInfo === 'function') {
        info = { ...info, ...wx.getDeviceInfo() };
      }
      
      if (typeof wx.getAppBaseInfo === 'function') {
        info = { ...info, ...wx.getAppBaseInfo() };
      }
      
      if (typeof wx.getWindowInfo === 'function') {
        info = { ...info, ...wx.getWindowInfo() };
      }
      
      if (typeof wx.getSystemSetting === 'function') {
        info = { ...info, ...wx.getSystemSetting() };
      }
      
      // 如果新API都不可用，使用旧API
      if (Object.keys(info).length === 0) {
        info = wx.getSystemInfoSync();
      }
      
      return info;
    } catch (error) {
      console.warn('获取系统信息失败:', error);
      return { platform: 'miniprogram', error: error.message };
    }
  }

  /**
   * 检测是否为开发环境
   */
  static isDevelopment() {
    const systemInfo = this.getSafeSystemInfo();
    return systemInfo.platform === 'devtools';
  }
}

// 自动初始化
if (typeof wx !== 'undefined') {
  MiniProgramPolyfills.init();
}

module.exports = MiniProgramPolyfills;
