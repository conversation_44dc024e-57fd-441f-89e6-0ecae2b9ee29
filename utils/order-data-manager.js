/**
 * 订单数据管理器
 * 确保商城和个人中心订单数据一致性
 */

class OrderDataManager {
  /**
   * 创建订单
   */
  static createOrder(orderData) {
    const orderId = 'ORDER' + Date.now();
    const order = {
      id: orderId,
      orderNumber: orderId,
      ...orderData,
      createTime: new Date().toISOString(),
      status: 'pending',
      statusText: '待付款'
    };
    
    // 保存到本地存储
    const orders = this.getOrders();
    orders.unshift(order);
    wx.setStorageSync('orders', orders);
    
    console.log('订单创建成功:', order);
    return order;
  }

  /**
   * 获取所有订单
   */
  static getOrders() {
    return wx.getStorageSync('orders') || [];
  }

  /**
   * 更新订单状态
   */
  static updateOrderStatus(orderId, status) {
    const orders = this.getOrders();
    const orderIndex = orders.findIndex(order => order.id === orderId);
    
    if (orderIndex > -1) {
      orders[orderIndex].status = status;
      orders[orderIndex].statusText = this.getStatusText(status);
      orders[orderIndex].updateTime = new Date().toISOString();
      wx.setStorageSync('orders', orders);
      
      console.log('订单状态更新成功:', orderId, status);
      return true;
    }
    
    console.error('订单不存在:', orderId);
    return false;
  }

  /**
   * 获取状态文本
   */
  static getStatusText(status) {
    const statusMap = {
      'pending': '待付款',
      'paid': '已付款',
      'shipped': '已发货',
      'delivered': '已送达',
      'completed': '已完成',
      'cancelled': '已取消',
      'refunded': '已退款'
    };
    
    return statusMap[status] || '未知状态';
  }

  /**
   * 获取单个订单
   */
  static getOrder(orderId) {
    const orders = this.getOrders();
    return orders.find(order => order.id === orderId);
  }

  /**
   * 删除订单
   */
  static deleteOrder(orderId) {
    const orders = this.getOrders();
    const filteredOrders = orders.filter(order => order.id !== orderId);
    wx.setStorageSync('orders', filteredOrders);
    
    console.log('订单删除成功:', orderId);
    return true;
  }

  /**
   * 从商品信息创建订单数据
   */
  static createOrderFromGoods(goods, quantity, address, options = {}) {
    const orderData = {
      goods: Array.isArray(goods) ? goods : [goods],
      totalAmount: this.calculateTotalAmount(goods, quantity),
      quantity: quantity,
      address: address,
      paymentMethod: options.paymentMethod || 'wechat',
      remark: options.remark || '',
      type: options.type || 'normal' // 'normal' | 'buynow' | 'cart'
    };
    
    return this.createOrder(orderData);
  }

  /**
   * 计算订单总金额
   */
  static calculateTotalAmount(goods, quantity) {
    if (Array.isArray(goods)) {
      return goods.reduce((total, item) => {
        return total + (item.price * (item.quantity || quantity || 1));
      }, 0);
    } else {
      return goods.price * (quantity || 1);
    }
  }

  /**
   * 获取订单统计信息
   */
  static getOrderStats() {
    const orders = this.getOrders();
    
    const stats = {
      total: orders.length,
      pending: 0,
      paid: 0,
      shipped: 0,
      delivered: 0,
      completed: 0,
      cancelled: 0
    };
    
    orders.forEach(order => {
      if (stats.hasOwnProperty(order.status)) {
        stats[order.status]++;
      }
    });
    
    return stats;
  }

  /**
   * 按状态筛选订单
   */
  static getOrdersByStatus(status) {
    const orders = this.getOrders();
    return orders.filter(order => order.status === status);
  }

  /**
   * 搜索订单
   */
  static searchOrders(keyword) {
    const orders = this.getOrders();
    return orders.filter(order => {
      return order.orderNumber.includes(keyword) ||
             order.goods.some(item => item.name.includes(keyword));
    });
  }

  /**
   * 清空所有订单（仅用于测试）
   */
  static clearAllOrders() {
    wx.removeStorageSync('orders');
    console.log('所有订单已清空');
  }

  /**
   * 模拟支付成功
   */
  static simulatePaymentSuccess(orderId) {
    return this.updateOrderStatus(orderId, 'paid');
  }

  /**
   * 模拟发货
   */
  static simulateShipment(orderId, trackingNumber = null) {
    const success = this.updateOrderStatus(orderId, 'shipped');
    
    if (success && trackingNumber) {
      const orders = this.getOrders();
      const orderIndex = orders.findIndex(order => order.id === orderId);
      if (orderIndex > -1) {
        orders[orderIndex].trackingNumber = trackingNumber;
        orders[orderIndex].shipTime = new Date().toISOString();
        wx.setStorageSync('orders', orders);
      }
    }
    
    return success;
  }

  /**
   * 确认收货
   */
  static confirmDelivery(orderId) {
    const success = this.updateOrderStatus(orderId, 'delivered');
    
    if (success) {
      const orders = this.getOrders();
      const orderIndex = orders.findIndex(order => order.id === orderId);
      if (orderIndex > -1) {
        orders[orderIndex].deliveryTime = new Date().toISOString();
        wx.setStorageSync('orders', orders);
      }
    }
    
    return success;
  }

  /**
   * 完成订单
   */
  static completeOrder(orderId) {
    const success = this.updateOrderStatus(orderId, 'completed');
    
    if (success) {
      const orders = this.getOrders();
      const orderIndex = orders.findIndex(order => order.id === orderId);
      if (orderIndex > -1) {
        orders[orderIndex].completeTime = new Date().toISOString();
        wx.setStorageSync('orders', orders);
      }
    }
    
    return success;
  }
}

module.exports = OrderDataManager;
