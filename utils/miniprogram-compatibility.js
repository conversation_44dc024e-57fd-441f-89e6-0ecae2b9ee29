/**
 * 微信小程序环境兼容性垫片
 * 提供 Node.js API 的模拟实现
 */

class MiniProgramCompatibility {
  /**
   * 检测是否在微信小程序环境
   */
  static isMiniProgram() {
    return typeof wx !== 'undefined' && typeof window === 'undefined';
  }

  /**
   * 检测是否在开发环境
   */
  static isDevelopment() {
    if (this.isMiniProgram()) {
      try {
        // 优先使用新API
        if (typeof wx.getDeviceInfo === 'function') {
          const deviceInfo = wx.getDeviceInfo();
          return deviceInfo.platform === 'devtools';
        } else {
          // 兼容旧API
          const systemInfo = wx.getSystemInfoSync();
          return systemInfo.platform === 'devtools';
        }
      } catch (error) {
        return false;
      }
    }
    return typeof process !== 'undefined' && process.env.NODE_ENV === 'development';
  }

  /**
   * 模拟内存使用情况
   */
  static getMockMemoryUsage() {
    return {
      rss: 50 * 1024 * 1024, // 50MB
      heapTotal: 30 * 1024 * 1024, // 30MB
      heapUsed: 20 * 1024 * 1024, // 20MB
      external: 5 * 1024 * 1024, // 5MB
      arrayBuffers: 1 * 1024 * 1024 // 1MB
    };
  }

  /**
   * 模拟CPU使用情况
   */
  static getMockCPUUsage() {
    return {
      user: Math.floor(Math.random() * 1000000),
      system: Math.floor(Math.random() * 500000)
    };
  }

  /**
   * 模拟运行时间
   */
  static getMockUptime() {
    // 返回一个模拟的运行时间（秒）
    return Math.floor(Date.now() / 1000) % 86400; // 当天的秒数
  }

  /**
   * 安全的控制台输出
   */
  static safeConsole(level, ...args) {
    if (this.isDevelopment()) {
      console[level](...args);
    }
  }

  /**
   * 环境信息获取
   */
  static getEnvironmentInfo() {
    if (this.isMiniProgram()) {
      try {
        let info = { platform: 'miniprogram' };

        // 使用新的API获取信息
        if (typeof wx.getDeviceInfo === 'function') {
          const deviceInfo = wx.getDeviceInfo();
          info = { ...info, ...deviceInfo };
        }

        if (typeof wx.getAppBaseInfo === 'function') {
          const appInfo = wx.getAppBaseInfo();
          info = { ...info, ...appInfo };
        }

        if (typeof wx.getWindowInfo === 'function') {
          const windowInfo = wx.getWindowInfo();
          info = { ...info, ...windowInfo };
        }

        // 如果新API不可用，使用旧API
        if (Object.keys(info).length === 1) {
          const systemInfo = wx.getSystemInfoSync();
          info = {
            platform: 'miniprogram',
            system: systemInfo.system,
            version: systemInfo.version,
            SDKVersion: systemInfo.SDKVersion,
            brand: systemInfo.brand,
            model: systemInfo.model
          };
        }

        return info;
      } catch (error) {
        return { platform: 'miniprogram', error: error.message };
      }
    } else {
      return {
        platform: 'nodejs',
        version: typeof process !== 'undefined' ? process.version : 'unknown',
        arch: typeof process !== 'undefined' ? process.arch : 'unknown'
      };
    }
  }
}

// 全局导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MiniProgramCompatibility;
} else if (typeof global !== 'undefined') {
  global.MiniProgramCompatibility = MiniProgramCompatibility;
}