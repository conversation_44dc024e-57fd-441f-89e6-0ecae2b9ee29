/**
 * API配置切换器
 * 支持开发模式和生产模式的API配置切换
 */

// 配置模式
const CONFIG_MODE = {
  DEVELOPMENT: 'development',
  MOCK: 'mock',
  PRODUCTION: 'production'
};

// 当前模式（可以通过小程序后台配置或本地存储设置）
let currentMode = CONFIG_MODE.MOCK; // 默认使用模拟数据模式

// 尝试从本地存储获取配置
try {
  const savedMode = wx.getStorageSync('api_config_mode');
  if (savedMode && Object.values(CONFIG_MODE).includes(savedMode)) {
    currentMode = savedMode;
  }
} catch (error) {
  console.log('获取API配置模式失败，使用默认模式');
}

// API配置
const API_CONFIGS = {
  [CONFIG_MODE.DEVELOPMENT]: {
    baseURL: 'http://localhost:3001',
    version: '/api/v2',
    timeout: 10000
  },
  [CONFIG_MODE.MOCK]: {
    baseURL: '', // 模拟数据不需要baseURL
    version: '',
    timeout: 0,
    useMockData: true
  },
  [CONFIG_MODE.PRODUCTION]: {
    baseURL: 'https://api.zhihuiyange.com',
    version: '/api/v2',
    timeout: 15000
  }
};

// 获取当前配置
function getCurrentConfig() {
  return API_CONFIGS[currentMode];
}

// 切换配置模式
function switchConfigMode(mode) {
  if (Object.values(CONFIG_MODE).includes(mode)) {
    currentMode = mode;
    try {
      wx.setStorageSync('api_config_mode', mode);
      console.log(`API配置模式已切换到: ${mode}`);
      return true;
    } catch (error) {
      console.error('保存API配置模式失败:', error);
      return false;
    }
  }
  return false;
}

// 构建API URL
function buildAPIURL(endpoint) {
  const config = getCurrentConfig();
  if (config.useMockData) {
    return null; // 返回null表示使用模拟数据
  }
  return `${config.baseURL}${config.version}${endpoint}`;
}

// 检查是否使用模拟数据
function shouldUseMockData() {
  return getCurrentConfig().useMockData === true;
}

// 显示当前配置状态
function showConfigStatus() {
  const config = getCurrentConfig();
  const status = {
    mode: currentMode,
    config: config,
    useMockData: shouldUseMockData()
  };
  
  console.log('当前API配置状态:', status);
  
  // 在小程序中显示提示
  if (typeof wx !== 'undefined') {
    const modeText = {
      [CONFIG_MODE.MOCK]: '模拟数据模式',
      [CONFIG_MODE.DEVELOPMENT]: '开发环境模式',
      [CONFIG_MODE.PRODUCTION]: '生产环境模式'
    };
    
    wx.showToast({
      title: `当前: ${modeText[currentMode]}`,
      icon: 'none',
      duration: 2000
    });
  }
  
  return status;
}

// 智能API调用
async function smartAPICall(endpoint, mockDataProvider, options = {}) {
  const config = getCurrentConfig();
  
  // 如果配置为使用模拟数据，直接返回模拟数据
  if (config.useMockData) {
    console.log('使用模拟数据:', endpoint);
    
    // 模拟网络延迟
    if (options.mockDelay) {
      await new Promise(resolve => setTimeout(resolve, options.mockDelay));
    }
    
    return {
      success: true,
      data: typeof mockDataProvider === 'function' ? mockDataProvider() : mockDataProvider,
      source: 'mock'
    };
  }
  
  // 构建真实API URL
  const apiUrl = buildAPIURL(endpoint);
  if (!apiUrl) {
    throw new Error('无法构建API URL');
  }
  
  // 执行真实API调用
  return new Promise((resolve, reject) => {
    wx.request({
      url: apiUrl,
      method: options.method || 'GET',
      data: options.data,
      timeout: config.timeout,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve({
            success: true,
            data: res.data,
            source: 'api'
          });
        } else {
          console.log(`API返回错误状态码: ${res.statusCode}，回退到模拟数据`);
          resolve({
            success: true,
            data: typeof mockDataProvider === 'function' ? mockDataProvider() : mockDataProvider,
            source: 'mock_fallback'
          });
        }
      },
      fail: (error) => {
        console.log('API调用失败，回退到模拟数据:', error);
        resolve({
          success: true,
          data: typeof mockDataProvider === 'function' ? mockDataProvider() : mockDataProvider,
          source: 'mock_fallback'
        });
      }
    });
  });
}

// 配置管理界面（用于开发调试）
function createConfigUI() {
  if (typeof wx === 'undefined') {
    console.log('非小程序环境，无法创建配置UI');
    return;
  }
  
  const modes = [
    { name: '模拟数据模式', value: CONFIG_MODE.MOCK },
    { name: '开发环境模式', value: CONFIG_MODE.DEVELOPMENT },
    { name: '生产环境模式', value: CONFIG_MODE.PRODUCTION }
  ];
  
  wx.showActionSheet({
    itemList: modes.map(mode => mode.name),
    success: (res) => {
      const selectedMode = modes[res.tapIndex];
      if (selectedMode) {
        switchConfigMode(selectedMode.value);
        showConfigStatus();
      }
    }
  });
}

// 检查API健康状态
async function checkAPIHealth() {
  const config = getCurrentConfig();
  
  if (config.useMockData) {
    return {
      healthy: true,
      mode: 'mock',
      message: '模拟数据模式'
    };
  }
  
  const healthCheckUrl = buildAPIURL('/health');
  
  return new Promise((resolve) => {
    wx.request({
      url: healthCheckUrl,
      method: 'GET',
      timeout: 3000,
      success: (res) => {
        resolve({
          healthy: res.statusCode === 200,
          mode: currentMode,
          message: res.statusCode === 200 ? 'API服务正常' : `API返回状态码: ${res.statusCode}`
        });
      },
      fail: (error) => {
        resolve({
          healthy: false,
          mode: currentMode,
          message: `API连接失败: ${error.errMsg}`
        });
      }
    });
  });
}

module.exports = {
  CONFIG_MODE,
  getCurrentConfig,
  switchConfigMode,
  buildAPIURL,
  shouldUseMockData,
  showConfigStatus,
  smartAPICall,
  createConfigUI,
  checkAPIHealth,
  currentMode: () => currentMode
};
