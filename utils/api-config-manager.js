/**
 * API配置管理器
 * 处理API端点配置、健康检查、模拟数据回退等功能
 */

class APIConfigManager {
  constructor() {
    this.healthCheckCache = new Map();
    this.cacheTimeout = 30000; // 30秒缓存
  }

  /**
   * 检查API端点健康状态
   */
  async checkEndpointHealth(url, timeout = 5000) {
    const cacheKey = url;
    const cached = this.healthCheckCache.get(cacheKey);
    
    // 检查缓存
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      return cached.isHealthy;
    }

    return new Promise((resolve) => {
      wx.request({
        url: url,
        method: 'GET',
        timeout: timeout,
        success: (res) => {
          const isHealthy = res.statusCode >= 200 && res.statusCode < 500;
          this.healthCheckCache.set(cacheKey, {
            isHealthy,
            timestamp: Date.now()
          });
          resolve(isHealthy);
        },
        fail: (error) => {
          console.log(`API健康检查失败 ${url}:`, error);
          this.healthCheckCache.set(cacheKey, {
            isHealthy: false,
            timestamp: Date.now()
          });
          resolve(false);
        }
      });
    });
  }

  /**
   * 批量检查多个端点健康状态
   */
  async checkMultipleEndpoints(endpoints) {
    const results = await Promise.allSettled(
      endpoints.map(endpoint => this.checkEndpointHealth(endpoint.url))
    );

    return endpoints.map((endpoint, index) => ({
      ...endpoint,
      isHealthy: results[index].status === 'fulfilled' ? results[index].value : false
    }));
  }

  /**
   * 获取API配置状态
   */
  async getAPIStatus() {
    try {
      const { API } = require('../constants/index.js');
      
      // 检查关键端点
      const keyEndpoints = [
        { name: '商品列表', url: API.ENDPOINTS.SHOP.PRODUCTS },
        { name: '商品分类', url: API.ENDPOINTS.SHOP.CATEGORIES },
        { name: '购物车', url: API.ENDPOINTS.SHOP.CART },
        { name: '订单', url: API.ENDPOINTS.SHOP.ORDERS }
      ];

      const results = await this.checkMultipleEndpoints(keyEndpoints);
      
      const healthyCount = results.filter(r => r.isHealthy).length;
      const totalCount = results.length;
      
      return {
        overall: healthyCount > 0 ? 'partial' : 'down',
        healthyCount,
        totalCount,
        endpoints: results,
        recommendation: this.getRecommendation(healthyCount, totalCount)
      };
    } catch (error) {
      console.error('获取API状态失败:', error);
      return {
        overall: 'error',
        healthyCount: 0,
        totalCount: 0,
        endpoints: [],
        recommendation: 'use_mock',
        error: error.message
      };
    }
  }

  /**
   * 获取使用建议
   */
  getRecommendation(healthyCount, totalCount) {
    if (healthyCount === 0) {
      return 'use_mock'; // 使用模拟数据
    } else if (healthyCount === totalCount) {
      return 'use_api'; // 使用API
    } else {
      return 'mixed'; // 混合模式
    }
  }

  /**
   * 智能API调用
   * 自动检查端点健康状态，决定使用API还是模拟数据
   */
  async smartAPICall(endpoint, fallbackData, options = {}) {
    const { timeout = 5000, useCache = true } = options;
    
    try {
      // 检查端点健康状态
      const isHealthy = await this.checkEndpointHealth(endpoint, timeout);
      
      if (!isHealthy) {
        console.log(`端点不健康，使用模拟数据: ${endpoint}`);
        return {
          success: true,
          data: fallbackData,
          source: 'mock'
        };
      }

      // 端点健康，尝试真实API调用
      return new Promise((resolve) => {
        wx.request({
          url: endpoint,
          method: 'GET',
          timeout: timeout,
          success: (res) => {
            if (res.statusCode === 200 && res.data) {
              resolve({
                success: true,
                data: res.data,
                source: 'api'
              });
            } else {
              console.log(`API返回异常状态码: ${res.statusCode}`);
              resolve({
                success: true,
                data: fallbackData,
                source: 'mock'
              });
            }
          },
          fail: (error) => {
            console.log(`API调用失败，使用模拟数据:`, error);
            resolve({
              success: true,
              data: fallbackData,
              source: 'mock'
            });
          }
        });
      });

    } catch (error) {
      console.error('智能API调用失败:', error);
      return {
        success: true,
        data: fallbackData,
        source: 'mock'
      };
    }
  }

  /**
   * 生成API配置报告
   */
  async generateConfigReport() {
    const status = await this.getAPIStatus();
    
    const report = {
      timestamp: new Date().toISOString(),
      status: status,
      suggestions: this.generateSuggestions(status),
      mockDataAvailable: this.checkMockDataAvailability()
    };

    return report;
  }

  /**
   * 生成配置建议
   */
  generateSuggestions(status) {
    const suggestions = [];

    if (status.overall === 'down') {
      suggestions.push({
        type: 'warning',
        message: 'API服务不可用，建议使用模拟数据进行开发和测试'
      });
      suggestions.push({
        type: 'info',
        message: '检查API服务器是否启动，或更新API配置中的基础URL'
      });
    } else if (status.overall === 'partial') {
      suggestions.push({
        type: 'warning',
        message: '部分API端点不可用，建议检查服务配置'
      });
    } else if (status.overall === 'error') {
      suggestions.push({
        type: 'error',
        message: 'API配置检查失败，请检查配置文件'
      });
    }

    return suggestions;
  }

  /**
   * 检查模拟数据可用性
   */
  checkMockDataAvailability() {
    try {
      // 检查关键页面是否有模拟数据方法
      const shopPagePath = 'pages/shop/shop.js';
      return {
        shop: true, // 商城页面有loadMockData方法
        available: true
      };
    } catch (error) {
      return {
        available: false,
        error: error.message
      };
    }
  }

  /**
   * 清除健康检查缓存
   */
  clearHealthCheckCache() {
    this.healthCheckCache.clear();
  }

  /**
   * 设置API配置
   */
  setAPIConfig(config) {
    try {
      wx.setStorageSync('api_config', config);
      this.clearHealthCheckCache(); // 清除缓存，强制重新检查
      return true;
    } catch (error) {
      console.error('保存API配置失败:', error);
      return false;
    }
  }

  /**
   * 获取API配置
   */
  getAPIConfig() {
    try {
      return wx.getStorageSync('api_config') || {};
    } catch (error) {
      console.error('获取API配置失败:', error);
      return {};
    }
  }
}

// 创建单例实例
const apiConfigManager = new APIConfigManager();

module.exports = {
  APIConfigManager,
  apiConfigManager
};
