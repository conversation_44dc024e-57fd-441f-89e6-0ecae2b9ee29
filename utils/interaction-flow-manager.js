/**
 * 用户交互流程管理器
 * 统一管理用户操作流程和反馈
 */

class InteractionFlowManager {
  constructor() {
    this.currentFlow = null;
    this.flowHistory = [];
    this.feedbackQueue = [];
  }

  /**
   * 开始交互流程
   */
  startFlow(flowName, options = {}) {
    console.log(`🚀 开始交互流程: ${flowName}`);
    
    this.currentFlow = {
      name: flowName,
      startTime: Date.now(),
      steps: [],
      options
    };
    
    // 显示加载状态
    if (options.showLoading !== false) {
      wx.showLoading({
        title: options.loadingText || '处理中...',
        mask: true
      });
    }
  }

  /**
   * 添加流程步骤
   */
  addStep(stepName, data = {}) {
    if (!this.currentFlow) return;
    
    this.currentFlow.steps.push({
      name: stepName,
      timestamp: Date.now(),
      data
    });
    
    console.log(`📝 流程步骤: ${stepName}`);
  }

  /**
   * 完成交互流程
   */
  completeFlow(result = {}) {
    if (!this.currentFlow) return;
    
    const duration = Date.now() - this.currentFlow.startTime;
    console.log(`✅ 完成交互流程: ${this.currentFlow.name}, 耗时: ${duration}ms`);
    
    // 隐藏加载状态
    wx.hideLoading();
    
    // 显示成功反馈
    if (result.showSuccess !== false) {
      wx.showToast({
        title: result.successText || '操作成功',
        icon: 'success',
        duration: 1500
      });
    }
    
    // 记录流程历史
    this.flowHistory.push({
      ...this.currentFlow,
      endTime: Date.now(),
      duration,
      result
    });
    
    this.currentFlow = null;
  }

  /**
   * 流程失败处理
   */
  failFlow(error) {
    if (!this.currentFlow) return;
    
    const duration = Date.now() - this.currentFlow.startTime;
    console.error(`❌ 交互流程失败: ${this.currentFlow.name}`, error);
    
    // 隐藏加载状态
    wx.hideLoading();
    
    // 显示错误反馈
    wx.showToast({
      title: error.message || '操作失败',
      icon: 'error',
      duration: 2000
    });
    
    // 记录失败流程
    this.flowHistory.push({
      ...this.currentFlow,
      endTime: Date.now(),
      duration,
      error: error.message || error
    });
    
    this.currentFlow = null;
  }

  /**
   * 显示确认对话框
   */
  async showConfirm(options) {
    return new Promise((resolve) => {
      wx.showModal({
        title: options.title || '确认操作',
        content: options.content || '确定要执行此操作吗？',
        confirmText: options.confirmText || '确定',
        cancelText: options.cancelText || '取消',
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  }

  /**
   * 获取流程统计
   */
  getFlowStats() {
    const stats = {
      totalFlows: this.flowHistory.length,
      successFlows: this.flowHistory.filter(f => !f.error).length,
      failedFlows: this.flowHistory.filter(f => f.error).length,
      avgDuration: 0
    };
    
    if (stats.totalFlows > 0) {
      const totalDuration = this.flowHistory.reduce((sum, f) => sum + f.duration, 0);
      stats.avgDuration = Math.round(totalDuration / stats.totalFlows);
    }
    
    return stats;
  }
}

// 创建全局实例
const interactionFlow = new InteractionFlowManager();

module.exports = {
  InteractionFlowManager,
  interactionFlow
};