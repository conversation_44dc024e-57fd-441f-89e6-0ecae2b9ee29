/**
 * 前端路由管理和权限守卫
 * Frontend Route Management and Permission Guard
 * 
 * 功能：
 * - 路由权限验证
 * - 动态菜单生成
 * - 平台/租户界面切换
 * - 页面访问控制
 */

const { ROLES, PERMISSIONS, hasPermission, getCurrentUserPermissions } = require('./role-permission');

/**
 * 路由配置定义
 */
const ROUTE_CONFIG = {
  // 平台级管理路由
  PLATFORM_ROUTES: {
    '/pages/admin/platform-management/platform-management': {
      name: '平台管理中心',
      requiredRoles: ['super_admin', 'platform_admin'],
      requiredPermissions: [],
      icon: 'platform',
      category: 'platform'
    },
    '/pages/admin/goose-price/goose-price': {
      name: '今日鹅价管理',
      requiredRoles: ['super_admin', 'platform_admin'],
      requiredPermissions: ['PLATFORM_GOOSE_PRICE_MANAGE'],
      icon: 'price',
      category: 'platform'
    },
    '/pages/admin/announcement/announcement': {
      name: '平台公告管理',
      requiredRoles: ['super_admin', 'platform_admin'],
      requiredPermissions: ['PLATFORM_ANNOUNCEMENT_MANAGE'],
      icon: 'announcement',
      category: 'platform'
    },
    '/pages/admin/tenant-list/tenant-list': {
      name: '租户管理',
      requiredRoles: ['super_admin', 'platform_admin'],
      requiredPermissions: ['PLATFORM_TENANT_VIEW'],
      icon: 'tenant',
      category: 'platform'
    },
    '/pages/admin/knowledge/knowledge': {
      name: '知识库管理',
      requiredRoles: ['super_admin', 'platform_admin'],
      requiredPermissions: ['PLATFORM_KNOWLEDGE_MANAGE'],
      icon: 'knowledge',
      category: 'platform'
    },
    '/pages/admin/ai-config/ai-config': {
      name: 'AI模型配置',
      requiredRoles: ['super_admin', 'platform_admin'],
      requiredPermissions: ['PLATFORM_AI_CONFIG'],
      icon: 'ai',
      category: 'platform'
    },
    '/pages/admin/system-config/system-config': {
      name: '系统配置',
      requiredRoles: ['super_admin', 'platform_admin'],
      requiredPermissions: ['PLATFORM_SYSTEM_CONFIG'],
      icon: 'system',
      category: 'platform'
    }
  },

  // 租户级管理路由
  TENANT_ROUTES: {
    '/pages/business/flock-management/flock-management': {
      name: '鹅群管理',
      requiredRoles: ['tenant_owner', 'admin', 'manager', 'employee'],
      requiredPermissions: ['FLOCK_VIEW_ALL', 'FLOCK_VIEW_OWN'],
      icon: 'flock',
      category: 'business'
    },
    '/pages/business/health-management/health-management': {
      name: '健康管理',
      requiredRoles: ['tenant_owner', 'admin', 'veterinarian', 'employee'],
      requiredPermissions: ['HEALTH_VIEW_ALL', 'HEALTH_VIEW_OWN'],
      icon: 'health',
      category: 'business'
    },
    '/pages/business/material-management/material-management': {
      name: '物料管理',
      requiredRoles: ['tenant_owner', 'admin', 'manager', 'employee'],
      requiredPermissions: ['MATERIAL_VIEW_ALL', 'MATERIAL_VIEW_OWN'],
      icon: 'material',
      category: 'business'
    },
    '/pages/business/finance-management/finance-management': {
      name: '财务管理',
      requiredRoles: ['tenant_owner', 'admin', 'finance_manager'],
      requiredPermissions: ['FINANCE_VIEW_ALL', 'FINANCE_VIEW_OWN'],
      icon: 'finance',
      category: 'business'
    },
    '/pages/business/production-management/production-management': {
      name: '生产管理',
      requiredRoles: ['tenant_owner', 'admin', 'manager', 'employee'],
      requiredPermissions: ['PRODUCTION_VIEW_ALL', 'PRODUCTION_VIEW_OWN'],
      icon: 'production',
      category: 'business'
    }
  },

  // 公共路由（所有用户可访问）
  PUBLIC_ROUTES: {
    '/pages/index/index': {
      name: '首页',
      requiredRoles: [],
      requiredPermissions: [],
      icon: 'home',
      category: 'public'
    },
    '/pages/profile/profile': {
      name: '个人中心',
      requiredRoles: [],
      requiredPermissions: [],
      icon: 'profile',
      category: 'public'
    },
    '/pages/goose-price/goose-price': {
      name: '今日鹅价',
      requiredRoles: [],
      requiredPermissions: [],
      icon: 'price',
      category: 'public'
    },
    '/pages/knowledge/knowledge': {
      name: '养鹅知识',
      requiredRoles: [],
      requiredPermissions: [],
      icon: 'knowledge',
      category: 'public'
    }
  }
};

/**
 * 路由守卫类
 */
class RouteGuard {
  /**
   * 检查路由访问权限
   */
  static async checkRoutePermission(url) {
    try {
      const userPermissions = await getCurrentUserPermissions();
      
      if (!userPermissions) {
        return {
          allowed: false,
          reason: '用户未登录',
          redirectTo: '/pages/login/login'
        };
      }

      // 检查是否为公共路由
      if (ROUTE_CONFIG.PUBLIC_ROUTES[url]) {
        return { allowed: true };
      }

      // 检查平台级路由
      if (ROUTE_CONFIG.PLATFORM_ROUTES[url]) {
        return this.checkPlatformRoute(url, userPermissions);
      }

      // 检查租户级路由
      if (ROUTE_CONFIG.TENANT_ROUTES[url]) {
        return this.checkTenantRoute(url, userPermissions);
      }

      // 未知路由，默认允许
      return { allowed: true };
    } catch (error) {
      console.error('路由权限检查失败:', error);
      return {
        allowed: false,
        reason: '权限检查失败',
        redirectTo: '/pages/index/index'
      };
    }
  }

  /**
   * 检查平台级路由权限
   */
  static checkPlatformRoute(url, userPermissions) {
    const routeConfig = ROUTE_CONFIG.PLATFORM_ROUTES[url];
    
    // 检查角色权限
    if (routeConfig.requiredRoles.length > 0) {
      if (!routeConfig.requiredRoles.includes(userPermissions.role)) {
        return {
          allowed: false,
          reason: '角色权限不足',
          redirectTo: '/pages/index/index'
        };
      }
    }

    // 检查具体权限
    if (routeConfig.requiredPermissions.length > 0) {
      const hasRequiredPermission = routeConfig.requiredPermissions.some(permission =>
        hasPermission(userPermissions.role, permission, userPermissions.permissions)
      );

      if (!hasRequiredPermission) {
        return {
          allowed: false,
          reason: '功能权限不足',
          redirectTo: '/pages/index/index'
        };
      }
    }

    return { allowed: true };
  }

  /**
   * 检查租户级路由权限
   */
  static checkTenantRoute(url, userPermissions) {
    const routeConfig = ROUTE_CONFIG.TENANT_ROUTES[url];
    
    // 检查是否有租户ID
    if (!userPermissions.tenant_id) {
      return {
        allowed: false,
        reason: '用户未绑定租户',
        redirectTo: '/pages/tenant-binding/tenant-binding'
      };
    }

    // 检查角色权限
    if (routeConfig.requiredRoles.length > 0) {
      if (!routeConfig.requiredRoles.includes(userPermissions.role)) {
        return {
          allowed: false,
          reason: '角色权限不足',
          redirectTo: '/pages/index/index'
        };
      }
    }

    // 检查具体权限
    if (routeConfig.requiredPermissions.length > 0) {
      const hasRequiredPermission = routeConfig.requiredPermissions.some(permission =>
        hasPermission(userPermissions.role, permission, userPermissions.permissions)
      );

      if (!hasRequiredPermission) {
        return {
          allowed: false,
          reason: '功能权限不足',
          redirectTo: '/pages/index/index'
        };
      }
    }

    return { allowed: true };
  }

  /**
   * 页面跳转前的权限检查
   */
  static async navigateWithPermissionCheck(url, options = {}) {
    const permissionResult = await this.checkRoutePermission(url);
    
    if (!permissionResult.allowed) {
      wx.showModal({
        title: '访问受限',
        content: permissionResult.reason,
        showCancel: false,
        success: () => {
          if (permissionResult.redirectTo) {
            wx.redirectTo({
              url: permissionResult.redirectTo
            });
          }
        }
      });
      return false;
    }

    // 权限检查通过，执行跳转
    if (options.redirect) {
      wx.redirectTo({ url });
    } else if (options.reLaunch) {
      wx.reLaunch({ url });
    } else if (options.switchTab) {
      wx.switchTab({ url });
    } else {
      wx.navigateTo({ url });
    }
    
    return true;
  }
}

/**
 * 动态菜单生成器
 */
class MenuGenerator {
  /**
   * 生成用户可访问的菜单
   */
  static async generateUserMenu() {
    try {
      const userPermissions = await getCurrentUserPermissions();
      
      if (!userPermissions) {
        return [];
      }

      const menu = [];

      // 添加公共菜单
      menu.push({
        category: '通用功能',
        items: this.filterRoutesByPermission(ROUTE_CONFIG.PUBLIC_ROUTES, userPermissions)
      });

      // 添加租户级菜单
      if (userPermissions.tenant_id) {
        const tenantRoutes = this.filterRoutesByPermission(ROUTE_CONFIG.TENANT_ROUTES, userPermissions);
        if (tenantRoutes.length > 0) {
          menu.push({
            category: '业务管理',
            items: tenantRoutes
          });
        }
      }

      // 添加平台级菜单
      const platformRoutes = this.filterRoutesByPermission(ROUTE_CONFIG.PLATFORM_ROUTES, userPermissions);
      if (platformRoutes.length > 0) {
        menu.push({
          category: '平台管理',
          items: platformRoutes
        });
      }

      return menu;
    } catch (error) {
      console.error('生成用户菜单失败:', error);
      return [];
    }
  }

  /**
   * 根据权限过滤路由
   */
  static filterRoutesByPermission(routes, userPermissions) {
    const filteredRoutes = [];

    Object.entries(routes).forEach(([url, config]) => {
      // 检查角色权限
      if (config.requiredRoles.length > 0) {
        if (!config.requiredRoles.includes(userPermissions.role)) {
          return;
        }
      }

      // 检查具体权限
      if (config.requiredPermissions.length > 0) {
        const hasRequiredPermission = config.requiredPermissions.some(permission =>
          hasPermission(userPermissions.role, permission, userPermissions.permissions)
        );

        if (!hasRequiredPermission) {
          return;
        }
      }

      filteredRoutes.push({
        url,
        name: config.name,
        icon: config.icon,
        category: config.category
      });
    });

    return filteredRoutes;
  }

  /**
   * 生成底部导航栏配置
   */
  static async generateTabBarConfig() {
    const userPermissions = await getCurrentUserPermissions();
    
    if (!userPermissions) {
      return {
        list: [
          {
            pagePath: 'pages/index/index',
            text: '首页',
            iconPath: '/images/tab_home.png',
            selectedIconPath: '/images/tab_home_active.png'
          }
        ]
      };
    }

    const tabList = [
      {
        pagePath: 'pages/index/index',
        text: '首页',
        iconPath: '/images/tab_home.png',
        selectedIconPath: '/images/tab_home_active.png'
      }
    ];

    // 根据用户角色添加不同的标签页
    if (userPermissions.tenant_id) {
      tabList.push({
        pagePath: 'pages/business/dashboard/dashboard',
        text: '业务',
        iconPath: '/images/tab_business.png',
        selectedIconPath: '/images/tab_business_active.png'
      });
    }

    if (['super_admin', 'platform_admin'].includes(userPermissions.role)) {
      tabList.push({
        pagePath: 'pages/admin/platform-management/platform-management',
        text: '管理',
        iconPath: '/images/tab_admin.png',
        selectedIconPath: '/images/tab_admin_active.png'
      });
    }

    tabList.push({
      pagePath: 'pages/profile/profile',
      text: '我的',
      iconPath: '/images/tab_profile.png',
      selectedIconPath: '/images/tab_profile_active.png'
    });

    return {
      color: '#999999',
      selectedColor: '#007AFF',
      backgroundColor: '#FFFFFF',
      borderStyle: 'black',
      list: tabList
    };
  }
}

/**
 * 页面权限装饰器
 */
function withPermissionCheck(pageConfig) {
  const originalOnLoad = pageConfig.onLoad || function() {};
  
  pageConfig.onLoad = async function(options) {
    // 获取当前页面路径
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentUrl = `/${currentPage.route}`;

    // 检查页面访问权限
    const permissionResult = await RouteGuard.checkRoutePermission(currentUrl);
    
    if (!permissionResult.allowed) {
      wx.showModal({
        title: '访问受限',
        content: permissionResult.reason,
        showCancel: false,
        success: () => {
          if (permissionResult.redirectTo) {
            wx.redirectTo({
              url: permissionResult.redirectTo
            });
          } else {
            wx.navigateBack();
          }
        }
      });
      return;
    }

    // 权限检查通过，执行原始onLoad
    originalOnLoad.call(this, options);
  };

  return pageConfig;
}

module.exports = {
  ROUTE_CONFIG,
  RouteGuard,
  MenuGenerator,
  withPermissionCheck
};
