/**
 * 数据流管理器
 * 统一管理数据的获取、处理和更新
 */

class DataFlowManager {
  constructor() {
    this.dataStore = new Map();
    this.subscribers = new Map();
    this.updateQueue = [];
  }

  /**
   * 设置数据
   */
  setData(key, data, options = {}) {
    const oldData = this.dataStore.get(key);
    this.dataStore.set(key, {
      data,
      timestamp: Date.now(),
      version: (oldData?.version || 0) + 1,
      options
    });
    
    // 通知订阅者
    this.notifySubscribers(key, data, oldData?.data);
  }

  /**
   * 获取数据
   */
  getData(key) {
    const item = this.dataStore.get(key);
    return item ? item.data : null;
  }

  /**
   * 订阅数据变化
   */
  subscribe(key, callback) {
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set());
    }
    this.subscribers.get(key).add(callback);
    
    // 返回取消订阅函数
    return () => {
      const callbacks = this.subscribers.get(key);
      if (callbacks) {
        callbacks.delete(callback);
      }
    };
  }

  /**
   * 通知订阅者
   */
  notifySubscribers(key, newData, oldData) {
    const callbacks = this.subscribers.get(key);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(newData, oldData, key);
        } catch (error) {
          console.error(`数据订阅回调错误 [${key}]:`, error);
        }
      });
    }
  }

  /**
   * 批量更新数据
   */
  batchUpdate(updates) {
    const startTime = Date.now();
    
    updates.forEach(({ key, data, options }) => {
      this.setData(key, data, options);
    });
    
    console.log(`📊 批量更新完成: ${updates.length}项, 耗时: ${Date.now() - startTime}ms`);
  }

  /**
   * 清理过期数据
   */
  cleanup(maxAge = 30 * 60 * 1000) { // 默认30分钟
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, item] of this.dataStore.entries()) {
      if (now - item.timestamp > maxAge) {
        this.dataStore.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`🧹 清理过期数据: ${cleanedCount}项`);
    }
  }
}

// 创建全局实例
const dataFlow = new DataFlowManager();

// 定期清理过期数据
setInterval(() => {
  dataFlow.cleanup();
}, 10 * 60 * 1000); // 每10分钟清理一次

module.exports = {
  DataFlowManager,
  dataFlow
};