/**
 * 数据处理管道 - 高效的数据流转和处理
 */

class DataProcessingPipeline {
  constructor() {
    this.processors = new Map();
    this.middlewares = [];
    this.metrics = {
      processed: 0,
      errors: 0,
      avgProcessTime: 0
    };
  }

  /**
   * 注册数据处理器
   */
  registerProcessor(name, processor) {
    this.processors.set(name, processor);
    console.log(`📝 注册数据处理器: ${name}`);
  }

  /**
   * 添加中间件
   */
  use(middleware) {
    this.middlewares.push(middleware);
  }

  /**
   * 处理数据
   */
  async process(processorName, data, options = {}) {
    const startTime = Date.now();
    
    try {
      // 应用中间件
      let processedData = data;
      for (const middleware of this.middlewares) {
        processedData = await middleware(processedData, options);
      }
      
      // 获取处理器
      const processor = this.processors.get(processorName);
      if (!processor) {
        throw new Error(`未找到处理器: ${processorName}`);
      }
      
      // 执行处理
      const result = await processor(processedData, options);
      
      // 更新指标
      const duration = Date.now() - startTime;
      this.updateMetrics(duration, false);
      
      console.log(`✅ 数据处理完成: ${processorName}, 耗时: ${duration}ms`);
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateMetrics(duration, true);
      
      console.error(`❌ 数据处理失败: ${processorName}`, error);
      throw error;
    }
  }

  /**
   * 批量处理数据
   */
  async batchProcess(processorName, dataArray, options = {}) {
    const startTime = Date.now();
    const batchSize = options.batchSize || 10;
    const results = [];
    
    console.log(`📦 开始批量处理: ${dataArray.length}条数据, 批次大小: ${batchSize}`);
    
    for (let i = 0; i < dataArray.length; i += batchSize) {
      const batch = dataArray.slice(i, i + batchSize);
      const batchResults = await Promise.allSettled(
        batch.map(data => this.process(processorName, data, options))
      );
      results.push(...batchResults);
    }
    
    const duration = Date.now() - startTime;
    const successCount = results.filter(r => r.status === 'fulfilled').length;
    const errorCount = results.filter(r => r.status === 'rejected').length;
    
    console.log(`📊 批量处理完成: 成功 ${successCount}, 失败 ${errorCount}, 耗时: ${duration}ms`);
    
    return results;
  }

  /**
   * 更新处理指标
   */
  updateMetrics(duration, isError) {
    this.metrics.processed++;
    if (isError) {
      this.metrics.errors++;
    }
    
    // 计算平均处理时间
    this.metrics.avgProcessTime = 
      (this.metrics.avgProcessTime * (this.metrics.processed - 1) + duration) / this.metrics.processed;
  }

  /**
   * 获取处理统计
   */
  getStats() {
    return {
      ...this.metrics,
      errorRate: this.metrics.processed > 0 ? 
        (this.metrics.errors / this.metrics.processed * 100).toFixed(2) + '%' : '0%'
    };
  }
}

// 创建全局数据处理管道
const dataProcessingPipeline = new DataProcessingPipeline();

// 注册常用处理器
dataProcessingPipeline.registerProcessor('validateData', async (data) => {
  // 数据验证逻辑
  if (!data || typeof data !== 'object') {
    throw new Error('无效的数据格式');
  }
  return data;
});

dataProcessingPipeline.registerProcessor('transformData', async (data) => {
  // 数据转换逻辑
  return {
    ...data,
    processedAt: new Date().toISOString(),
    version: '1.0'
  };
});

dataProcessingPipeline.registerProcessor('saveData', async (data, options) => {
  // 数据保存逻辑
  console.log('💾 保存数据:', data);
  return { success: true, id: Date.now() };
});

module.exports = {
  DataProcessingPipeline,
  dataProcessingPipeline
};