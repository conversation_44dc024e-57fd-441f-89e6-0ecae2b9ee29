/**
 * 图片后备处理器
 * 处理图片加载失败的情况，提供替代方案
 */

class ImageFallbackHandler {
  constructor() {
    this.fallbackMap = new Map();
    this.initFallbackMap();
  }

  /**
   * 初始化后备图片映射
   */
  initFallbackMap() {
    // 定义图片后备方案
    const fallbacks = {
      // 缺失的图标使用现有的替代图标
      '/images/icons/sort.png': '/images/icons/list.png',
      '/images/icons/tag.png': '/images/icons/info.png',
      '/images/icons/filter.png': '/images/icons/search.png',
      '/images/icons/menu.png': '/images/icons/more.png',
      
      // SVG 优先方案
      '/images/icons/sort.png': '/images/icons/sort.svg',
      '/images/icons/tag.png': '/images/icons/tag.svg',
    };
    
    Object.entries(fallbacks).forEach(([original, fallback]) => {
      this.fallbackMap.set(original, fallback);
    });
  }

  /**
   * 获取后备图片路径
   */
  getFallbackImage(originalPath) {
    return this.fallbackMap.get(originalPath) || originalPath;
  }

  /**
   * 处理图片加载错误
   */
  handleImageError(imagePath, imageElement) {
    const fallbackPath = this.getFallbackImage(imagePath);
    
    if (fallbackPath !== imagePath) {
      console.log(`🔄 图片加载失败，使用后备方案: ${imagePath} -> ${fallbackPath}`);
      
      if (imageElement && imageElement.src) {
        imageElement.src = fallbackPath;
      }
      
      return fallbackPath;
    }
    
    console.warn(`⚠️ 图片加载失败且无后备方案: ${imagePath}`);
    return null;
  }

  /**
   * 为小程序创建图片组件错误处理
   */
  createImageErrorHandler() {
    return {
      onError: (e) => {
        const { currentTarget } = e;
        const originalSrc = currentTarget.dataset.originalSrc || currentTarget.src;
        const fallbackSrc = this.getFallbackImage(originalSrc);
        
        if (fallbackSrc !== originalSrc) {
          currentTarget.src = fallbackSrc;
          console.log(`🔄 图片后备处理: ${originalSrc} -> ${fallbackSrc}`);
        }
      }
    };
  }

  /**
   * 预检查图片路径
   */
  preprocessImagePath(imagePath) {
    // 如果是已知的缺失图片，直接返回后备方案
    const knownMissing = [
      '/images/icons/sort.png',
      '/images/icons/tag.png'
    ];
    
    if (knownMissing.includes(imagePath)) {
      const fallback = this.getFallbackImage(imagePath);
      console.log(`🔄 预处理缺失图片: ${imagePath} -> ${fallback}`);
      return fallback;
    }
    
    return imagePath;
  }
}

// 创建全局实例
const imageHandler = new ImageFallbackHandler();

// 导出便捷方法
const ImageUtils = {
  /**
   * 获取安全的图片路径
   */
  getSafeImagePath: (imagePath) => {
    return imageHandler.preprocessImagePath(imagePath);
  },

  /**
   * 处理图片错误
   */
  handleError: (imagePath, element) => {
    return imageHandler.handleImageError(imagePath, element);
  },

  /**
   * 创建错误处理器
   */
  createErrorHandler: () => {
    return imageHandler.createImageErrorHandler();
  },

  /**
   * 常用图标映射
   */
  COMMON_ICONS: {
    SORT: imageHandler.preprocessImagePath('/images/icons/sort.png'),
    TAG: imageHandler.preprocessImagePath('/images/icons/tag.png'),
    FILTER: imageHandler.preprocessImagePath('/images/icons/filter.png'),
    SEARCH: '/images/icons/search.png',
    MORE: '/images/icons/more.png',
    LIST: '/images/icons/list.png',
    INFO: '/images/icons/info.png'
  }
};

module.exports = {
  ImageFallbackHandler,
  ImageUtils,
  imageHandler
};
