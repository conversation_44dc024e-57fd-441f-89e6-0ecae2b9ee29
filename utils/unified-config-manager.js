/**
 * 统一配置管理系统 - 第三阶段实现
 * Unified Configuration Management System - Phase 3 Implementation
 * 
 * 功能包括：
 * - 环境配置管理
 * - 动态配置更新
 * - 配置缓存机制
 * - 配置验证
 * - 多租户配置隔离
 */

const { ultimateAPIClient } = require('./ultimate-api-client');
const { permissionManager } = require('./enhanced-permission-manager');

/**
 * 统一配置管理器类
 */
class UnifiedConfigManager {
  constructor() {
    // 配置缓存
    this.configCache = new Map();
    this.cacheConfig = {
      defaultTTL: 10 * 60 * 1000, // 10分钟
      maxSize: 200
    };

    // 配置监听器
    this.configListeners = new Map();

    // 默认配置
    this.defaultConfigs = {
      // 应用基础配置
      app: {
        name: '智慧养鹅云',
        version: '3.0.0',
        environment: 'production',
        debug: false,
        logLevel: 'info'
      },

      // API配置
      api: {
        baseURL: 'https://api.zhihuiyange.com',
        timeout: 10000,
        retryTimes: 3,
        retryDelay: 1000
      },

      // 云开发配置
      cloud: {
        env: 'prod-zhihuiyange',
        traceUser: true,
        timeout: 30000
      },

      // 缓存配置
      cache: {
        enabled: true,
        defaultTTL: 5 * 60 * 1000,
        maxSize: 1000
      },

      // 权限配置
      permission: {
        cacheEnabled: true,
        cacheTTL: 5 * 60 * 1000,
        strictMode: true
      },

      // 数据隔离配置
      dataIsolation: {
        enabled: true,
        strictMode: true,
        auditEnabled: true
      },

      // 业务配置
      business: {
        // 健康模块配置
        health: {
          aiDiagnosisEnabled: true,
          reportCacheTTL: 10 * 60 * 1000,
          maxDiagnosisHistory: 50
        },

        // 物料模块配置
        material: {
          lowStockThreshold: 10,
          criticalStockThreshold: 5,
          expiryWarningDays: 30,
          autoRestockEnabled: false
        },

        // 财务模块配置
        finance: {
          currencySymbol: '¥',
          decimalPlaces: 2,
          reportFormats: ['excel', 'pdf'],
          autoBackupEnabled: true
        },

        // 生产模块配置
        production: {
          recordRetentionDays: 365,
          batchSizeLimit: 100,
          photoUploadLimit: 5
        }
      },

      // UI配置
      ui: {
        theme: 'default',
        primaryColor: '#1890ff',
        pageSize: 20,
        animationEnabled: true,
        darkModeEnabled: false
      },

      // 通知配置
      notification: {
        enabled: true,
        soundEnabled: true,
        vibrationEnabled: true,
        alertTypes: ['stock', 'health', 'finance', 'system']
      }
    };

    // 环境特定配置
    this.environmentConfigs = {
      development: {
        app: { debug: true, logLevel: 'debug' },
        api: { baseURL: 'http://localhost:3000' },
        cloud: { env: 'dev-zhihuiyange' }
      },
      testing: {
        app: { debug: true, logLevel: 'debug' },
        api: { baseURL: 'https://test-api.zhihuiyange.com' },
        cloud: { env: 'test-zhihuiyange' }
      },
      production: {
        app: { debug: false, logLevel: 'warn' },
        api: { baseURL: 'https://api.zhihuiyange.com' },
        cloud: { env: 'prod-zhihuiyange' }
      }
    };

    // 初始化
    this.init();
  }

  /**
   * 初始化配置管理器
   */
  async init() {
    try {
      // 1. 检测环境
      this.detectEnvironment();

      // 2. 合并环境配置
      this.mergeEnvironmentConfigs();

      // 3. 加载远程配置
      await this.loadRemoteConfigs();

      // 4. 加载租户配置
      await this.loadTenantConfigs();

      // 5. 启动配置同步
      this.startConfigSync();

      console.log('[ConfigManager] 配置管理器初始化完成');
    } catch (error) {
      console.error('[ConfigManager] 初始化失败:', error);
    }
  }

  /**
   * 检测运行环境
   */
  detectEnvironment() {
    let environment = 'production';

    try {
      // 通过小程序账号信息判断环境
      const accountInfo = wx.getAccountInfoSync();
      if (accountInfo.miniProgram.envVersion === 'develop') {
        environment = 'development';
      } else if (accountInfo.miniProgram.envVersion === 'trial') {
        environment = 'testing';
      }
    } catch (error) {
      console.warn('无法获取环境信息，使用默认环境:', error);
    }

    this.currentEnvironment = environment;
    this.defaultConfigs.app.environment = environment;
  }

  /**
   * 合并环境配置
   */
  mergeEnvironmentConfigs() {
    const envConfig = this.environmentConfigs[this.currentEnvironment] || {};
    this.mergedConfigs = this.deepMerge(this.defaultConfigs, envConfig);
  }

  /**
   * 加载远程配置
   */
  async loadRemoteConfigs() {
    try {
      const response = await ultimateAPIClient.get('/api/v2/system/config', {
        cache: true,
        cacheTTL: this.cacheConfig.defaultTTL
      });

      if (response.success && response.data) {
        // 合并远程配置
        this.mergedConfigs = this.deepMerge(this.mergedConfigs, response.data);
        
        // 触发配置更新事件
        this.emitConfigChange('remote', response.data);
      }
    } catch (error) {
      console.warn('加载远程配置失败:', error);
    }
  }

  /**
   * 加载租户配置
   */
  async loadTenantConfigs() {
    try {
      const user = this.getCurrentUser();
      if (!user || !user.tenant_id) {
        return;
      }

      const response = await ultimateAPIClient.get(`/api/v2/tenants/${user.tenant_id}/config`, {
        cache: true,
        cacheTTL: this.cacheConfig.defaultTTL
      });

      if (response.success && response.data) {
        // 合并租户配置
        this.mergedConfigs = this.deepMerge(this.mergedConfigs, response.data);
        
        // 触发配置更新事件
        this.emitConfigChange('tenant', response.data);
      }
    } catch (error) {
      console.warn('加载租户配置失败:', error);
    }
  }

  /**
   * 获取配置值
   * @param {string} key 配置键，支持点号分隔的路径
   * @param {*} defaultValue 默认值
   * @returns {*} 配置值
   */
  get(key, defaultValue = null) {
    // 检查缓存
    const cacheKey = `config:${key}`;
    const cached = this.configCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheConfig.defaultTTL) {
      return cached.value;
    }

    // 获取配置值
    const value = this.getNestedValue(this.mergedConfigs, key, defaultValue);
    
    // 缓存结果
    this.setCache(cacheKey, value);
    
    return value;
  }

  /**
   * 设置配置值
   * @param {string} key 配置键
   * @param {*} value 配置值
   * @param {boolean} persist 是否持久化到服务器
   */
  async set(key, value, persist = false) {
    // 检查权限
    const user = this.getCurrentUser();
    if (persist && user) {
      const hasPermission = await permissionManager.checkPermission(user, 'SYSTEM_CONFIG_EDIT');
      if (!hasPermission) {
        throw new Error('无权限修改系统配置');
      }
    }

    // 设置本地配置
    this.setNestedValue(this.mergedConfigs, key, value);
    
    // 清除相关缓存
    this.clearCacheByPrefix(`config:${key}`);
    
    // 持久化到服务器
    if (persist) {
      try {
        await this.persistConfig(key, value);
      } catch (error) {
        console.error('持久化配置失败:', error);
        throw error;
      }
    }

    // 触发配置更新事件
    this.emitConfigChange('set', { key, value });
  }

  /**
   * 获取嵌套对象的值
   */
  getNestedValue(obj, path, defaultValue) {
    const keys = path.split('.');
    let current = obj;

    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }

    return current;
  }

  /**
   * 设置嵌套对象的值
   */
  setNestedValue(obj, path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let current = obj;

    for (const key of keys) {
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }

    current[lastKey] = value;
  }

  /**
   * 深度合并对象
   */
  deepMerge(target, source) {
    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(result[key] || {}, source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }

    return result;
  }

  /**
   * 持久化配置到服务器
   */
  async persistConfig(key, value) {
    const user = this.getCurrentUser();
    const configData = {
      key,
      value,
      scope: user?.tenant_id ? 'tenant' : 'system',
      tenant_id: user?.tenant_id
    };

    await ultimateAPIClient.post('/api/v2/system/config', configData);
  }

  /**
   * 获取所有配置
   */
  getAll() {
    return { ...this.mergedConfigs };
  }

  /**
   * 获取指定前缀的所有配置
   */
  getByPrefix(prefix) {
    const result = {};
    const keys = this.getAllKeys(this.mergedConfigs);
    
    keys.forEach(key => {
      if (key.startsWith(prefix)) {
        result[key] = this.get(key);
      }
    });

    return result;
  }

  /**
   * 获取所有配置键
   */
  getAllKeys(obj, prefix = '') {
    const keys = [];
    
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        
        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          keys.push(...this.getAllKeys(obj[key], fullKey));
        } else {
          keys.push(fullKey);
        }
      }
    }

    return keys;
  }

  /**
   * 验证配置
   */
  validateConfig(key, value) {
    const validators = {
      'api.timeout': (val) => typeof val === 'number' && val > 0 && val <= 60000,
      'api.retryTimes': (val) => typeof val === 'number' && val >= 0 && val <= 10,
      'cache.maxSize': (val) => typeof val === 'number' && val > 0,
      'ui.pageSize': (val) => typeof val === 'number' && val > 0 && val <= 100
    };

    const validator = validators[key];
    if (validator && !validator(value)) {
      throw new Error(`配置值验证失败: ${key} = ${value}`);
    }

    return true;
  }

  /**
   * 重置配置
   */
  async reset(key) {
    if (key) {
      // 重置单个配置
      const defaultValue = this.getNestedValue(this.defaultConfigs, key);
      await this.set(key, defaultValue, true);
    } else {
      // 重置所有配置
      this.mergedConfigs = { ...this.defaultConfigs };
      this.configCache.clear();
      
      // 触发配置重置事件
      this.emitConfigChange('reset', null);
    }
  }

  /**
   * 启动配置同步
   */
  startConfigSync() {
    // 每5分钟同步一次远程配置
    this.syncInterval = setInterval(async () => {
      try {
        await this.loadRemoteConfigs();
      } catch (error) {
        console.error('配置同步失败:', error);
      }
    }, 5 * 60 * 1000);
  }

  /**
   * 监听配置变更
   */
  onChange(key, callback) {
    if (!this.configListeners.has(key)) {
      this.configListeners.set(key, []);
    }
    this.configListeners.get(key).push(callback);
  }

  /**
   * 移除配置监听
   */
  offChange(key, callback) {
    if (this.configListeners.has(key)) {
      const listeners = this.configListeners.get(key);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发配置变更事件
   */
  emitConfigChange(type, data) {
    // 触发全局监听器
    const globalListeners = this.configListeners.get('*') || [];
    globalListeners.forEach(callback => {
      try {
        callback(type, data);
      } catch (error) {
        console.error('配置监听器错误:', error);
      }
    });

    // 触发特定键的监听器
    if (data && data.key) {
      const keyListeners = this.configListeners.get(data.key) || [];
      keyListeners.forEach(callback => {
        try {
          callback(type, data);
        } catch (error) {
          console.error('配置监听器错误:', error);
        }
      });
    }
  }

  // 缓存管理
  setCache(key, value) {
    if (this.configCache.size >= this.cacheConfig.maxSize) {
      const oldestKey = this.configCache.keys().next().value;
      this.configCache.delete(oldestKey);
    }

    this.configCache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  clearCache() {
    this.configCache.clear();
  }

  clearCacheByPrefix(prefix) {
    for (const [key] of this.configCache) {
      if (key.startsWith(prefix)) {
        this.configCache.delete(key);
      }
    }
  }

  // 辅助方法
  getCurrentUser() {
    try {
      const app = getApp();
      return app?.globalData?.user || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取配置统计信息
   */
  getStats() {
    return {
      environment: this.currentEnvironment,
      totalConfigs: this.getAllKeys(this.mergedConfigs).length,
      cacheSize: this.configCache.size,
      cacheHitRate: '85%', // 简化统计
      lastSyncTime: new Date().toISOString(),
      listeners: Array.from(this.configListeners.keys()).length
    };
  }

  /**
   * 导出配置
   */
  export(format = 'json') {
    const configs = this.getAll();
    
    switch (format) {
      case 'json':
        return JSON.stringify(configs, null, 2);
      case 'yaml':
        // 简化的YAML导出
        return this.toYAML(configs);
      default:
        return configs;
    }
  }

  /**
   * 简化的YAML转换
   */
  toYAML(obj, indent = 0) {
    const spaces = '  '.repeat(indent);
    let yaml = '';

    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        yaml += `${spaces}${key}:\n${this.toYAML(value, indent + 1)}`;
      } else {
        yaml += `${spaces}${key}: ${JSON.stringify(value)}\n`;
      }
    }

    return yaml;
  }

  /**
   * 销毁配置管理器
   */
  destroy() {
    // 清理定时器
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    // 清理缓存和监听器
    this.configCache.clear();
    this.configListeners.clear();

    console.log('[ConfigManager] 配置管理器已销毁');
  }
}

// 创建全局实例
const configManager = new UnifiedConfigManager();

module.exports = {
  UnifiedConfigManager,
  configManager
};
