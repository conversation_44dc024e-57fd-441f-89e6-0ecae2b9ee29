/**
 * API接口管理（云开发兼容层）
 * - 优先使用云函数调用
 * - 回退到传统HTTP请求
 * - 保留原有导出结构，确保兼容性
 */

const request = require('./request.js');
const { API: API_CONST } = require('../constants/index.js');
const { cloudAPIAdapter } = require('./cloud-api-adapter.js');

// 智能API调用函数：优先使用云函数，回退到HTTP请求
async function smartApiCall(method, url, data = {}) {
  try {
    const app = getApp();

    // 检查云开发是否可用
    if (app.globalData.isCloudEnabled) {
      try {
        console.log(`使用云函数调用: ${method} ${url}`);
        return await cloudAPIAdapter.request(method, url, data, { fallbackToHttp: true });
      } catch (cloudError) {
        console.warn('云函数调用失败，回退到HTTP请求:', cloudError);
        // 继续执行HTTP请求
      }
    }

    // 回退到传统HTTP请求
    console.log(`使用HTTP请求: ${method} ${url}`);
    return await httpApiCall(method, url, data);

  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
}

// 传统HTTP API调用
function httpApiCall(method, url, data = {}) {
  const relativeUrl = toRelative(url);

  switch (method.toUpperCase()) {
    case 'GET':
      return request.get(relativeUrl, data);
    case 'POST':
      return request.post(relativeUrl, data);
    case 'PUT':
      return request.put(relativeUrl, data);
    case 'DELETE':
      return request.del(relativeUrl);
    default:
      throw new Error(`不支持的HTTP方法: ${method}`);
  }
}

// 适配函数：将完整URL映射为相对路径供 request 使用
function toRelative(url) {
  try {
    if (!url) {
      throw new Error('URL不能为空');
    }
    const base = API_CONST.BASE_URL; // 如 http://localhost:3001
    if (!base) {
      // 如果没有基础URL，直接返回原URL
      return url;
    }
    return url.replace(base, '');
  } catch (e) {
    console.warn('toRelative转换失败:', e, 'url:', url);
    return url || '';
  }
}

// 根据常量重新组织模块化API（支持云开发和HTTP双模式）
const auth = {
  login: (data) => smartApiCall('POST', API_CONST.ENDPOINTS.AUTH.LOGIN, data),
  logout: () => smartApiCall('POST', API_CONST.ENDPOINTS.AUTH.LOGOUT),
  getUserInfo: () => smartApiCall('GET', API_CONST.ENDPOINTS.AUTH.USER_INFO),
  changePassword: (data) => smartApiCall('PUT', API_CONST.ENDPOINTS.AUTH.CHANGE_PASSWORD, data)
};

const home = {
  getHomeData: () => smartApiCall('GET', API_CONST.ENDPOINTS.HOME.DATA),
  getAnnouncements: (params) => smartApiCall('GET', API_CONST.ENDPOINTS.HOME.ANNOUNCEMENTS + '?' + new URLSearchParams(params).toString())
};

const production = {
  // 健康记录相关（生产健康监测）
  getRecords: (params) => smartApiCall('GET', API_CONST.ENDPOINTS.PRODUCTION.RECORDS + '?' + new URLSearchParams(params).toString()),
  getRecordDetail: (id) => smartApiCall('GET', API_CONST.ENDPOINTS.PRODUCTION.RECORD_DETAIL(id)),
  createRecord: (data) => smartApiCall('POST', API_CONST.ENDPOINTS.PRODUCTION.CREATE_RECORD, data),
  updateRecord: (id, data) => smartApiCall('PUT', API_CONST.ENDPOINTS.PRODUCTION.UPDATE_RECORD(id), data),
  deleteRecord: (id) => smartApiCall('DELETE', API_CONST.ENDPOINTS.PRODUCTION.DELETE_RECORD(id)),
  aiDiagnosis: (data) => smartApiCall('POST', API_CONST.ENDPOINTS.PRODUCTION.AI_DIAGNOSIS, data),
  getKnowledgeList: (params) => smartApiCall('GET', API_CONST.ENDPOINTS.PRODUCTION.KNOWLEDGE + '?' + new URLSearchParams(params).toString()),
  getKnowledgeDetail: (id) => request.get(toRelative(API_CONST.ENDPOINTS.PRODUCTION.RECORD_DETAIL(id))),
  getReport: (params) => request.get(toRelative(API_CONST.ENDPOINTS.PRODUCTION.REPORTS), params),

  // 生产环境和财务相关
  getEnvironmentData: (params) => smartApiCall('GET', API_CONST.ENDPOINTS.PRODUCTION.ENVIRONMENT + '?' + new URLSearchParams(params).toString()),
  getFinanceData: (params) => smartApiCall('GET', API_CONST.ENDPOINTS.PRODUCTION.FINANCE + '?' + new URLSearchParams(params).toString()),
  createPurchaseRequest: (data) => smartApiCall('POST', API_CONST.ENDPOINTS.PRODUCTION.PURCHASE_REQUESTS, data),
  createReimbursementRequest: (data) => smartApiCall('POST', API_CONST.ENDPOINTS.PRODUCTION.REIMBURSEMENT_REQUESTS, data),
  saveAIInventoryRecord: (data) => smartApiCall('POST', API_CONST.ENDPOINTS.PRODUCTION.AI_INVENTORY, data),
  getAIInventoryRecords: (params) => smartApiCall('GET', API_CONST.ENDPOINTS.PRODUCTION.AI_INVENTORY + '?' + new URLSearchParams(params).toString())
};

const profile = {
  getSettings: () => smartApiCall('GET', API_CONST.ENDPOINTS.PROFILE.SETTINGS),
  updateSettings: (data) => smartApiCall('PUT', API_CONST.ENDPOINTS.PROFILE.SETTINGS, data),
  getHelpList: () => smartApiCall('GET', API_CONST.ENDPOINTS.PROFILE.HELP)
};

module.exports = {
  API_ENDPOINTS: API_CONST.ENDPOINTS,
  auth,
  home,
  production, // 将原来的 health 改为 production
  profile,
  API: API_CONST.ENDPOINTS
};