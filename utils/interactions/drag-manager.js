/**
 * 拖拽管理器模块
 * 提供元素拖拽功能，支持拖拽排序、拖拽到目标区域等
 */

const { GestureRecognizer } = require('./gesture-recognizer.js');

class DragManager {
  constructor(options = {}) {
    this.options = {
      threshold: 10,           // 开始拖拽的最小距离
      ghostOpacity: 0.5,       // 拖拽时的透明度
      autoScroll: true,        // 自动滚动
      scrollSpeed: 10,         // 滚动速度
      scrollThreshold: 50,     // 滚动触发阈值
      ...options
    };
    
    this.draggableItems = new Set();
    this.dropZones = new Set();
    this.currentDrag = null;
    this.ghostElement = null;
    this.scrollTimer = null;
  }

  // 使元素可拖拽
  makeDraggable(element, data = {}) {
    if (this.draggableItems.has(element)) {
      return; // 已经是可拖拽的
    }

    const gestureRecognizer = new GestureRecognizer(element, {
      threshold: this.options.threshold
    });

    const dragData = {
      element: element,
      data: data,
      gestureRecognizer: gestureRecognizer,
      originalPosition: null,
      isDragging: false
    };

    // 绑定拖拽事件
    gestureRecognizer.on('touchstart', (e) => {
      this.onDragStart(dragData, e);
    });

    gestureRecognizer.on('touchmove', (e) => {
      this.onDragMove(dragData, e);
    });

    gestureRecognizer.on('touchend', (e) => {
      this.onDragEnd(dragData, e);
    });

    gestureRecognizer.on('touchcancel', (e) => {
      this.onDragCancel(dragData, e);
    });

    element._dragData = dragData;
    this.draggableItems.add(element);
  }

  // 移除拖拽功能
  removeDraggable(element) {
    if (!this.draggableItems.has(element)) {
      return;
    }

    const dragData = element._dragData;
    if (dragData && dragData.gestureRecognizer) {
      dragData.gestureRecognizer.destroy();
    }

    delete element._dragData;
    this.draggableItems.delete(element);
  }

  // 添加放置区域
  addDropZone(element, options = {}) {
    const dropZoneData = {
      element: element,
      accept: options.accept || '*',  // 接受的拖拽类型
      onDragEnter: options.onDragEnter,
      onDragLeave: options.onDragLeave,
      onDrop: options.onDrop,
      isActive: false
    };

    element._dropZoneData = dropZoneData;
    this.dropZones.add(element);
  }

  // 移除放置区域
  removeDropZone(element) {
    if (!this.dropZones.has(element)) {
      return;
    }

    delete element._dropZoneData;
    this.dropZones.delete(element);
  }

  onDragStart(dragData, e) {
    const rect = dragData.element.getBoundingClientRect();
    dragData.originalPosition = {
      x: rect.left,
      y: rect.top,
      width: rect.width,
      height: rect.height
    };

    // 创建拖拽提示
    this.createDragFeedback(dragData, e.point);
    
    // 触发拖拽开始事件
    this.triggerEvent('dragstart', {
      element: dragData.element,
      data: dragData.data,
      position: e.point
    });
  }

  onDragMove(dragData, e) {
    if (!dragData.isDragging && e.distance > this.options.threshold) {
      // 开始拖拽
      dragData.isDragging = true;
      dragData.element.classList.add('dragging');
      this.currentDrag = dragData;
      
      // 显示拖拽幽灵
      this.showGhost(dragData);
    }

    if (dragData.isDragging) {
      // 更新幽灵位置
      this.updateGhostPosition(e.point);
      
      // 检查放置区域
      this.checkDropZones(e.point);
      
      // 自动滚动
      if (this.options.autoScroll) {
        this.handleAutoScroll(e.point);
      }
      
      // 触发拖拽移动事件
      this.triggerEvent('dragmove', {
        element: dragData.element,
        data: dragData.data,
        position: e.point,
        delta: e.delta
      });
    }
  }

  onDragEnd(dragData, e) {
    if (dragData.isDragging) {
      // 检查是否在放置区域内
      const dropZone = this.getDropZoneAt(e.point);
      
      if (dropZone && this.canDrop(dragData, dropZone)) {
        // 执行放置
        this.performDrop(dragData, dropZone, e.point);
      } else {
        // 返回原位置
        this.returnToOriginalPosition(dragData);
      }
      
      // 清理拖拽状态
      this.cleanupDrag(dragData);
      
      // 触发拖拽结束事件
      this.triggerEvent('dragend', {
        element: dragData.element,
        data: dragData.data,
        position: e.point,
        dropped: !!dropZone
      });
    }
  }

  onDragCancel(dragData, e) {
    if (dragData.isDragging) {
      this.returnToOriginalPosition(dragData);
      this.cleanupDrag(dragData);
      
      this.triggerEvent('dragcancel', {
        element: dragData.element,
        data: dragData.data
      });
    }
  }

  createDragFeedback(dragData, point) {
    // 添加视觉反馈
    dragData.element.style.transform = 'scale(1.05)';
    dragData.element.style.transition = 'transform 0.2s ease';
    
    setTimeout(() => {
      if (dragData.element.style) {
        dragData.element.style.transition = '';
      }
    }, 200);
  }

  showGhost(dragData) {
    // 创建拖拽幽灵元素
    this.ghostElement = dragData.element.cloneNode(true);
    this.ghostElement.classList.add('drag-ghost');
    this.ghostElement.style.position = 'fixed';
    this.ghostElement.style.opacity = this.options.ghostOpacity;
    this.ghostElement.style.pointerEvents = 'none';
    this.ghostElement.style.zIndex = '9999';
    this.ghostElement.style.transform = 'scale(0.9)';
    
    document.body.appendChild(this.ghostElement);
  }

  updateGhostPosition(point) {
    if (this.ghostElement) {
      this.ghostElement.style.left = (point.x - this.ghostElement.offsetWidth / 2) + 'px';
      this.ghostElement.style.top = (point.y - this.ghostElement.offsetHeight / 2) + 'px';
    }
  }

  checkDropZones(point) {
    this.dropZones.forEach(dropZone => {
      const dropZoneData = dropZone._dropZoneData;
      const rect = dropZone.getBoundingClientRect();
      
      const isInside = point.x >= rect.left && 
                      point.x <= rect.right && 
                      point.y >= rect.top && 
                      point.y <= rect.bottom;
      
      if (isInside && !dropZoneData.isActive) {
        // 进入放置区域
        dropZoneData.isActive = true;
        dropZone.classList.add('drag-over');
        
        if (dropZoneData.onDragEnter) {
          dropZoneData.onDragEnter(this.currentDrag);
        }
        
        this.triggerEvent('dragenter', {
          dropZone: dropZone,
          dragData: this.currentDrag
        });
      } else if (!isInside && dropZoneData.isActive) {
        // 离开放置区域
        dropZoneData.isActive = false;
        dropZone.classList.remove('drag-over');
        
        if (dropZoneData.onDragLeave) {
          dropZoneData.onDragLeave(this.currentDrag);
        }
        
        this.triggerEvent('dragleave', {
          dropZone: dropZone,
          dragData: this.currentDrag
        });
      }
    });
  }

  getDropZoneAt(point) {
    for (let dropZone of this.dropZones) {
      const rect = dropZone.getBoundingClientRect();
      if (point.x >= rect.left && 
          point.x <= rect.right && 
          point.y >= rect.top && 
          point.y <= rect.bottom) {
        return dropZone;
      }
    }
    return null;
  }

  canDrop(dragData, dropZone) {
    const dropZoneData = dropZone._dropZoneData;
    if (dropZoneData.accept === '*') {
      return true;
    }
    
    // 检查类型匹配
    return dragData.data.type && dropZoneData.accept.includes(dragData.data.type);
  }

  performDrop(dragData, dropZone, point) {
    const dropZoneData = dropZone._dropZoneData;
    
    if (dropZoneData.onDrop) {
      dropZoneData.onDrop(dragData, point);
    }
    
    this.triggerEvent('drop', {
      dragData: dragData,
      dropZone: dropZone,
      position: point
    });
  }

  returnToOriginalPosition(dragData) {
    // 动画返回原位置
    if (dragData.originalPosition) {
      dragData.element.style.transition = 'transform 0.3s ease';
      dragData.element.style.transform = 'translate(0, 0)';
      
      setTimeout(() => {
        if (dragData.element.style) {
          dragData.element.style.transition = '';
          dragData.element.style.transform = '';
        }
      }, 300);
    }
  }

  cleanupDrag(dragData) {
    // 清理拖拽状态
    dragData.isDragging = false;
    dragData.element.classList.remove('dragging');
    this.currentDrag = null;
    
    // 移除幽灵元素
    if (this.ghostElement) {
      document.body.removeChild(this.ghostElement);
      this.ghostElement = null;
    }
    
    // 清理放置区域状态
    this.dropZones.forEach(dropZone => {
      const dropZoneData = dropZone._dropZoneData;
      dropZoneData.isActive = false;
      dropZone.classList.remove('drag-over');
    });
    
    // 停止自动滚动
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
      this.scrollTimer = null;
    }
  }

  handleAutoScroll(point) {
    const viewport = {
      top: window.pageYOffset,
      bottom: window.pageYOffset + window.innerHeight,
      left: window.pageXOffset,
      right: window.pageXOffset + window.innerWidth
    };
    
    let scrollX = 0;
    let scrollY = 0;
    
    // 检查是否需要滚动
    if (point.y < viewport.top + this.options.scrollThreshold) {
      scrollY = -this.options.scrollSpeed;
    } else if (point.y > viewport.bottom - this.options.scrollThreshold) {
      scrollY = this.options.scrollSpeed;
    }
    
    if (point.x < viewport.left + this.options.scrollThreshold) {
      scrollX = -this.options.scrollSpeed;
    } else if (point.x > viewport.right - this.options.scrollThreshold) {
      scrollX = this.options.scrollSpeed;
    }
    
    if (scrollX !== 0 || scrollY !== 0) {
      if (!this.scrollTimer) {
        this.scrollTimer = setInterval(() => {
          window.scrollBy(scrollX, scrollY);
        }, 16); // 60fps
      }
    } else if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
      this.scrollTimer = null;
    }
  }

  // 事件系统
  triggerEvent(eventName, data) {
    const event = new CustomEvent(`drag${eventName}`, {
      detail: data,
      bubbles: true,
      cancelable: true
    });
    
    if (data.element) {
      data.element.dispatchEvent(event);
    } else {
      document.dispatchEvent(event);
    }
  }

  // 销毁
  destroy() {
    this.draggableItems.forEach(element => {
      this.removeDraggable(element);
    });
    
    this.dropZones.forEach(element => {
      this.removeDropZone(element);
    });
    
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
    }
    
    if (this.ghostElement) {
      document.body.removeChild(this.ghostElement);
    }
  }
}

module.exports = {
  DragManager
};
