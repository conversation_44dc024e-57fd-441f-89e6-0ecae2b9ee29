/**
 * 高级交互功能统一导出文件
 * 替换原来的 utils/advanced-interactions.js 大文件
 * 提供向后兼容的接口
 */

// 导入所有模块
const { GestureRecognizer } = require('./gesture-recognizer.js');
const { DragManager } = require('./drag-manager.js');
const { SwipeManager, swipeManager, SwipeUtils } = require('./swipe-manager.js');
const { AdvancedInteractionManager, advancedInteractionManager } = require('./advanced-interaction-manager.js');
const { AdvancedInteractionMixin, AdvancedInteractionComponentMixin, QuickSetup } = require('./interaction-mixin.js');

// 向后兼容的全局实例（保持原有接口）
const globalInstances = {
  gestureRecognizer: null,
  dragManager: new DragManager(),
  swipeManager: swipeManager,
  interactionManager: advancedInteractionManager
};

// 向后兼容的便捷方法
const CompatibilityAPI = {
  // 创建手势识别器（兼容原接口）
  createGestureRecognizer(element, options = {}) {
    return advancedInteractionManager.createGestureRecognizer(element, options);
  },

  // 拖拽相关方法
  makeDraggable(element, data = {}) {
    return advancedInteractionManager.makeDraggable(element, data);
  },

  addDropZone(element, options = {}) {
    return advancedInteractionManager.addDropZone(element, options);
  },

  // 滑动相关方法
  makeSwipeable(element, actions = []) {
    return advancedInteractionManager.makeSwipeable(element, actions);
  },

  // 长按选择
  createLongPressSelection(elements, options = {}) {
    return advancedInteractionManager.createLongPressSelection(elements, options);
  },

  // 触摸反馈
  createTouchFeedback(element, options = {}) {
    return advancedInteractionManager.createTouchFeedback(element, options);
  },

  // 惯性滚动
  createInertialScroll(element, options = {}) {
    return advancedInteractionManager.createInertialScroll(element, options);
  },

  // 磁性吸附
  createMagneticSnap(draggableElement, snapTargets, options = {}) {
    return advancedInteractionManager.createMagneticSnap(draggableElement, snapTargets, options);
  }
};

// 高级功能API
const AdvancedAPI = {
  // 批量设置交互
  batchSetup: {
    // 批量设置手势识别
    gestures(elements, options = {}) {
      const recognizers = [];
      elements.forEach(element => {
        const recognizer = advancedInteractionManager.createGestureRecognizer(element, options);
        recognizers.push(recognizer);
      });
      return recognizers;
    },

    // 批量设置拖拽
    draggable(elements, dataProvider = () => ({})) {
      elements.forEach((element, index) => {
        const data = typeof dataProvider === 'function' ? dataProvider(element, index) : dataProvider;
        advancedInteractionManager.makeDraggable(element, data);
      });
    },

    // 批量设置滑动
    swipeable(elements, actionsProvider = () => []) {
      elements.forEach((element, index) => {
        const actions = typeof actionsProvider === 'function' ? actionsProvider(element, index) : actionsProvider;
        advancedInteractionManager.makeSwipeable(element, actions);
      });
    },

    // 批量设置触摸反馈
    touchFeedback(elements, options = {}) {
      elements.forEach(element => {
        advancedInteractionManager.createTouchFeedback(element, options);
      });
    }
  },

  // 交互链式调用
  chain(element) {
    return {
      gesture(options = {}) {
        advancedInteractionManager.createGestureRecognizer(element, options);
        return this;
      },
      
      draggable(data = {}) {
        advancedInteractionManager.makeDraggable(element, data);
        return this;
      },
      
      swipeable(actions = []) {
        advancedInteractionManager.makeSwipeable(element, actions);
        return this;
      },
      
      touchFeedback(options = {}) {
        advancedInteractionManager.createTouchFeedback(element, options);
        return this;
      },
      
      dropZone(options = {}) {
        advancedInteractionManager.addDropZone(element, options);
        return this;
      }
    };
  },

  // 预设交互模式
  presets: {
    // 列表项预设（滑动删除 + 触摸反馈）
    listItem(element, options = {}) {
      const { onDelete, onEdit, touchFeedback = true } = options;
      
      // 设置触摸反馈
      if (touchFeedback) {
        advancedInteractionManager.createTouchFeedback(element);
      }
      
      // 设置滑动操作
      const actions = [];
      if (onEdit) {
        actions.push({
          text: '编辑',
          color: '#3742fa',
          type: 'edit',
          handler: onEdit
        });
      }
      if (onDelete) {
        actions.push({
          text: '删除',
          color: '#ff4757',
          type: 'delete',
          handler: onDelete
        });
      }
      
      if (actions.length > 0) {
        advancedInteractionManager.makeSwipeable(element, actions);
      }
    },

    // 卡片预设（拖拽 + 触摸反馈 + 长按选择）
    card(element, options = {}) {
      const { draggable = true, touchFeedback = true, data = {} } = options;
      
      if (touchFeedback) {
        advancedInteractionManager.createTouchFeedback(element);
      }
      
      if (draggable) {
        advancedInteractionManager.makeDraggable(element, data);
      }
    },

    // 按钮预设（触摸反馈 + 手势识别）
    button(element, options = {}) {
      const { onTap, onLongPress, touchFeedback = true } = options;
      
      if (touchFeedback) {
        advancedInteractionManager.createTouchFeedback(element, {
          scale: 0.95,
          opacity: 0.8
        });
      }
      
      const recognizer = advancedInteractionManager.createGestureRecognizer(element);
      
      if (onTap) {
        recognizer.on('tap', onTap);
      }
      
      if (onLongPress) {
        recognizer.on('longpress', onLongPress);
      }
      
      return recognizer;
    }
  }
};

// 工具函数
const InteractionUtils = {
  // 检测设备支持
  support: {
    touch: 'ontouchstart' in window,
    pointer: 'onpointerdown' in window,
    gesture: 'ongesturestart' in window
  },

  // 坐标转换
  coordinates: {
    // 获取元素相对坐标
    getRelativePosition(element, point) {
      const rect = element.getBoundingClientRect();
      return {
        x: point.x - rect.left,
        y: point.y - rect.top
      };
    },

    // 获取元素中心点
    getElementCenter(element) {
      const rect = element.getBoundingClientRect();
      return {
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      };
    },

    // 计算两点距离
    getDistance(point1, point2) {
      const dx = point1.x - point2.x;
      const dy = point1.y - point2.y;
      return Math.sqrt(dx * dx + dy * dy);
    }
  },

  // 动画辅助
  animation: {
    // 缓动函数
    easing: {
      linear: t => t,
      easeInQuad: t => t * t,
      easeOutQuad: t => t * (2 - t),
      easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
      easeInCubic: t => t * t * t,
      easeOutCubic: t => (--t) * t * t + 1,
      easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
    },

    // 创建动画
    animate(element, properties, duration = 300, easing = 'easeOutQuad') {
      return new Promise(resolve => {
        const startTime = Date.now();
        const startValues = {};
        const endValues = properties;
        
        // 获取初始值
        Object.keys(properties).forEach(prop => {
          const computedStyle = getComputedStyle(element);
          startValues[prop] = parseFloat(computedStyle[prop]) || 0;
        });
        
        const easingFunc = this.easing[easing] || this.easing.easeOutQuad;
        
        const step = () => {
          const elapsed = Date.now() - startTime;
          const progress = Math.min(elapsed / duration, 1);
          const easedProgress = easingFunc(progress);
          
          Object.keys(properties).forEach(prop => {
            const start = startValues[prop];
            const end = endValues[prop];
            const current = start + (end - start) * easedProgress;
            element.style[prop] = current + (prop.includes('opacity') ? '' : 'px');
          });
          
          if (progress < 1) {
            requestAnimationFrame(step);
          } else {
            resolve();
          }
        };
        
        requestAnimationFrame(step);
      });
    }
  }
};

// 主要导出对象（保持向后兼容）
module.exports = {
  // 核心类
  GestureRecognizer,
  DragManager,
  SwipeManager,
  AdvancedInteractionManager,

  // 全局实例
  advancedInteractionManager,
  swipeManager,

  // 混入对象
  AdvancedInteractionMixin,
  AdvancedInteractionComponentMixin,

  // 兼容性API（保持原有接口）
  ...CompatibilityAPI,

  // 高级API
  ...AdvancedAPI,

  // 快速设置
  QuickSetup,

  // 工具函数
  InteractionUtils,

  // 便捷工具
  SwipeUtils,

  // 全局实例（向后兼容）
  ...globalInstances
};
