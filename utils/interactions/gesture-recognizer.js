/**
 * 手势识别器模块
 * 提供触摸手势识别功能，包括点击、长按、滑动、缩放等
 */

class GestureRecognizer {
  constructor(element, options = {}) {
    this.element = element;
    this.options = {
      threshold: 10,           // 最小移动距离
      longPressTime: 500,      // 长按时间阈值
      swipeThreshold: 50,      // 滑动距离阈值
      pinchThreshold: 10,      // 缩放阈值
      ...options
    };
    
    this.state = {
      isTracking: false,
      startPoint: null,
      currentPoint: null,
      startTime: 0,
      touches: []
    };
    
    this.callbacks = {};
    this.longPressTimer = null;
    
    this.init();
  }

  init() {
    this.bindEvents();
  }

  bindEvents() {
    // 绑定触摸事件
    this.element.addEventListener('touchstart', this.onTouchStart.bind(this));
    this.element.addEventListener('touchmove', this.onTouchMove.bind(this));
    this.element.addEventListener('touchend', this.onTouchEnd.bind(this));
    this.element.addEventListener('touchcancel', this.onTouchCancel.bind(this));
  }

  onTouchStart(e) {
    const touch = e.touches[0];
    this.state.isTracking = true;
    this.state.startPoint = { x: touch.clientX, y: touch.clientY };
    this.state.currentPoint = { ...this.state.startPoint };
    this.state.startTime = Date.now();
    this.state.touches = Array.from(e.touches);

    // 开始长按计时
    this.startLongPressTimer();

    // 触发touchstart事件
    this.trigger('touchstart', {
      point: this.state.startPoint,
      touches: this.state.touches,
      originalEvent: e
    });

    // 多点触控检测
    if (e.touches.length > 1) {
      this.handleMultiTouch(e);
    }
  }

  onTouchMove(e) {
    if (!this.state.isTracking) return;

    const touch = e.touches[0];
    const newPoint = { x: touch.clientX, y: touch.clientY };
    const deltaX = newPoint.x - this.state.startPoint.x;
    const deltaY = newPoint.y - this.state.startPoint.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    this.state.currentPoint = newPoint;

    // 如果移动距离超过阈值，取消长按
    if (distance > this.options.threshold) {
      this.clearLongPressTimer();
    }

    // 触发move事件
    this.trigger('touchmove', {
      point: newPoint,
      delta: { x: deltaX, y: deltaY },
      distance: distance,
      originalEvent: e
    });

    // 多点触控处理
    if (e.touches.length > 1) {
      this.handleMultiTouch(e);
    }
  }

  onTouchEnd(e) {
    if (!this.state.isTracking) return;

    this.clearLongPressTimer();
    
    const deltaX = this.state.currentPoint.x - this.state.startPoint.x;
    const deltaY = this.state.currentPoint.y - this.state.startPoint.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const duration = Date.now() - this.state.startTime;

    // 判断手势类型
    if (distance < this.options.threshold) {
      // 点击
      this.trigger('tap', {
        point: this.state.currentPoint,
        duration: duration,
        originalEvent: e
      });
    } else if (distance > this.options.swipeThreshold) {
      // 滑动
      const direction = this.getSwipeDirection(deltaX, deltaY);
      this.trigger('swipe', {
        direction: direction,
        distance: distance,
        delta: { x: deltaX, y: deltaY },
        originalEvent: e
      });
    }

    // 触发touchend事件
    this.trigger('touchend', {
      point: this.state.currentPoint,
      delta: { x: deltaX, y: deltaY },
      distance: distance,
      duration: duration,
      originalEvent: e
    });

    this.resetState();
  }

  onTouchCancel(e) {
    this.clearLongPressTimer();
    this.trigger('touchcancel', { originalEvent: e });
    this.resetState();
  }

  handleMultiTouch(e) {
    if (e.touches.length === 2) {
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      
      const distance = this.getTouchDistance(touch1, touch2);
      
      if (!this.state.initialPinchDistance) {
        this.state.initialPinchDistance = distance;
        return;
      }
      
      const scale = distance / this.state.initialPinchDistance;
      const center = {
        x: (touch1.clientX + touch2.clientX) / 2,
        y: (touch1.clientY + touch2.clientY) / 2
      };
      
      this.trigger('pinch', {
        scale: scale,
        center: center,
        distance: distance,
        originalEvent: e
      });
    }
  }

  getTouchDistance(touch1, touch2) {
    const deltaX = touch1.clientX - touch2.clientX;
    const deltaY = touch1.clientY - touch2.clientY;
    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  }

  getSwipeDirection(deltaX, deltaY) {
    const absX = Math.abs(deltaX);
    const absY = Math.abs(deltaY);
    
    if (absX > absY) {
      return deltaX > 0 ? 'right' : 'left';
    } else {
      return deltaY > 0 ? 'down' : 'up';
    }
  }

  startLongPressTimer() {
    this.longPressTimer = setTimeout(() => {
      if (this.state.isTracking) {
        this.trigger('longpress', {
          point: this.state.currentPoint,
          duration: Date.now() - this.state.startTime
        });
      }
    }, this.options.longPressTime);
  }

  clearLongPressTimer() {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  resetState() {
    this.state.isTracking = false;
    this.state.startPoint = null;
    this.state.currentPoint = null;
    this.state.startTime = 0;
    this.state.touches = [];
    this.state.initialPinchDistance = null;
  }

  // 事件系统
  on(event, callback) {
    if (!this.callbacks[event]) {
      this.callbacks[event] = [];
    }
    this.callbacks[event].push(callback);
    return this;
  }

  off(event, callback) {
    if (!this.callbacks[event]) return this;
    
    if (callback) {
      const index = this.callbacks[event].indexOf(callback);
      if (index > -1) {
        this.callbacks[event].splice(index, 1);
      }
    } else {
      this.callbacks[event] = [];
    }
    return this;
  }

  trigger(event, data = {}) {
    if (!this.callbacks[event]) return this;
    
    this.callbacks[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('手势识别器回调执行错误:', error);
      }
    });
    return this;
  }

  // 销毁
  destroy() {
    this.clearLongPressTimer();
    this.element.removeEventListener('touchstart', this.onTouchStart);
    this.element.removeEventListener('touchmove', this.onTouchMove);
    this.element.removeEventListener('touchend', this.onTouchEnd);
    this.element.removeEventListener('touchcancel', this.onTouchCancel);
    this.callbacks = {};
    this.resetState();
  }
}

module.exports = {
  GestureRecognizer
};
