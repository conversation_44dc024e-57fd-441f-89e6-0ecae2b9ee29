/**
 * 高级交互管理器主模块
 * 整合所有交互功能，提供统一的接口
 */

const { GestureRecognizer } = require('./gesture-recognizer.js');
const { DragManager } = require('./drag-manager.js');
const { SwipeManager, swipeManager } = require('./swipe-manager.js');

class AdvancedInteractionManager {
  constructor() {
    this.gestureRecognizers = new Map();
    this.dragManager = new DragManager();
    this.swipeManager = swipeManager;
    this.doubleTapTimeout = 300; // 双击间隔时间
    this.lastTapTime = 0;
    this.lastTapElement = null;
  }

  // 创建手势识别器
  createGestureRecognizer(element, options = {}) {
    if (this.gestureRecognizers.has(element)) {
      return this.gestureRecognizers.get(element);
    }

    const recognizer = new GestureRecognizer(element, options);
    this.gestureRecognizers.set(element, recognizer);
    
    // 添加双击检测
    recognizer.on('tap', (e) => {
      this.handleDoubleTap(element, e);
    });

    return recognizer;
  }

  // 处理双击事件
  handleDoubleTap(element, e) {
    const currentTime = Date.now();
    
    if (this.lastTapElement === element && 
        currentTime - this.lastTapTime < this.doubleTapTimeout) {
      // 双击事件
      const event = new CustomEvent('doubletap', {
        detail: {
          point: e.point,
          originalEvent: e.originalEvent
        },
        bubbles: true,
        cancelable: true
      });
      element.dispatchEvent(event);
      
      // 重置状态
      this.lastTapTime = 0;
      this.lastTapElement = null;
    } else {
      // 记录单击
      this.lastTapTime = currentTime;
      this.lastTapElement = element;
    }
  }

  // 移除手势识别器
  removeGestureRecognizer(element) {
    const recognizer = this.gestureRecognizers.get(element);
    if (recognizer) {
      recognizer.destroy();
      this.gestureRecognizers.delete(element);
    }
  }

  // 使元素可拖拽
  makeDraggable(element, data = {}) {
    return this.dragManager.makeDraggable(element, data);
  }

  // 移除拖拽功能
  removeDraggable(element) {
    return this.dragManager.removeDraggable(element);
  }

  // 添加放置区域
  addDropZone(element, options = {}) {
    return this.dragManager.addDropZone(element, options);
  }

  // 移除放置区域
  removeDropZone(element) {
    return this.dragManager.removeDropZone(element);
  }

  // 使元素可滑动
  makeSwipeable(element, actions = []) {
    return this.swipeManager.makeSwipeable(element, actions);
  }

  // 移除滑动功能
  removeSwipeable(element) {
    return this.swipeManager.removeSwipeable(element);
  }

  // 创建长按选择功能
  createLongPressSelection(elements, options = {}) {
    const selectedItems = new Set();
    let selectionMode = false;
    
    elements.forEach(element => {
      const gestureRecognizer = this.createGestureRecognizer(element);
      
      gestureRecognizer.on('longpress', () => {
        if (!selectionMode) {
          selectionMode = true;
          this.enterSelectionMode(elements, options);
        }
        
        this.toggleSelection(element, selectedItems, options);
      });
      
      gestureRecognizer.on('tap', () => {
        if (selectionMode) {
          this.toggleSelection(element, selectedItems, options);
        }
      });
    });
    
    return {
      getSelectedItems: () => Array.from(selectedItems),
      clearSelection: () => {
        selectedItems.clear();
        this.exitSelectionMode(elements, options);
        selectionMode = false;
      },
      isSelectionMode: () => selectionMode,
      selectAll: () => {
        elements.forEach(element => {
          if (!selectedItems.has(element)) {
            this.toggleSelection(element, selectedItems, options);
          }
        });
      }
    };
  }

  // 进入选择模式
  enterSelectionMode(elements, options = {}) {
    elements.forEach(element => {
      element.classList.add('selection-mode');
    });
    
    // 触发选择模式开始事件
    const event = new CustomEvent('selectionstart', {
      detail: { elements: elements },
      bubbles: true
    });
    document.dispatchEvent(event);
    
    if (options.onEnterSelection) {
      options.onEnterSelection(elements);
    }
  }

  // 退出选择模式
  exitSelectionMode(elements, options = {}) {
    elements.forEach(element => {
      element.classList.remove('selection-mode', 'selected');
    });
    
    // 触发选择模式结束事件
    const event = new CustomEvent('selectionend', {
      detail: { elements: elements },
      bubbles: true
    });
    document.dispatchEvent(event);
    
    if (options.onExitSelection) {
      options.onExitSelection(elements);
    }
  }

  // 切换选择状态
  toggleSelection(element, selectedItems, options = {}) {
    if (selectedItems.has(element)) {
      selectedItems.delete(element);
      element.classList.remove('selected');
    } else {
      selectedItems.add(element);
      element.classList.add('selected');
    }
    
    // 触发选择变化事件
    const event = new CustomEvent('selectionchange', {
      detail: {
        element: element,
        selected: selectedItems.has(element),
        selectedCount: selectedItems.size
      },
      bubbles: true
    });
    element.dispatchEvent(event);
    
    if (options.onSelectionChange) {
      options.onSelectionChange(element, selectedItems.has(element), selectedItems.size);
    }
  }

  // 创建触摸反馈
  createTouchFeedback(element, options = {}) {
    const gestureRecognizer = this.createGestureRecognizer(element);
    
    const feedbackOptions = {
      scale: 0.95,
      opacity: 0.7,
      duration: 150,
      ...options
    };
    
    gestureRecognizer.on('touchstart', () => {
      element.style.transition = `transform ${feedbackOptions.duration}ms ease, opacity ${feedbackOptions.duration}ms ease`;
      element.style.transform = `scale(${feedbackOptions.scale})`;
      element.style.opacity = feedbackOptions.opacity;
    });
    
    gestureRecognizer.on('touchend', () => {
      element.style.transform = '';
      element.style.opacity = '';
      
      setTimeout(() => {
        if (element.style) {
          element.style.transition = '';
        }
      }, feedbackOptions.duration);
    });
    
    gestureRecognizer.on('touchcancel', () => {
      element.style.transform = '';
      element.style.opacity = '';
      element.style.transition = '';
    });
  }

  // 创建磁性吸附效果
  createMagneticSnap(draggableElement, snapTargets, options = {}) {
    const snapOptions = {
      snapDistance: 50,
      snapStrength: 0.3,
      ...options
    };
    
    this.makeDraggable(draggableElement);
    
    // 监听拖拽移动事件
    draggableElement.addEventListener('dragmove', (e) => {
      const dragPoint = e.detail.position;
      let closestTarget = null;
      let minDistance = Infinity;
      
      snapTargets.forEach(target => {
        const rect = target.getBoundingClientRect();
        const targetCenter = {
          x: rect.left + rect.width / 2,
          y: rect.top + rect.height / 2
        };
        
        const distance = Math.sqrt(
          Math.pow(dragPoint.x - targetCenter.x, 2) + 
          Math.pow(dragPoint.y - targetCenter.y, 2)
        );
        
        if (distance < minDistance && distance < snapOptions.snapDistance) {
          minDistance = distance;
          closestTarget = target;
        }
      });
      
      // 应用磁性效果
      if (closestTarget) {
        const rect = closestTarget.getBoundingClientRect();
        const targetCenter = {
          x: rect.left + rect.width / 2,
          y: rect.top + rect.height / 2
        };
        
        const snapX = dragPoint.x + (targetCenter.x - dragPoint.x) * snapOptions.snapStrength;
        const snapY = dragPoint.y + (targetCenter.y - dragPoint.y) * snapOptions.snapStrength;
        
        // 更新拖拽位置
        draggableElement.style.transform = `translate(${snapX - dragPoint.x}px, ${snapY - dragPoint.y}px)`;
        
        // 高亮目标
        closestTarget.classList.add('snap-target-active');
      } else {
        // 移除所有高亮
        snapTargets.forEach(target => {
          target.classList.remove('snap-target-active');
        });
      }
    });
  }

  // 创建惯性滚动
  createInertialScroll(element, options = {}) {
    const scrollOptions = {
      friction: 0.95,
      minVelocity: 0.1,
      ...options
    };
    
    let velocity = { x: 0, y: 0 };
    let lastPoint = null;
    let lastTime = 0;
    let animationFrame = null;
    
    const gestureRecognizer = this.createGestureRecognizer(element);
    
    gestureRecognizer.on('touchstart', (e) => {
      // 停止当前动画
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
        animationFrame = null;
      }
      
      lastPoint = e.point;
      lastTime = Date.now();
      velocity = { x: 0, y: 0 };
    });
    
    gestureRecognizer.on('touchmove', (e) => {
      const currentTime = Date.now();
      const deltaTime = currentTime - lastTime;
      
      if (deltaTime > 0 && lastPoint) {
        velocity.x = (e.point.x - lastPoint.x) / deltaTime;
        velocity.y = (e.point.y - lastPoint.y) / deltaTime;
      }
      
      lastPoint = e.point;
      lastTime = currentTime;
      
      // 滚动元素
      element.scrollLeft -= e.delta.x;
      element.scrollTop -= e.delta.y;
    });
    
    gestureRecognizer.on('touchend', () => {
      // 开始惯性滚动
      if (Math.abs(velocity.x) > scrollOptions.minVelocity || 
          Math.abs(velocity.y) > scrollOptions.minVelocity) {
        this.startInertialAnimation(element, velocity, scrollOptions);
      }
    });
  }

  // 开始惯性动画
  startInertialAnimation(element, velocity, options) {
    const animate = () => {
      // 应用摩擦力
      velocity.x *= options.friction;
      velocity.y *= options.friction;
      
      // 更新滚动位置
      element.scrollLeft -= velocity.x * 16; // 假设60fps
      element.scrollTop -= velocity.y * 16;
      
      // 检查是否继续动画
      if (Math.abs(velocity.x) > options.minVelocity || 
          Math.abs(velocity.y) > options.minVelocity) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  }

  // 销毁所有交互
  destroy() {
    // 销毁所有手势识别器
    this.gestureRecognizers.forEach(recognizer => {
      recognizer.destroy();
    });
    this.gestureRecognizers.clear();
    
    // 销毁拖拽管理器
    this.dragManager.destroy();
    
    // 销毁滑动管理器
    this.swipeManager.destroy();
  }
}

// 创建全局实例
const advancedInteractionManager = new AdvancedInteractionManager();

module.exports = {
  AdvancedInteractionManager,
  advancedInteractionManager
};
