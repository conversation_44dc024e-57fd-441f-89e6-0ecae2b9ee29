/**
 * 滑动管理器模块
 * 提供滑动操作功能，支持左滑右滑显示操作按钮
 */

const { GestureRecognizer } = require('./gesture-recognizer.js');

class SwipeManager {
  constructor(options = {}) {
    this.options = {
      threshold: 50,           // 滑动距离阈值
      maxSwipeDistance: 150,   // 最大滑动距离
      animationDuration: 300,  // 动画持续时间
      ...options
    };
    
    this.swipeItems = new Set();
    this.currentSwipeItem = null;
  }

  // 使元素可滑动
  makeSwipeable(element, actions = []) {
    if (this.swipeItems.has(element)) {
      return; // 已经是可滑动的
    }

    const gestureRecognizer = new GestureRecognizer(element, {
      swipeThreshold: this.options.threshold
    });

    // 创建操作按钮容器
    this.createSwipeActions(element, actions);

    // 绑定滑动事件
    gestureRecognizer.on('touchstart', (e) => {
      this.onSwipeStart(element, e);
    });

    gestureRecognizer.on('touchmove', (e) => {
      this.onSwipeMove(element, e);
    });

    gestureRecognizer.on('touchend', (e) => {
      this.onSwipeEnd(element, e);
    });

    gestureRecognizer.on('swipe', (e) => {
      this.onSwipe(element, e);
    });

    element._swipeGestureRecognizer = gestureRecognizer;
    element._swipeActions = actions;
    this.swipeItems.add(element);
  }

  // 移除滑动功能
  removeSwipeable(element) {
    if (!this.swipeItems.has(element)) {
      return;
    }

    if (element._swipeGestureRecognizer) {
      element._swipeGestureRecognizer.destroy();
      delete element._swipeGestureRecognizer;
    }

    // 移除操作按钮
    const actionsContainer = element.querySelector('.swipe-actions');
    if (actionsContainer) {
      actionsContainer.remove();
    }

    delete element._swipeActions;
    this.swipeItems.delete(element);
  }

  onSwipeStart(element, e) {
    // 关闭其他已打开的滑动项
    if (this.currentSwipeItem && this.currentSwipeItem !== element) {
      this.closeSwipe(this.currentSwipeItem);
    }
  }

  onSwipeMove(element, e) {
    // 实时跟随手指移动
    if (Math.abs(e.delta.x) > Math.abs(e.delta.y)) {
      // 水平滑动
      let translateX = e.delta.x;
      
      // 限制滑动距离
      const maxDistance = this.getActionsWidth(element);
      if (translateX < -maxDistance) {
        translateX = -maxDistance;
      } else if (translateX > maxDistance) {
        translateX = maxDistance;
      }
      
      element.style.transform = `translateX(${translateX}px)`;
      element.style.transition = '';
    }
  }

  onSwipeEnd(element, e) {
    const currentTranslate = this.getCurrentTranslate(element);
    const actionsWidth = this.getActionsWidth(element);
    const threshold = actionsWidth * 0.3; // 30% 阈值
    
    if (Math.abs(currentTranslate) > threshold) {
      // 显示操作按钮
      const targetTranslate = currentTranslate > 0 ? actionsWidth : -actionsWidth;
      this.animateToPosition(element, targetTranslate);
      this.currentSwipeItem = element;
    } else {
      // 回到原位置
      this.closeSwipe(element);
    }
  }

  onSwipe(element, e) {
    const actionsWidth = this.getActionsWidth(element);
    
    if (e.direction === 'left') {
      // 左滑显示右侧操作
      this.animateToPosition(element, -actionsWidth);
      this.currentSwipeItem = element;
    } else if (e.direction === 'right') {
      // 右滑显示左侧操作或关闭
      const currentTranslate = this.getCurrentTranslate(element);
      if (currentTranslate < 0) {
        // 当前是左滑状态，右滑关闭
        this.closeSwipe(element);
      } else {
        // 右滑显示左侧操作
        this.animateToPosition(element, actionsWidth);
        this.currentSwipeItem = element;
      }
    }
  }

  createSwipeActions(element, actions) {
    // 检查是否已存在操作容器
    let actionsContainer = element.querySelector('.swipe-actions');
    if (!actionsContainer) {
      actionsContainer = document.createElement('div');
      actionsContainer.className = 'swipe-actions';
      actionsContainer.style.cssText = `
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        display: flex;
        align-items: center;
        z-index: 1;
      `;
      element.appendChild(actionsContainer);
      
      // 确保父元素有相对定位
      if (getComputedStyle(element).position === 'static') {
        element.style.position = 'relative';
      }
    }
    
    // 清空现有动作
    actionsContainer.innerHTML = '';
    
    // 创建动作按钮
    actions.forEach(action => {
      const button = document.createElement('button');
      button.className = `swipe-action ${action.type || ''}`;
      button.innerHTML = action.icon ? `<i class="${action.icon}"></i>` : action.text;
      button.style.cssText = `
        height: 100%;
        padding: 0 15px;
        border: none;
        color: white;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 60px;
        background-color: ${action.color || '#ff4757'};
      `;
      
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        action.handler && action.handler(element);
        this.closeSwipe(element);
      });
      
      actionsContainer.appendChild(button);
    });
  }

  getCurrentTranslate(element) {
    const transform = element.style.transform;
    const match = transform.match(/translateX\((-?\d+(?:\.\d+)?)px\)/);
    return match ? parseFloat(match[1]) : 0;
  }

  getActionsWidth(element) {
    const actionsContainer = element.querySelector('.swipe-actions');
    return actionsContainer ? actionsContainer.offsetWidth : 0;
  }

  animateToPosition(element, targetTranslate) {
    element.style.transition = `transform ${this.options.animationDuration}ms ease`;
    element.style.transform = `translateX(${targetTranslate}px)`;
    
    setTimeout(() => {
      if (element.style) {
        element.style.transition = '';
      }
    }, this.options.animationDuration);
  }

  closeSwipe(element) {
    this.animateToPosition(element, 0);
    if (this.currentSwipeItem === element) {
      this.currentSwipeItem = null;
    }
  }

  closeAllSwipes() {
    this.swipeItems.forEach(element => {
      this.closeSwipe(element);
    });
  }

  // 更新操作按钮
  updateActions(element, actions) {
    if (!this.swipeItems.has(element)) {
      return;
    }
    
    element._swipeActions = actions;
    this.createSwipeActions(element, actions);
  }

  // 获取当前滑动状态
  getSwipeState(element) {
    const currentTranslate = this.getCurrentTranslate(element);
    const actionsWidth = this.getActionsWidth(element);
    
    if (currentTranslate === 0) {
      return 'closed';
    } else if (currentTranslate > 0) {
      return 'right';
    } else {
      return 'left';
    }
  }

  // 程序化控制滑动
  openSwipe(element, direction = 'left') {
    if (!this.swipeItems.has(element)) {
      return;
    }
    
    const actionsWidth = this.getActionsWidth(element);
    const targetTranslate = direction === 'left' ? -actionsWidth : actionsWidth;
    
    this.animateToPosition(element, targetTranslate);
    this.currentSwipeItem = element;
  }

  // 事件监听
  on(eventName, callback) {
    document.addEventListener(`swipe${eventName}`, callback);
  }

  off(eventName, callback) {
    document.removeEventListener(`swipe${eventName}`, callback);
  }

  // 触发自定义事件
  triggerEvent(eventName, element, data = {}) {
    const event = new CustomEvent(`swipe${eventName}`, {
      detail: {
        element: element,
        ...data
      },
      bubbles: true,
      cancelable: true
    });
    
    element.dispatchEvent(event);
  }

  // 销毁
  destroy() {
    this.swipeItems.forEach(element => {
      this.removeSwipeable(element);
    });
    this.swipeItems.clear();
    this.currentSwipeItem = null;
  }
}

// 创建全局实例
const swipeManager = new SwipeManager();

// 便捷方法
const SwipeUtils = {
  // 快速创建滑动项
  createSwipeItem(element, actions) {
    swipeManager.makeSwipeable(element, actions);
  },
  
  // 创建删除滑动
  createDeleteSwipe(element, onDelete) {
    const actions = [{
      text: '删除',
      color: '#ff4757',
      type: 'delete',
      handler: onDelete
    }];
    swipeManager.makeSwipeable(element, actions);
  },
  
  // 创建编辑滑动
  createEditSwipe(element, onEdit, onDelete) {
    const actions = [
      {
        text: '编辑',
        color: '#3742fa',
        type: 'edit',
        handler: onEdit
      },
      {
        text: '删除',
        color: '#ff4757',
        type: 'delete',
        handler: onDelete
      }
    ];
    swipeManager.makeSwipeable(element, actions);
  },
  
  // 关闭所有滑动
  closeAll() {
    swipeManager.closeAllSwipes();
  }
};

module.exports = {
  SwipeManager,
  swipeManager,
  SwipeUtils
};
