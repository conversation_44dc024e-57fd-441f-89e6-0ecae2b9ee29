/**
 * 交互功能页面混入对象
 * 为小程序页面提供便捷的交互功能接口
 */

const { advancedInteractionManager } = require('./advanced-interaction-manager.js');

// 页面混入对象
const AdvancedInteractionMixin = {
  data: {
    interactionManager: null
  },

  onLoad() {
    this.data.interactionManager = advancedInteractionManager;
  },

  onUnload() {
    // 清理页面相关的交互
    this.cleanupInteractions();
  },

  // 便捷方法
  createGestureRecognizer(selector, options) {
    const element = this.getElement(selector);
    if (element) {
      return advancedInteractionManager.createGestureRecognizer(element, options);
    }
    return null;
  },

  makeDraggable(selector, data) {
    const element = this.getElement(selector);
    if (element) {
      advancedInteractionManager.makeDraggable(element, data);
    }
  },

  makeSwipeable(selector, actions) {
    const element = this.getElement(selector);
    if (element) {
      advancedInteractionManager.makeSwipeable(element, actions);
    }
  },

  addDropZone(selector, options) {
    const element = this.getElement(selector);
    if (element) {
      advancedInteractionManager.addDropZone(element, options);
    }
  },

  createTouchFeedback(selector, options) {
    const element = this.getElement(selector);
    if (element) {
      advancedInteractionManager.createTouchFeedback(element, options);
    }
  },

  createLongPressSelection(selector, options) {
    const elements = this.getElements(selector);
    if (elements.length > 0) {
      return advancedInteractionManager.createLongPressSelection(elements, options);
    }
    return null;
  },

  createInertialScroll(selector, options) {
    const element = this.getElement(selector);
    if (element) {
      advancedInteractionManager.createInertialScroll(element, options);
    }
  },

  // 工具方法
  getElement(selector) {
    if (typeof selector === 'string') {
      return document.querySelector(selector);
    }
    return selector; // 假设已经是元素对象
  },

  getElements(selector) {
    if (typeof selector === 'string') {
      return Array.from(document.querySelectorAll(selector));
    }
    return Array.isArray(selector) ? selector : [selector];
  },

  // 清理交互
  cleanupInteractions() {
    // 这里可以添加页面特定的清理逻辑
    // 由于是全局管理器，通常不需要特殊清理
  }
};

// 组件混入对象
const AdvancedInteractionComponentMixin = {
  data: {
    interactionManager: null
  },

  attached() {
    this.data.interactionManager = advancedInteractionManager;
  },

  detached() {
    this.cleanupInteractions();
  },

  methods: {
    createGestureRecognizer(selector, options) {
      const element = this.getElement(selector);
      if (element) {
        return advancedInteractionManager.createGestureRecognizer(element, options);
      }
      return null;
    },

    makeDraggable(selector, data) {
      const element = this.getElement(selector);
      if (element) {
        advancedInteractionManager.makeDraggable(element, data);
      }
    },

    makeSwipeable(selector, actions) {
      const element = this.getElement(selector);
      if (element) {
        advancedInteractionManager.makeSwipeable(element, actions);
      }
    },

    addDropZone(selector, options) {
      const element = this.getElement(selector);
      if (element) {
        advancedInteractionManager.addDropZone(element, options);
      }
    },

    createTouchFeedback(selector, options) {
      const element = this.getElement(selector);
      if (element) {
        advancedInteractionManager.createTouchFeedback(element, options);
      }
    },

    getElement(selector) {
      if (typeof selector === 'string') {
        return this.selectComponent(selector) || document.querySelector(selector);
      }
      return selector;
    },

    getElements(selector) {
      if (typeof selector === 'string') {
        const components = this.selectAllComponents(selector);
        if (components.length > 0) {
          return components;
        }
        return Array.from(document.querySelectorAll(selector));
      }
      return Array.isArray(selector) ? selector : [selector];
    },

    cleanupInteractions() {
      // 组件特定的清理逻辑
    }
  }
};

// 快速设置方法
const QuickSetup = {
  // 快速设置列表滑动删除
  setupSwipeDelete(listSelector, itemSelector, onDelete) {
    const listElement = document.querySelector(listSelector);
    if (!listElement) return;

    const items = listElement.querySelectorAll(itemSelector);
    items.forEach(item => {
      const actions = [{
        text: '删除',
        color: '#ff4757',
        type: 'delete',
        handler: (element) => {
          if (onDelete) {
            onDelete(element);
          }
        }
      }];
      
      advancedInteractionManager.makeSwipeable(item, actions);
    });
  },

  // 快速设置拖拽排序
  setupDragSort(containerSelector, itemSelector, onSort) {
    const container = document.querySelector(containerSelector);
    if (!container) return;

    const items = container.querySelectorAll(itemSelector);
    
    // 设置容器为放置区域
    advancedInteractionManager.addDropZone(container, {
      accept: '*',
      onDrop: (dragData, point) => {
        if (onSort) {
          onSort(dragData.element, point);
        }
      }
    });

    // 设置所有项目为可拖拽
    items.forEach((item, index) => {
      advancedInteractionManager.makeDraggable(item, {
        type: 'sortable',
        index: index,
        originalParent: container
      });
    });
  },

  // 快速设置长按多选
  setupLongPressMultiSelect(containerSelector, itemSelector, options = {}) {
    const container = document.querySelector(containerSelector);
    if (!container) return;

    const items = Array.from(container.querySelectorAll(itemSelector));
    
    return advancedInteractionManager.createLongPressSelection(items, {
      onEnterSelection: (elements) => {
        container.classList.add('multi-select-mode');
        if (options.onEnterSelection) {
          options.onEnterSelection(elements);
        }
      },
      onExitSelection: (elements) => {
        container.classList.remove('multi-select-mode');
        if (options.onExitSelection) {
          options.onExitSelection(elements);
        }
      },
      onSelectionChange: options.onSelectionChange
    });
  },

  // 快速设置触摸反馈
  setupTouchFeedback(selector, options = {}) {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      advancedInteractionManager.createTouchFeedback(element, options);
    });
  }
};

module.exports = {
  AdvancedInteractionMixin,
  AdvancedInteractionComponentMixin,
  QuickSetup
};
