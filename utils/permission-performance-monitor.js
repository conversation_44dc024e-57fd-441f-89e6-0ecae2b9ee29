/**
 * 权限性能监控器
 * Permission Performance Monitor
 * 
 * 监控权限检查性能，验证缓存优化效果
 */

class PermissionPerformanceMonitor {
  constructor() {
    this.metrics = {
      // 权限检查次数
      totalChecks: 0,
      cachedChecks: 0,
      uncachedChecks: 0,
      
      // 响应时间统计
      responseTimes: [],
      cacheHitTimes: [],
      cacheMissTimes: [],
      
      // 错误统计
      errors: 0,
      
      // 开始时间
      startTime: Date.now()
    };
    
    this.isMonitoring = false;
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    this.isMonitoring = true;
    this.metrics.startTime = Date.now();
    console.log('🚀 权限性能监控已启动');
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.isMonitoring = false;
    console.log('⏹️ 权限性能监控已停止');
  }

  /**
   * 记录权限检查性能
   * @param {boolean} fromCache 是否来自缓存
   * @param {number} responseTime 响应时间（毫秒）
   * @param {boolean} hasError 是否有错误
   */
  recordPermissionCheck(fromCache, responseTime, hasError = false) {
    if (!this.isMonitoring) return;

    this.metrics.totalChecks++;
    
    if (fromCache) {
      this.metrics.cachedChecks++;
      this.metrics.cacheHitTimes.push(responseTime);
    } else {
      this.metrics.uncachedChecks++;
      this.metrics.cacheMissTimes.push(responseTime);
    }
    
    this.metrics.responseTimes.push(responseTime);
    
    if (hasError) {
      this.metrics.errors++;
    }
  }

  /**
   * 包装权限检查函数以进行性能监控
   * @param {Function} permissionCheckFn 权限检查函数
   * @param {boolean} fromCache 是否来自缓存
   * @returns {Function} 包装后的函数
   */
  wrapPermissionCheck(permissionCheckFn, fromCache = false) {
    return async (...args) => {
      const startTime = Date.now();
      let hasError = false;
      
      try {
        const result = await permissionCheckFn(...args);
        return result;
      } catch (error) {
        hasError = true;
        throw error;
      } finally {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        this.recordPermissionCheck(fromCache, responseTime, hasError);
      }
    };
  }

  /**
   * 获取性能统计报告
   */
  getPerformanceReport() {
    const now = Date.now();
    const duration = now - this.metrics.startTime;
    
    // 计算平均响应时间
    const avgResponseTime = this.calculateAverage(this.metrics.responseTimes);
    const avgCacheHitTime = this.calculateAverage(this.metrics.cacheHitTimes);
    const avgCacheMissTime = this.calculateAverage(this.metrics.cacheMissTimes);
    
    // 计算缓存命中率
    const cacheHitRate = this.metrics.totalChecks > 0 
      ? (this.metrics.cachedChecks / this.metrics.totalChecks * 100).toFixed(2)
      : 0;
    
    // 计算性能提升
    const performanceImprovement = avgCacheMissTime > 0 && avgCacheHitTime > 0
      ? ((avgCacheMissTime - avgCacheHitTime) / avgCacheMissTime * 100).toFixed(2)
      : 0;
    
    // 计算错误率
    const errorRate = this.metrics.totalChecks > 0
      ? (this.metrics.errors / this.metrics.totalChecks * 100).toFixed(2)
      : 0;

    return {
      // 基础统计
      duration: this.formatDuration(duration),
      totalChecks: this.metrics.totalChecks,
      cachedChecks: this.metrics.cachedChecks,
      uncachedChecks: this.metrics.uncachedChecks,
      errors: this.metrics.errors,
      
      // 性能指标
      cacheHitRate: `${cacheHitRate}%`,
      errorRate: `${errorRate}%`,
      
      // 响应时间统计
      avgResponseTime: `${avgResponseTime.toFixed(2)}ms`,
      avgCacheHitTime: `${avgCacheHitTime.toFixed(2)}ms`,
      avgCacheMissTime: `${avgCacheMissTime.toFixed(2)}ms`,
      
      // 性能提升
      performanceImprovement: `${performanceImprovement}%`,
      
      // 详细统计
      responseTimeStats: this.calculateStats(this.metrics.responseTimes),
      cacheHitTimeStats: this.calculateStats(this.metrics.cacheHitTimes),
      cacheMissTimeStats: this.calculateStats(this.metrics.cacheMissTimes),
      
      // 每秒请求数
      requestsPerSecond: duration > 0 ? (this.metrics.totalChecks / (duration / 1000)).toFixed(2) : 0
    };
  }

  /**
   * 计算数组平均值
   */
  calculateAverage(arr) {
    if (arr.length === 0) return 0;
    return arr.reduce((sum, val) => sum + val, 0) / arr.length;
  }

  /**
   * 计算统计信息
   */
  calculateStats(arr) {
    if (arr.length === 0) {
      return {
        min: 0,
        max: 0,
        avg: 0,
        median: 0,
        p95: 0,
        p99: 0
      };
    }

    const sorted = [...arr].sort((a, b) => a - b);
    const len = sorted.length;
    
    return {
      min: sorted[0],
      max: sorted[len - 1],
      avg: this.calculateAverage(arr),
      median: len % 2 === 0 
        ? (sorted[len / 2 - 1] + sorted[len / 2]) / 2 
        : sorted[Math.floor(len / 2)],
      p95: sorted[Math.floor(len * 0.95)],
      p99: sorted[Math.floor(len * 0.99)]
    };
  }

  /**
   * 格式化持续时间
   */
  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * 生成性能报告（控制台输出）
   */
  printPerformanceReport() {
    const report = this.getPerformanceReport();
    
    console.log('\n📊 权限性能监控报告');
    console.log('=' .repeat(50));
    console.log(`监控时长: ${report.duration}`);
    console.log(`总检查次数: ${report.totalChecks}`);
    console.log(`缓存命中次数: ${report.cachedChecks}`);
    console.log(`缓存未命中次数: ${report.uncachedChecks}`);
    console.log(`错误次数: ${report.errors}`);
    console.log('');
    console.log('🎯 性能指标');
    console.log('-'.repeat(30));
    console.log(`缓存命中率: ${report.cacheHitRate}`);
    console.log(`错误率: ${report.errorRate}`);
    console.log(`平均响应时间: ${report.avgResponseTime}`);
    console.log(`缓存命中平均时间: ${report.avgCacheHitTime}`);
    console.log(`缓存未命中平均时间: ${report.avgCacheMissTime}`);
    console.log(`性能提升: ${report.performanceImprovement}`);
    console.log(`每秒请求数: ${report.requestsPerSecond}`);
    console.log('');
    console.log('📈 响应时间分布');
    console.log('-'.repeat(30));
    console.log(`最小值: ${report.responseTimeStats.min.toFixed(2)}ms`);
    console.log(`最大值: ${report.responseTimeStats.max.toFixed(2)}ms`);
    console.log(`中位数: ${report.responseTimeStats.median.toFixed(2)}ms`);
    console.log(`95分位: ${report.responseTimeStats.p95.toFixed(2)}ms`);
    console.log(`99分位: ${report.responseTimeStats.p99.toFixed(2)}ms`);
    console.log('=' .repeat(50));
  }

  /**
   * 重置监控数据
   */
  reset() {
    this.metrics = {
      totalChecks: 0,
      cachedChecks: 0,
      uncachedChecks: 0,
      responseTimes: [],
      cacheHitTimes: [],
      cacheMissTimes: [],
      errors: 0,
      startTime: Date.now()
    };
  }

  /**
   * 导出监控数据
   */
  exportData() {
    return {
      metrics: { ...this.metrics },
      report: this.getPerformanceReport(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 设置性能阈值告警
   */
  setPerformanceThresholds(thresholds) {
    this.thresholds = {
      maxAvgResponseTime: 100, // 100ms
      minCacheHitRate: 80, // 80%
      maxErrorRate: 1, // 1%
      ...thresholds
    };
  }

  /**
   * 检查性能阈值
   */
  checkPerformanceThresholds() {
    if (!this.thresholds) return { passed: true, warnings: [] };
    
    const report = this.getPerformanceReport();
    const warnings = [];
    
    // 检查平均响应时间
    const avgResponseTime = parseFloat(report.avgResponseTime);
    if (avgResponseTime > this.thresholds.maxAvgResponseTime) {
      warnings.push(`平均响应时间过高: ${report.avgResponseTime} > ${this.thresholds.maxAvgResponseTime}ms`);
    }
    
    // 检查缓存命中率
    const cacheHitRate = parseFloat(report.cacheHitRate);
    if (cacheHitRate < this.thresholds.minCacheHitRate) {
      warnings.push(`缓存命中率过低: ${report.cacheHitRate} < ${this.thresholds.minCacheHitRate}%`);
    }
    
    // 检查错误率
    const errorRate = parseFloat(report.errorRate);
    if (errorRate > this.thresholds.maxErrorRate) {
      warnings.push(`错误率过高: ${report.errorRate} > ${this.thresholds.maxErrorRate}%`);
    }
    
    return {
      passed: warnings.length === 0,
      warnings
    };
  }
}

// 创建全局实例
const performanceMonitor = new PermissionPerformanceMonitor();

module.exports = performanceMonitor;
