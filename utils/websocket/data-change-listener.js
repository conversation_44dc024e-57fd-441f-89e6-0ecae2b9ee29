/**
 * 数据变更监听器
 * Data Change Listener
 */

const { realtimeSyncService } = require('./realtime-sync-service.js');

class DataChangeListener {
  constructor() {
    this.listeners = new Map(); // 监听器映射
    this.dataWatchers = new Map(); // 数据观察器
    this.changeQueue = []; // 变更队列
    this.isProcessing = false;
    this.batchSize = 10; // 批处理大小
    this.batchDelay = 100; // 批处理延迟(ms)
  }

  /**
   * 监听数据变更
   */
  watch(dataType, selector, callback, options = {}) {
    const watcherId = this.generateWatcherId(dataType, selector);
    
    const watcher = {
      id: watcherId,
      dataType: dataType,
      selector: selector,
      callback: callback,
      options: {
        immediate: false, // 是否立即执行
        deep: true, // 是否深度监听
        debounce: 0, // 防抖延迟
        ...options
      },
      lastValue: null,
      debounceTimer: null
    };

    this.dataWatchers.set(watcherId, watcher);
    
    // 订阅实时同步服务
    const unsubscribe = realtimeSyncService.subscribe(dataType, (event) => {
      this.handleDataChange(watcherId, event);
    });
    
    watcher.unsubscribe = unsubscribe;
    
    // 如果需要立即执行，获取当前数据
    if (watcher.options.immediate) {
      this.getCurrentData(dataType, selector).then(data => {
        this.executeCallback(watcher, data, null, 'initial');
      }).catch(error => {
        console.error('获取初始数据失败:', error);
      });
    }

    console.log(`数据监听器已创建: ${watcherId}`);
    return watcherId;
  }

  /**
   * 取消监听
   */
  unwatch(watcherId) {
    const watcher = this.dataWatchers.get(watcherId);
    if (!watcher) {
      return;
    }

    // 取消订阅
    if (watcher.unsubscribe) {
      watcher.unsubscribe();
    }

    // 清除防抖定时器
    if (watcher.debounceTimer) {
      clearTimeout(watcher.debounceTimer);
    }

    this.dataWatchers.delete(watcherId);
    console.log(`数据监听器已移除: ${watcherId}`);
  }

  /**
   * 处理数据变更
   */
  handleDataChange(watcherId, event) {
    const watcher = this.dataWatchers.get(watcherId);
    if (!watcher) {
      return;
    }

    // 根据事件类型处理
    switch (event.type) {
      case 'data_updated':
      case 'realtime_update':
        this.handleDataUpdate(watcher, event);
        break;
      case 'data_change':
        this.handleSpecificChange(watcher, event);
        break;
      case 'sync_error':
        this.handleSyncError(watcher, event);
        break;
      default:
        console.log('未处理的事件类型:', event.type);
    }
  }

  /**
   * 处理数据更新
   */
  handleDataUpdate(watcher, event) {
    const { data, previousData } = event;
    
    // 根据选择器过滤数据
    const currentValue = this.selectData(data, watcher.selector);
    const previousValue = this.selectData(previousData, watcher.selector);
    
    // 检查数据是否真的发生了变化
    if (this.hasValueChanged(previousValue, currentValue, watcher.options.deep)) {
      this.scheduleCallback(watcher, currentValue, previousValue, 'update');
    }
  }

  /**
   * 处理特定变更
   */
  handleSpecificChange(watcher, event) {
    const { changeType, data } = event;
    
    // 根据选择器检查是否匹配
    if (this.matchesSelector(data, watcher.selector)) {
      const currentValue = this.selectData(data, watcher.selector);
      this.scheduleCallback(watcher, currentValue, watcher.lastValue, changeType);
    }
  }

  /**
   * 处理同步错误
   */
  handleSyncError(watcher, event) {
    const { error } = event;
    
    // 执行错误回调
    if (watcher.callback.onError) {
      try {
        watcher.callback.onError(error);
      } catch (callbackError) {
        console.error('错误回调执行失败:', callbackError);
      }
    }
  }

  /**
   * 安排回调执行
   */
  scheduleCallback(watcher, currentValue, previousValue, changeType) {
    // 清除之前的防抖定时器
    if (watcher.debounceTimer) {
      clearTimeout(watcher.debounceTimer);
    }

    const executeCallback = () => {
      this.executeCallback(watcher, currentValue, previousValue, changeType);
    };

    // 如果设置了防抖，延迟执行
    if (watcher.options.debounce > 0) {
      watcher.debounceTimer = setTimeout(executeCallback, watcher.options.debounce);
    } else {
      executeCallback();
    }
  }

  /**
   * 执行回调
   */
  executeCallback(watcher, currentValue, previousValue, changeType) {
    try {
      // 更新最后的值
      watcher.lastValue = this.cloneValue(currentValue);
      
      // 构建变更信息
      const changeInfo = {
        type: changeType,
        currentValue: currentValue,
        previousValue: previousValue,
        timestamp: Date.now(),
        dataType: watcher.dataType,
        selector: watcher.selector
      };

      // 执行回调
      if (typeof watcher.callback === 'function') {
        watcher.callback(changeInfo);
      } else if (watcher.callback.onChange) {
        watcher.callback.onChange(changeInfo);
      }

      console.log(`数据变更回调已执行: ${watcher.id}`);

    } catch (error) {
      console.error(`数据变更回调执行失败 (${watcher.id}):`, error);
    }
  }

  /**
   * 根据选择器选择数据
   */
  selectData(data, selector) {
    if (!data || !selector) {
      return data;
    }

    // 如果选择器是函数，直接调用
    if (typeof selector === 'function') {
      return selector(data);
    }

    // 如果选择器是字符串路径
    if (typeof selector === 'string') {
      return this.getValueByPath(data, selector);
    }

    // 如果选择器是对象，进行复杂匹配
    if (typeof selector === 'object') {
      return this.selectByObject(data, selector);
    }

    return data;
  }

  /**
   * 根据路径获取值
   */
  getValueByPath(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 根据对象选择器选择数据
   */
  selectByObject(data, selector) {
    if (Array.isArray(data)) {
      return data.filter(item => this.matchesSelector(item, selector));
    } else {
      return this.matchesSelector(data, selector) ? data : null;
    }
  }

  /**
   * 检查是否匹配选择器
   */
  matchesSelector(item, selector) {
    if (!selector || typeof selector !== 'object') {
      return true;
    }

    for (const [key, value] of Object.entries(selector)) {
      if (item[key] !== value) {
        return false;
      }
    }

    return true;
  }

  /**
   * 检查值是否发生变化
   */
  hasValueChanged(oldValue, newValue, deep = true) {
    if (oldValue === newValue) {
      return false;
    }

    if (oldValue === null || newValue === null) {
      return oldValue !== newValue;
    }

    if (typeof oldValue !== typeof newValue) {
      return true;
    }

    if (!deep) {
      return oldValue !== newValue;
    }

    // 深度比较
    if (typeof oldValue === 'object') {
      return JSON.stringify(oldValue) !== JSON.stringify(newValue);
    }

    return oldValue !== newValue;
  }

  /**
   * 克隆值
   */
  cloneValue(value) {
    if (value === null || typeof value !== 'object') {
      return value;
    }

    try {
      return JSON.parse(JSON.stringify(value));
    } catch (error) {
      console.warn('克隆值失败:', error);
      return value;
    }
  }

  /**
   * 获取当前数据
   */
  async getCurrentData(dataType, selector) {
    // 这里可以从缓存或API获取当前数据
    // 暂时返回空，实际实现时需要根据数据类型获取
    return null;
  }

  /**
   * 生成监听器ID
   */
  generateWatcherId(dataType, selector) {
    const selectorStr = typeof selector === 'function' 
      ? selector.toString() 
      : JSON.stringify(selector);
    
    return `${dataType}_${btoa(selectorStr).replace(/[^a-zA-Z0-9]/g, '').substring(0, 8)}_${Date.now()}`;
  }

  /**
   * 批量监听
   */
  watchBatch(watchers) {
    const watcherIds = [];
    
    watchers.forEach(({ dataType, selector, callback, options }) => {
      const watcherId = this.watch(dataType, selector, callback, options);
      watcherIds.push(watcherId);
    });

    return watcherIds;
  }

  /**
   * 批量取消监听
   */
  unwatchBatch(watcherIds) {
    watcherIds.forEach(watcherId => {
      this.unwatch(watcherId);
    });
  }

  /**
   * 获取监听器状态
   */
  getWatcherStatus() {
    const watchers = {};
    
    this.dataWatchers.forEach((watcher, id) => {
      watchers[id] = {
        dataType: watcher.dataType,
        selector: watcher.selector,
        options: watcher.options,
        hasLastValue: watcher.lastValue !== null
      };
    });

    return {
      totalWatchers: this.dataWatchers.size,
      watchers: watchers,
      changeQueueSize: this.changeQueue.length,
      isProcessing: this.isProcessing
    };
  }

  /**
   * 清理所有监听器
   */
  clear() {
    this.dataWatchers.forEach((watcher, watcherId) => {
      this.unwatch(watcherId);
    });
    
    this.changeQueue = [];
    this.isProcessing = false;
    
    console.log('所有数据监听器已清理');
  }
}

// 创建全局实例
const dataChangeListener = new DataChangeListener();

// 便捷方法
const DataWatcher = {
  // 监听数据变更
  watch: (dataType, selector, callback, options) => 
    dataChangeListener.watch(dataType, selector, callback, options),
  
  // 取消监听
  unwatch: (watcherId) => 
    dataChangeListener.unwatch(watcherId),
  
  // 批量监听
  watchBatch: (watchers) => 
    dataChangeListener.watchBatch(watchers),
  
  // 批量取消监听
  unwatchBatch: (watcherIds) => 
    dataChangeListener.unwatchBatch(watcherIds),
  
  // 获取状态
  getStatus: () => 
    dataChangeListener.getWatcherStatus(),
  
  // 清理
  clear: () => 
    dataChangeListener.clear()
};

module.exports = {
  DataChangeListener,
  dataChangeListener,
  DataWatcher
};
