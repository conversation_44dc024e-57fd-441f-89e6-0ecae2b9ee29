/**
 * WebSocket连接管理器
 * WebSocket Connection Manager
 */

class WebSocketManager {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // 初始重连延迟1秒
    this.maxReconnectDelay = 30000; // 最大重连延迟30秒
    this.heartbeatInterval = 30000; // 心跳间隔30秒
    this.heartbeatTimer = null;
    this.reconnectTimer = null;
    
    this.eventListeners = new Map();
    this.messageQueue = []; // 离线消息队列
    this.config = {
      url: '',
      protocols: [],
      enableHeartbeat: true,
      enableReconnect: true,
      enableMessageQueue: true
    };
  }

  /**
   * 初始化WebSocket连接
   */
  async initialize(config = {}) {
    this.config = { ...this.config, ...config };
    
    if (!this.config.url) {
      throw new Error('WebSocket URL is required');
    }

    console.log('初始化WebSocket连接:', this.config.url);
    return this.connect();
  }

  /**
   * 建立WebSocket连接
   */
  async connect() {
    if (this.isConnected || this.isConnecting) {
      console.log('WebSocket已连接或正在连接中');
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      try {
        this.isConnecting = true;
        
        // 创建WebSocket连接
        this.socket = wx.connectSocket({
          url: this.config.url,
          protocols: this.config.protocols,
          success: () => {
            console.log('WebSocket连接创建成功');
          },
          fail: (error) => {
            console.error('WebSocket连接创建失败:', error);
            this.isConnecting = false;
            reject(error);
          }
        });

        // 监听连接打开
        this.socket.onOpen(() => {
          console.log('WebSocket连接已打开');
          this.isConnected = true;
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          
          // 启动心跳
          if (this.config.enableHeartbeat) {
            this.startHeartbeat();
          }
          
          // 发送离线消息队列
          this.flushMessageQueue();
          
          // 触发连接成功事件
          this.emit('connected');
          resolve();
        });

        // 监听消息接收
        this.socket.onMessage((event) => {
          this.handleMessage(event.data);
        });

        // 监听连接关闭
        this.socket.onClose((event) => {
          console.log('WebSocket连接已关闭:', event);
          this.handleDisconnect(event);
        });

        // 监听连接错误
        this.socket.onError((error) => {
          console.error('WebSocket连接错误:', error);
          this.handleError(error);
        });

      } catch (error) {
        console.error('创建WebSocket连接异常:', error);
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * 处理消息接收
   */
  handleMessage(data) {
    try {
      let message;
      
      // 尝试解析JSON消息
      if (typeof data === 'string') {
        try {
          message = JSON.parse(data);
        } catch (e) {
          message = { type: 'text', data: data };
        }
      } else {
        message = data;
      }

      console.log('收到WebSocket消息:', message);

      // 处理心跳响应
      if (message.type === 'pong') {
        console.log('收到心跳响应');
        return;
      }

      // 触发消息事件
      this.emit('message', message);
      
      // 根据消息类型触发特定事件
      if (message.type) {
        this.emit(message.type, message);
      }

    } catch (error) {
      console.error('处理WebSocket消息失败:', error);
      this.emit('error', { type: 'message_parse_error', error });
    }
  }

  /**
   * 处理连接断开
   */
  handleDisconnect(event) {
    this.isConnected = false;
    this.isConnecting = false;
    
    // 停止心跳
    this.stopHeartbeat();
    
    // 触发断开连接事件
    this.emit('disconnected', event);
    
    // 自动重连
    if (this.config.enableReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect();
    } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限');
      this.emit('reconnect_failed');
    }
  }

  /**
   * 处理连接错误
   */
  handleError(error) {
    console.error('WebSocket错误:', error);
    this.emit('error', error);
    
    // 如果连接中出错，标记连接失败
    if (this.isConnecting) {
      this.isConnecting = false;
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    // 计算重连延迟（指数退避）
    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.reconnectAttempts),
      this.maxReconnectDelay
    );

    console.log(`${delay}ms后尝试重连 (第${this.reconnectAttempts + 1}次)`);

    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      this.connect().catch(error => {
        console.error('重连失败:', error);
      });
    }, delay);
  }

  /**
   * 发送消息
   */
  send(message) {
    if (!this.isConnected) {
      if (this.config.enableMessageQueue) {
        console.log('连接未建立，消息加入队列');
        this.messageQueue.push(message);
        return Promise.resolve();
      } else {
        return Promise.reject(new Error('WebSocket未连接'));
      }
    }

    return new Promise((resolve, reject) => {
      try {
        const data = typeof message === 'string' ? message : JSON.stringify(message);
        
        this.socket.send({
          data: data,
          success: () => {
            console.log('消息发送成功:', message);
            resolve();
          },
          fail: (error) => {
            console.error('消息发送失败:', error);
            reject(error);
          }
        });
      } catch (error) {
        console.error('发送消息异常:', error);
        reject(error);
      }
    });
  }

  /**
   * 发送离线消息队列
   */
  flushMessageQueue() {
    if (this.messageQueue.length === 0) {
      return;
    }

    console.log(`发送${this.messageQueue.length}条离线消息`);
    
    const messages = [...this.messageQueue];
    this.messageQueue = [];
    
    messages.forEach(message => {
      this.send(message).catch(error => {
        console.error('发送离线消息失败:', error);
        // 重新加入队列
        this.messageQueue.push(message);
      });
    });
  }

  /**
   * 启动心跳
   */
  startHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping', timestamp: Date.now() }).catch(error => {
          console.error('发送心跳失败:', error);
        });
      }
    }, this.heartbeatInterval);

    console.log('心跳已启动');
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
      console.log('心跳已停止');
    }
  }

  /**
   * 关闭连接
   */
  disconnect() {
    console.log('主动关闭WebSocket连接');
    
    // 停止重连
    this.config.enableReconnect = false;
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    // 停止心跳
    this.stopHeartbeat();
    
    // 关闭连接
    if (this.socket && this.isConnected) {
      this.socket.close({
        code: 1000,
        reason: 'Normal closure'
      });
    }
    
    // 重置状态
    this.isConnected = false;
    this.isConnecting = false;
    this.socket = null;
  }

  /**
   * 事件监听
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * 移除事件监听
   */
  off(event, callback) {
    if (!this.eventListeners.has(event)) {
      return;
    }
    
    const listeners = this.eventListeners.get(event);
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (!this.eventListeners.has(event)) {
      return;
    }
    
    this.eventListeners.get(event).forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`事件回调执行错误 (${event}):`, error);
      }
    });
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      messageQueueSize: this.messageQueue.length,
      config: this.config
    };
  }
}

// 创建全局实例
const webSocketManager = new WebSocketManager();

module.exports = {
  WebSocketManager,
  webSocketManager
};
