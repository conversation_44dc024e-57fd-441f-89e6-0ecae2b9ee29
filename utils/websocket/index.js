/**
 * WebSocket实时数据同步系统统一导出
 * WebSocket Real-time Data Sync System
 */

const { webSocketManager } = require('./websocket-manager.js');
const { realtimeSyncService } = require('./realtime-sync-service.js');
const { dataChangeListener, DataWatcher } = require('./data-change-listener.js');

/**
 * 实时同步系统管理器
 */
class RealtimeSyncSystem {
  constructor() {
    this.isInitialized = false;
    this.webSocket = webSocketManager;
    this.syncService = realtimeSyncService;
    this.dataListener = dataChangeListener;
  }

  /**
   * 初始化实时同步系统
   */
  async initialize(config = {}) {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('正在初始化实时同步系统...');

      // 初始化实时同步服务
      await this.syncService.initialize(config);

      this.isInitialized = true;
      console.log('实时同步系统初始化完成');

      return {
        success: true,
        message: '实时同步系统初始化成功'
      };

    } catch (error) {
      console.error('实时同步系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 订阅数据同步
   */
  subscribe(dataType, callback) {
    return this.syncService.subscribe(dataType, callback);
  }

  /**
   * 取消订阅
   */
  unsubscribe(dataType, callback) {
    return this.syncService.unsubscribe(dataType, callback);
  }

  /**
   * 监听数据变更
   */
  watch(dataType, selector, callback, options) {
    return this.dataListener.watch(dataType, selector, callback, options);
  }

  /**
   * 取消监听
   */
  unwatch(watcherId) {
    return this.dataListener.unwatch(watcherId);
  }

  /**
   * 手动同步数据
   */
  async sync(dataType) {
    return this.syncService.manualSync(dataType);
  }

  /**
   * 获取系统状态
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      webSocket: this.webSocket.getStatus(),
      syncService: this.syncService.getSyncStatus(),
      dataListener: this.dataListener.getWatcherStatus()
    };
  }

  /**
   * 销毁系统
   */
  destroy() {
    this.dataListener.clear();
    this.syncService.destroy();
    this.isInitialized = false;
    console.log('实时同步系统已销毁');
  }
}

// 创建全局实例
const realtimeSyncSystem = new RealtimeSyncSystem();

// 便捷API
const RealtimeAPI = {
  // 初始化
  init: (config) => realtimeSyncSystem.initialize(config),
  
  // 订阅数据同步
  subscribe: (dataType, callback) => realtimeSyncSystem.subscribe(dataType, callback),
  
  // 取消订阅
  unsubscribe: (dataType, callback) => realtimeSyncSystem.unsubscribe(dataType, callback),
  
  // 监听数据变更
  watch: (dataType, selector, callback, options) => 
    realtimeSyncSystem.watch(dataType, selector, callback, options),
  
  // 取消监听
  unwatch: (watcherId) => realtimeSyncSystem.unwatch(watcherId),
  
  // 手动同步
  sync: (dataType) => realtimeSyncSystem.sync(dataType),
  
  // 获取状态
  getStatus: () => realtimeSyncSystem.getStatus(),
  
  // 销毁
  destroy: () => realtimeSyncSystem.destroy()
};

// 页面混入对象
const RealtimeSyncMixin = {
  data: {
    realtimeSubscriptions: [],
    dataWatchers: []
  },

  onLoad() {
    // 初始化实时同步（如果还未初始化）
    if (!realtimeSyncSystem.isInitialized) {
      RealtimeAPI.init().catch(error => {
        console.error('页面初始化实时同步失败:', error);
      });
    }
  },

  onUnload() {
    // 清理订阅和监听器
    this.cleanupRealtimeSync();
  },

  methods: {
    // 订阅数据同步
    subscribeData(dataType, callback) {
      const unsubscribe = RealtimeAPI.subscribe(dataType, callback);
      this.data.realtimeSubscriptions.push(unsubscribe);
      return unsubscribe;
    },

    // 监听数据变更
    watchData(dataType, selector, callback, options) {
      const watcherId = RealtimeAPI.watch(dataType, selector, callback, options);
      this.data.dataWatchers.push(watcherId);
      return watcherId;
    },

    // 清理实时同步
    cleanupRealtimeSync() {
      // 取消所有订阅
      this.data.realtimeSubscriptions.forEach(unsubscribe => {
        try {
          unsubscribe();
        } catch (error) {
          console.error('取消订阅失败:', error);
        }
      });

      // 取消所有监听器
      this.data.dataWatchers.forEach(watcherId => {
        try {
          RealtimeAPI.unwatch(watcherId);
        } catch (error) {
          console.error('取消监听失败:', error);
        }
      });

      // 清空数组
      this.data.realtimeSubscriptions = [];
      this.data.dataWatchers = [];
    }
  }
};

// 快速设置方法
const QuickSetup = {
  // 设置健康记录实时同步
  setupHealthRecordsSync(page, callback) {
    return page.subscribeData('health_records', (event) => {
      if (event.type === 'data_updated' || event.type === 'realtime_update') {
        callback(event.data);
      }
    });
  },

  // 设置生产记录实时同步
  setupProductionRecordsSync(page, callback) {
    return page.subscribeData('production_records', (event) => {
      if (event.type === 'data_updated' || event.type === 'realtime_update') {
        callback(event.data);
      }
    });
  },

  // 设置鹅群信息实时同步
  setupFlocksSync(page, callback) {
    return page.subscribeData('flocks', (event) => {
      if (event.type === 'data_updated' || event.type === 'realtime_update') {
        callback(event.data);
      }
    });
  },

  // 设置特定记录监听
  setupRecordWatch(page, dataType, recordId, callback) {
    return page.watchData(dataType, { id: recordId }, (changeInfo) => {
      callback(changeInfo);
    });
  },

  // 设置列表变更监听
  setupListWatch(page, dataType, callback) {
    return page.watchData(dataType, null, (changeInfo) => {
      if (changeInfo.type === 'create' || changeInfo.type === 'delete') {
        callback(changeInfo);
      }
    });
  }
};

// 主要导出
module.exports = {
  // 核心类
  RealtimeSyncSystem,
  
  // 全局实例
  realtimeSyncSystem,
  
  // 子模块
  webSocketManager,
  realtimeSyncService,
  dataChangeListener,
  
  // 便捷API
  RealtimeAPI,
  DataWatcher,
  
  // 页面混入
  RealtimeSyncMixin,
  
  // 快速设置
  QuickSetup,
  
  // 便捷方法
  ...RealtimeAPI,
  
  // 重构信息
  __version: '1.0',
  __modules: [
    'utils/websocket/websocket-manager.js',
    'utils/websocket/realtime-sync-service.js',
    'utils/websocket/data-change-listener.js'
  ]
};
