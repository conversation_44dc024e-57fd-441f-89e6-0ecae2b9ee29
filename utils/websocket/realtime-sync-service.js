/**
 * 实时数据同步服务
 * Real-time Data Sync Service
 */

const { webSocketManager } = require('./websocket-manager.js');
const { apiClient } = require('../api-client-final.js');

class RealtimeSyncService {
  constructor() {
    this.isInitialized = false;
    this.syncChannels = new Map(); // 同步频道
    this.dataCache = new Map(); // 数据缓存
    this.syncQueue = new Map(); // 同步队列
    this.conflictResolver = null; // 冲突解决器
    this.syncStats = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      conflicts: 0
    };
  }

  /**
   * 初始化实时同步服务
   */
  async initialize(config = {}) {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('初始化实时数据同步服务...');

      // 获取WebSocket配置
      const wsConfig = await this.getWebSocketConfig();
      
      // 初始化WebSocket连接
      await webSocketManager.initialize({
        url: wsConfig.url,
        protocols: wsConfig.protocols,
        enableHeartbeat: true,
        enableReconnect: true,
        enableMessageQueue: true
      });

      // 绑定WebSocket事件
      this.bindWebSocketEvents();

      // 注册同步频道
      this.registerSyncChannels();

      this.isInitialized = true;
      console.log('实时数据同步服务初始化完成');

    } catch (error) {
      console.error('实时数据同步服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取WebSocket配置
   */
  async getWebSocketConfig() {
    try {
      const response = await apiClient.get('/api/v2/system/websocket-config');
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || '获取WebSocket配置失败');
      }
    } catch (error) {
      console.warn('获取WebSocket配置失败，使用默认配置:', error);
      
      // 使用默认配置
      return {
        url: 'wss://api.zhihuiyange.com/ws',
        protocols: ['sync-protocol-v1']
      };
    }
  }

  /**
   * 绑定WebSocket事件
   */
  bindWebSocketEvents() {
    // 连接成功
    webSocketManager.on('connected', () => {
      console.log('WebSocket连接成功，开始数据同步');
      this.onConnected();
    });

    // 连接断开
    webSocketManager.on('disconnected', () => {
      console.log('WebSocket连接断开，暂停数据同步');
      this.onDisconnected();
    });

    // 接收消息
    webSocketManager.on('message', (message) => {
      this.handleSyncMessage(message);
    });

    // 数据同步消息
    webSocketManager.on('data_sync', (message) => {
      this.handleDataSync(message);
    });

    // 数据变更通知
    webSocketManager.on('data_change', (message) => {
      this.handleDataChange(message);
    });

    // 冲突通知
    webSocketManager.on('sync_conflict', (message) => {
      this.handleSyncConflict(message);
    });
  }

  /**
   * 注册同步频道
   */
  registerSyncChannels() {
    // 注册各种数据类型的同步频道
    this.registerChannel('health_records', {
      endpoint: '/api/v2/health/records',
      syncInterval: 30000, // 30秒
      enableRealtime: true
    });

    this.registerChannel('production_records', {
      endpoint: '/api/v2/production/records',
      syncInterval: 60000, // 1分钟
      enableRealtime: true
    });

    this.registerChannel('flocks', {
      endpoint: '/api/v2/flocks',
      syncInterval: 120000, // 2分钟
      enableRealtime: true
    });

    this.registerChannel('inventory', {
      endpoint: '/api/v2/inventory',
      syncInterval: 300000, // 5分钟
      enableRealtime: false
    });

    this.registerChannel('user_profile', {
      endpoint: '/api/v2/auth/userinfo',
      syncInterval: 600000, // 10分钟
      enableRealtime: false
    });
  }

  /**
   * 注册同步频道
   */
  registerChannel(channelName, config) {
    const channel = {
      name: channelName,
      config: config,
      lastSync: null,
      syncTimer: null,
      subscribers: new Set(),
      isActive: false
    };

    this.syncChannels.set(channelName, channel);
    console.log(`同步频道已注册: ${channelName}`);
  }

  /**
   * 订阅数据同步
   */
  subscribe(channelName, callback) {
    const channel = this.syncChannels.get(channelName);
    if (!channel) {
      throw new Error(`同步频道不存在: ${channelName}`);
    }

    channel.subscribers.add(callback);
    
    // 如果是第一个订阅者，启动同步
    if (channel.subscribers.size === 1) {
      this.startChannelSync(channelName);
    }

    console.log(`已订阅数据同步: ${channelName}`);
    
    // 返回取消订阅函数
    return () => {
      this.unsubscribe(channelName, callback);
    };
  }

  /**
   * 取消订阅
   */
  unsubscribe(channelName, callback) {
    const channel = this.syncChannels.get(channelName);
    if (!channel) {
      return;
    }

    channel.subscribers.delete(callback);
    
    // 如果没有订阅者了，停止同步
    if (channel.subscribers.size === 0) {
      this.stopChannelSync(channelName);
    }

    console.log(`已取消订阅数据同步: ${channelName}`);
  }

  /**
   * 启动频道同步
   */
  startChannelSync(channelName) {
    const channel = this.syncChannels.get(channelName);
    if (!channel || channel.isActive) {
      return;
    }

    channel.isActive = true;
    
    // 立即同步一次
    this.syncChannelData(channelName);
    
    // 设置定时同步
    if (channel.config.syncInterval > 0) {
      channel.syncTimer = setInterval(() => {
        this.syncChannelData(channelName);
      }, channel.config.syncInterval);
    }

    // 如果支持实时同步，订阅WebSocket消息
    if (channel.config.enableRealtime && webSocketManager.isConnected) {
      this.subscribeRealtimeUpdates(channelName);
    }

    console.log(`频道同步已启动: ${channelName}`);
  }

  /**
   * 停止频道同步
   */
  stopChannelSync(channelName) {
    const channel = this.syncChannels.get(channelName);
    if (!channel || !channel.isActive) {
      return;
    }

    channel.isActive = false;
    
    // 清除定时器
    if (channel.syncTimer) {
      clearInterval(channel.syncTimer);
      channel.syncTimer = null;
    }

    // 取消实时订阅
    if (channel.config.enableRealtime) {
      this.unsubscribeRealtimeUpdates(channelName);
    }

    console.log(`频道同步已停止: ${channelName}`);
  }

  /**
   * 同步频道数据
   */
  async syncChannelData(channelName) {
    const channel = this.syncChannels.get(channelName);
    if (!channel) {
      return;
    }

    try {
      console.log(`开始同步频道数据: ${channelName}`);
      
      // 获取最新数据
      const response = await apiClient.get(channel.config.endpoint, {
        data: {
          lastSync: channel.lastSync,
          includeDeleted: true
        }
      });

      if (response.success) {
        const newData = response.data;
        const cachedData = this.dataCache.get(channelName);
        
        // 检查数据是否有变更
        if (this.hasDataChanged(cachedData, newData)) {
          // 更新缓存
          this.dataCache.set(channelName, newData);
          
          // 通知订阅者
          this.notifySubscribers(channelName, {
            type: 'data_updated',
            data: newData,
            previousData: cachedData,
            timestamp: Date.now()
          });
          
          console.log(`频道数据已更新: ${channelName}`);
        }
        
        // 更新同步时间
        channel.lastSync = Date.now();
        this.syncStats.successfulSyncs++;
        
      } else {
        throw new Error(response.message || '同步失败');
      }

    } catch (error) {
      console.error(`同步频道数据失败 (${channelName}):`, error);
      this.syncStats.failedSyncs++;
      
      // 通知订阅者同步失败
      this.notifySubscribers(channelName, {
        type: 'sync_error',
        error: error.message,
        timestamp: Date.now()
      });
    } finally {
      this.syncStats.totalSyncs++;
    }
  }

  /**
   * 检查数据是否有变更
   */
  hasDataChanged(oldData, newData) {
    if (!oldData && newData) {
      return true;
    }
    
    if (oldData && !newData) {
      return true;
    }
    
    if (!oldData && !newData) {
      return false;
    }
    
    // 简单的JSON字符串比较（可以优化为更精确的比较）
    return JSON.stringify(oldData) !== JSON.stringify(newData);
  }

  /**
   * 通知订阅者
   */
  notifySubscribers(channelName, event) {
    const channel = this.syncChannels.get(channelName);
    if (!channel) {
      return;
    }

    channel.subscribers.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error(`订阅者回调执行错误 (${channelName}):`, error);
      }
    });
  }

  /**
   * 订阅实时更新
   */
  subscribeRealtimeUpdates(channelName) {
    if (webSocketManager.isConnected) {
      webSocketManager.send({
        type: 'subscribe',
        channel: channelName,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 取消实时订阅
   */
  unsubscribeRealtimeUpdates(channelName) {
    if (webSocketManager.isConnected) {
      webSocketManager.send({
        type: 'unsubscribe',
        channel: channelName,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 处理连接成功
   */
  onConnected() {
    // 重新订阅所有活跃频道的实时更新
    this.syncChannels.forEach((channel, channelName) => {
      if (channel.isActive && channel.config.enableRealtime) {
        this.subscribeRealtimeUpdates(channelName);
      }
    });
  }

  /**
   * 处理连接断开
   */
  onDisconnected() {
    // 连接断开时，依赖定时同步保持数据更新
    console.log('WebSocket断开，切换到定时同步模式');
  }

  /**
   * 处理同步消息
   */
  handleSyncMessage(message) {
    console.log('收到同步消息:', message);
    
    // 根据消息类型处理
    switch (message.type) {
      case 'data_sync':
        this.handleDataSync(message);
        break;
      case 'data_change':
        this.handleDataChange(message);
        break;
      case 'sync_conflict':
        this.handleSyncConflict(message);
        break;
      default:
        console.log('未知的同步消息类型:', message.type);
    }
  }

  /**
   * 处理数据同步
   */
  handleDataSync(message) {
    const { channel, data, timestamp } = message;
    
    if (this.syncChannels.has(channel)) {
      // 更新缓存
      this.dataCache.set(channel, data);
      
      // 通知订阅者
      this.notifySubscribers(channel, {
        type: 'realtime_update',
        data: data,
        timestamp: timestamp
      });
      
      console.log(`实时数据已更新: ${channel}`);
    }
  }

  /**
   * 处理数据变更
   */
  handleDataChange(message) {
    const { channel, changeType, data, timestamp } = message;
    
    if (this.syncChannels.has(channel)) {
      // 通知订阅者数据变更
      this.notifySubscribers(channel, {
        type: 'data_change',
        changeType: changeType, // create, update, delete
        data: data,
        timestamp: timestamp
      });
      
      console.log(`数据变更通知: ${channel} - ${changeType}`);
    }
  }

  /**
   * 处理同步冲突
   */
  handleSyncConflict(message) {
    const { channel, conflictData, timestamp } = message;
    
    this.syncStats.conflicts++;
    
    // 通知订阅者冲突
    this.notifySubscribers(channel, {
      type: 'sync_conflict',
      conflictData: conflictData,
      timestamp: timestamp
    });
    
    console.warn(`同步冲突: ${channel}`, conflictData);
    
    // 如果有冲突解决器，尝试自动解决
    if (this.conflictResolver) {
      this.conflictResolver(channel, conflictData);
    }
  }

  /**
   * 设置冲突解决器
   */
  setConflictResolver(resolver) {
    this.conflictResolver = resolver;
  }

  /**
   * 手动触发同步
   */
  async manualSync(channelName) {
    if (!this.syncChannels.has(channelName)) {
      throw new Error(`同步频道不存在: ${channelName}`);
    }
    
    await this.syncChannelData(channelName);
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    const channelStatus = {};
    
    this.syncChannels.forEach((channel, name) => {
      channelStatus[name] = {
        isActive: channel.isActive,
        lastSync: channel.lastSync,
        subscriberCount: channel.subscribers.size,
        enableRealtime: channel.config.enableRealtime
      };
    });
    
    return {
      isInitialized: this.isInitialized,
      webSocketConnected: webSocketManager.isConnected,
      channels: channelStatus,
      stats: this.syncStats
    };
  }

  /**
   * 销毁服务
   */
  destroy() {
    // 停止所有频道同步
    this.syncChannels.forEach((channel, channelName) => {
      this.stopChannelSync(channelName);
    });
    
    // 断开WebSocket连接
    webSocketManager.disconnect();
    
    // 清理数据
    this.syncChannels.clear();
    this.dataCache.clear();
    this.syncQueue.clear();
    
    this.isInitialized = false;
    console.log('实时数据同步服务已销毁');
  }
}

// 创建全局实例
const realtimeSyncService = new RealtimeSyncService();

module.exports = {
  RealtimeSyncService,
  realtimeSyncService
};
