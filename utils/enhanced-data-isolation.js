/**
 * 增强的数据隔离系统 - 优化版
 * Enhanced Data Isolation System - Optimized Version
 * 
 * 新增功能：
 * - 查询结果缓存
 * - 自动查询优化
 * - 批量操作支持
 * - 性能监控
 * - 数据访问审计
 */

const { ISOLATION_LEVELS, COLLECTION_ISOLATION_CONFIG } = require('./data-isolation');
const { permissionManager } = require('./enhanced-permission-manager');

/**
 * 增强的数据隔离中间件
 */
class EnhancedDataIsolationMiddleware {
  constructor() {
    // 查询缓存
    this.queryCache = new Map();
    this.cacheConfig = {
      queryTTL: 2 * 60 * 1000, // 查询缓存2分钟
      maxCacheSize: 1000        // 最大缓存条目数
    };
    
    // 性能统计
    this.performanceStats = {
      totalQueries: 0,
      cacheHits: 0,
      avgQueryTime: 0,
      slowQueries: []
    };
    
    // 数据库连接池
    this.dbConnections = new Map();
    
    // 初始化清理定时器
    this.initCacheCleanup();
  }

  /**
   * 创建安全的数据库查询（增强版）
   * @param {Object} user 用户对象
   * @param {string} collection 集合名称
   * @param {Object} baseQuery 基础查询条件
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  async createSecureQuery(user, collection, baseQuery = {}, options = {}) {
    const startTime = Date.now();
    
    try {
      // 1. 验证用户和集合
      if (!user || !collection) {
        throw new Error('用户信息或集合名称不能为空');
      }

      // 2. 检查缓存
      const cacheKey = this.generateCacheKey(user, collection, baseQuery, options);
      if (options.useCache !== false) {
        const cached = this.getFromCache(cacheKey);
        if (cached) {
          this.updatePerformanceStats(startTime, true);
          return cached;
        }
      }

      // 3. 构建安全查询
      const secureQuery = await this.buildSecureQuery(user, collection, baseQuery, options);
      
      // 4. 执行查询优化
      const optimizedQuery = this.optimizeQuery(secureQuery, options);
      
      // 5. 记录访问日志
      this.logDataAccess(user, collection, 'read', optimizedQuery);
      
      // 6. 缓存结果（如果启用）
      if (options.useCache !== false && options.cacheable !== false) {
        this.setCache(cacheKey, optimizedQuery);
      }
      
      this.updatePerformanceStats(startTime, false);
      return optimizedQuery;
      
    } catch (error) {
      console.error('创建安全查询失败:', error);
      this.updatePerformanceStats(startTime, false, error);
      throw error;
    }
  }

  /**
   * 构建安全查询条件
   * @param {Object} user 用户对象
   * @param {string} collection 集合名称
   * @param {Object} baseQuery 基础查询条件
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  async buildSecureQuery(user, collection, baseQuery, options) {
    const config = COLLECTION_ISOLATION_CONFIG[collection] || {
      level: ISOLATION_LEVELS.TENANT,
      readPermissions: [],
      writePermissions: []
    };

    // 1. 权限验证
    await this.validatePermissions(user, config.readPermissions, 'read');

    // 2. 构建隔离查询
    const isolationQuery = await this.buildIsolationQuery(user, config, options);
    
    // 3. 合并查询条件
    const secureQuery = {
      ...baseQuery,
      ...isolationQuery
    };

    // 4. 应用查询过滤器
    return this.applyQueryFilters(secureQuery, user, collection, options);
  }

  /**
   * 构建隔离查询条件
   * @param {Object} user 用户对象
   * @param {Object} config 集合配置
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  async buildIsolationQuery(user, config, options) {
    const { level } = config;

    switch (level) {
      case ISOLATION_LEVELS.PLATFORM:
        return this.buildPlatformQuery(user, options);
      
      case ISOLATION_LEVELS.TENANT:
        return this.buildTenantQuery(user, options);
      
      case ISOLATION_LEVELS.USER:
        return this.buildUserQuery(user, options);
      
      case ISOLATION_LEVELS.SHARED:
        return this.buildSharedQuery(user, options);
      
      default:
        return this.buildTenantQuery(user, options);
    }
  }

  /**
   * 构建平台级查询
   * @param {Object} user 用户对象
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  async buildPlatformQuery(user, options) {
    const isPlatformAdmin = await permissionManager.checkPermission(user, 'PLATFORM_ADMIN');
    
    if (!isPlatformAdmin) {
      // 非平台管理员只能查看公开数据
      return {
        $and: [
          { status: { $in: ['published', 'active'] } },
          { visibility: { $in: ['public', 'tenant'] } }
        ]
      };
    }
    
    return {}; // 平台管理员可以查看所有数据
  }

  /**
   * 构建租户级查询
   * @param {Object} user 用户对象
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  async buildTenantQuery(user, options) {
    if (!user.tenant_id) {
      throw new Error('用户未绑定租户，无法访问租户数据');
    }

    const query = {
      tenant_id: user.tenant_id
    };

    // 检查是否有查看所有数据的权限
    const canViewAll = await this.checkViewAllPermission(user, options.collection);
    
    if (!canViewAll) {
      // 只能查看自己的数据
      query.user_id = user._id;
    }

    // 添加状态过滤（排除已删除的数据）
    if (options.includeDeleted !== true) {
      query.status = { $ne: 'deleted' };
    }

    return query;
  }

  /**
   * 构建用户级查询
   * @param {Object} user 用户对象
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  async buildUserQuery(user, options) {
    const query = {
      user_id: user._id,
      tenant_id: user.tenant_id
    };

    if (options.includeDeleted !== true) {
      query.status = { $ne: 'deleted' };
    }

    return query;
  }

  /**
   * 构建共享数据查询
   * @param {Object} user 用户对象
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  async buildSharedQuery(user, options) {
    return {
      $or: [
        { tenant_id: user.tenant_id },
        { shared_with_tenants: { $in: [user.tenant_id] } },
        { visibility: 'public' },
        { created_by: user._id }
      ]
    };
  }

  /**
   * 验证数据写入安全性（增强版）
   * @param {Object} user 用户对象
   * @param {string} collection 集合名称
   * @param {Object} data 数据对象
   * @param {Object} options 选项
   * @returns {Promise<Object>}
   */
  async validateWriteData(user, collection, data, options = {}) {
    const startTime = Date.now();
    
    try {
      const config = COLLECTION_ISOLATION_CONFIG[collection];
      if (!config) {
        throw new Error(`未知的集合：${collection}`);
      }

      // 1. 权限验证
      await this.validatePermissions(user, config.writePermissions, 'write');

      // 2. 数据验证
      const validatedData = await this.validateDataByLevel(user, config, data, options);

      // 3. 数据清理和标准化
      const cleanedData = this.cleanAndStandardizeData(validatedData, config);

      // 4. 记录写入日志
      this.logDataAccess(user, collection, 'write', cleanedData);

      this.updatePerformanceStats(startTime, false);
      return cleanedData;

    } catch (error) {
      console.error('数据写入验证失败:', error);
      this.updatePerformanceStats(startTime, false, error);
      throw error;
    }
  }

  /**
   * 根据隔离级别验证数据
   * @param {Object} user 用户对象
   * @param {Object} config 集合配置
   * @param {Object} data 数据对象
   * @param {Object} options 选项
   * @returns {Promise<Object>}
   */
  async validateDataByLevel(user, config, data, options) {
    switch (config.level) {
      case ISOLATION_LEVELS.PLATFORM:
        return this.validatePlatformData(user, data, options);
      
      case ISOLATION_LEVELS.TENANT:
        return this.validateTenantData(user, data, options);
      
      case ISOLATION_LEVELS.USER:
        return this.validateUserData(user, data, options);
      
      case ISOLATION_LEVELS.SHARED:
        return this.validateSharedData(user, data, options);
      
      default:
        return this.validateTenantData(user, data, options);
    }
  }

  /**
   * 验证平台级数据
   * @param {Object} user 用户对象
   * @param {Object} data 数据对象
   * @param {Object} options 选项
   * @returns {Promise<Object>}
   */
  async validatePlatformData(user, data, options) {
    const isPlatformAdmin = await permissionManager.checkPermission(user, 'PLATFORM_ADMIN');
    
    if (!isPlatformAdmin) {
      throw new Error('权限不足：只有平台管理员可以操作平台级数据');
    }

    return {
      ...data,
      created_by: user._id,
      tenant_id: null,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * 验证租户级数据
   * @param {Object} user 用户对象
   * @param {Object} data 数据对象
   * @param {Object} options 选项
   * @returns {Promise<Object>}
   */
  async validateTenantData(user, data, options) {
    if (!user.tenant_id) {
      throw new Error('用户未绑定租户');
    }

    const validatedData = {
      ...data,
      tenant_id: user.tenant_id,
      created_by: user._id,
      created_at: new Date(),
      updated_at: new Date()
    };

    // 检查是否需要添加用户ID
    const canManageAll = await this.checkManageAllPermission(user, options.collection);
    if (!canManageAll) {
      validatedData.user_id = user._id;
    }

    return validatedData;
  }

  /**
   * 验证用户级数据
   * @param {Object} user 用户对象
   * @param {Object} data 数据对象
   * @param {Object} options 选项
   * @returns {Promise<Object>}
   */
  async validateUserData(user, data, options) {
    return {
      ...data,
      user_id: user._id,
      tenant_id: user.tenant_id,
      created_by: user._id,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * 验证共享数据
   * @param {Object} user 用户对象
   * @param {Object} data 数据对象
   * @param {Object} options 选项
   * @returns {Promise<Object>}
   */
  async validateSharedData(user, data, options) {
    const validatedData = {
      ...data,
      created_by: user._id,
      tenant_id: user.tenant_id,
      created_at: new Date(),
      updated_at: new Date()
    };

    // 设置默认共享级别
    if (!validatedData.visibility) {
      validatedData.visibility = 'tenant'; // 默认租户内可见
    }

    return validatedData;
  }

  /**
   * 批量操作支持
   * @param {Object} user 用户对象
   * @param {string} collection 集合名称
   * @param {Array} operations 操作数组
   * @param {Object} options 选项
   * @returns {Promise<Array>}
   */
  async batchOperations(user, collection, operations, options = {}) {
    const results = [];
    const errors = [];

    for (let i = 0; i < operations.length; i++) {
      const operation = operations[i];
      
      try {
        let result;
        
        switch (operation.type) {
          case 'read':
            result = await this.createSecureQuery(user, collection, operation.query, options);
            break;
          
          case 'write':
            result = await this.validateWriteData(user, collection, operation.data, options);
            break;
          
          default:
            throw new Error(`不支持的操作类型：${operation.type}`);
        }
        
        results.push({ index: i, success: true, result });
        
      } catch (error) {
        errors.push({ index: i, error: error.message });
        results.push({ index: i, success: false, error: error.message });
      }
    }

    return {
      results,
      errors,
      successCount: results.filter(r => r.success).length,
      errorCount: errors.length
    };
  }

  // 辅助方法
  generateCacheKey(user, collection, baseQuery, options) {
    const keyData = {
      userId: user._id,
      tenantId: user.tenant_id,
      collection,
      query: JSON.stringify(baseQuery),
      options: JSON.stringify(options)
    };
    
    return Buffer.from(JSON.stringify(keyData)).toString('base64');
  }

  getFromCache(cacheKey) {
    const cached = this.queryCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheConfig.queryTTL) {
      this.performanceStats.cacheHits++;
      return cached.data;
    }
    
    return null;
  }

  setCache(cacheKey, data) {
    // 检查缓存大小限制
    if (this.queryCache.size >= this.cacheConfig.maxCacheSize) {
      // 删除最旧的缓存项
      const oldestKey = this.queryCache.keys().next().value;
      this.queryCache.delete(oldestKey);
    }
    
    this.queryCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  async validatePermissions(user, requiredPermissions, operation) {
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    for (const permission of requiredPermissions) {
      const hasPermission = await permissionManager.checkPermission(user, permission);
      if (hasPermission) {
        return true;
      }
    }

    throw new Error(`权限不足：无法执行${operation}操作`);
  }

  async checkViewAllPermission(user, collection) {
    const viewAllPermissions = [
      'FLOCK_VIEW_ALL',
      'HEALTH_VIEW_ALL',
      'MATERIAL_VIEW_ALL',
      'FINANCE_VIEW_ALL',
      'PRODUCTION_VIEW_ALL'
    ];

    for (const permission of viewAllPermissions) {
      if (await permissionManager.checkPermission(user, permission)) {
        return true;
      }
    }

    return false;
  }

  async checkManageAllPermission(user, collection) {
    const managePermissions = [
      'FLOCK_EDIT',
      'HEALTH_EDIT',
      'MATERIAL_EDIT',
      'FINANCE_EDIT',
      'PRODUCTION_EDIT'
    ];

    for (const permission of managePermissions) {
      if (await permissionManager.checkPermission(user, permission)) {
        return true;
      }
    }

    return false;
  }

  optimizeQuery(query, options) {
    // 查询优化逻辑
    const optimized = { ...query };
    
    // 添加默认排序
    if (!options.sort) {
      optimized._sort = { created_at: -1 };
    }
    
    // 添加默认限制
    if (!options.limit && !options.skip) {
      optimized._limit = 100; // 默认限制100条
    }
    
    return optimized;
  }

  cleanAndStandardizeData(data, config) {
    const cleaned = { ...data };
    
    // 移除不允许的字段
    delete cleaned._id;
    delete cleaned.__v;
    
    // 标准化时间字段
    if (cleaned.created_at && typeof cleaned.created_at === 'string') {
      cleaned.created_at = new Date(cleaned.created_at);
    }
    
    return cleaned;
  }

  applyQueryFilters(query, user, collection, options) {
    // 应用额外的查询过滤器
    const filtered = { ...query };
    
    // 软删除过滤
    if (options.includeSoftDeleted !== true) {
      filtered.deleted_at = { $exists: false };
    }
    
    return filtered;
  }

  logDataAccess(user, collection, operation, data) {
    const logEntry = {
      timestamp: new Date(),
      userId: user._id,
      tenantId: user.tenant_id,
      collection,
      operation,
      dataSize: JSON.stringify(data).length,
      ip: user.ip || 'unknown'
    };

    // 这里可以发送到日志服务
    console.log('数据访问日志:', logEntry);
  }

  updatePerformanceStats(startTime, fromCache, error = null) {
    const duration = Date.now() - startTime;
    
    this.performanceStats.totalQueries++;
    
    if (fromCache) {
      this.performanceStats.cacheHits++;
    }
    
    // 更新平均查询时间
    this.performanceStats.avgQueryTime = 
      (this.performanceStats.avgQueryTime + duration) / 2;
    
    // 记录慢查询
    if (duration > 1000) { // 超过1秒的查询
      this.performanceStats.slowQueries.push({
        timestamp: Date.now(),
        duration,
        error: error?.message
      });
      
      // 只保留最近的50个慢查询
      if (this.performanceStats.slowQueries.length > 50) {
        this.performanceStats.slowQueries.shift();
      }
    }
  }

  initCacheCleanup() {
    // 每3分钟清理一次过期缓存
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 3 * 60 * 1000);
  }

  cleanupExpiredCache() {
    const now = Date.now();
    
    for (const [key, value] of this.queryCache) {
      if (now - value.timestamp > this.cacheConfig.queryTTL) {
        this.queryCache.delete(key);
      }
    }
  }

  getPerformanceStats() {
    return {
      ...this.performanceStats,
      cacheHitRate: this.performanceStats.totalQueries > 0 
        ? (this.performanceStats.cacheHits / this.performanceStats.totalQueries * 100).toFixed(2) + '%'
        : '0%',
      cacheSize: this.queryCache.size
    };
  }
}

// 创建全局实例
const enhancedDataIsolation = new EnhancedDataIsolationMiddleware();

module.exports = {
  EnhancedDataIsolationMiddleware,
  enhancedDataIsolation
};
