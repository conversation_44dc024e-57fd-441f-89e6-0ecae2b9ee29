/**
 * 数据调度规范
 * Data Scheduling Standards
 * 
 * 功能：
 * - 统一的数据流转机制
 * - 数据同步策略
 * - 缓存管理
 * - 错误处理和重试机制
 * - 数据一致性保证
 */

/**
 * 数据调度配置
 */
const SCHEDULER_CONFIG = {
  // 缓存配置
  CACHE: {
    DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
    MAX_SIZE: 100, // 最大缓存条目数
    CLEANUP_INTERVAL: 10 * 60 * 1000 // 10分钟清理一次
  },
  
  // 重试配置
  RETRY: {
    MAX_ATTEMPTS: 3,
    INITIAL_DELAY: 1000, // 1秒
    BACKOFF_FACTOR: 2,
    MAX_DELAY: 10000 // 10秒
  },
  
  // 同步配置
  SYNC: {
    BATCH_SIZE: 50,
    SYNC_INTERVAL: 30 * 1000, // 30秒
    CONFLICT_RESOLUTION: 'server_wins' // server_wins, client_wins, merge
  }
};

/**
 * 数据缓存管理器
 */
class DataCacheManager {
  constructor() {
    this.cache = new Map();
    this.timestamps = new Map();
    this.startCleanupTimer();
  }

  /**
   * 设置缓存
   */
  set(key, data, ttl = SCHEDULER_CONFIG.CACHE.DEFAULT_TTL) {
    // 检查缓存大小限制
    if (this.cache.size >= SCHEDULER_CONFIG.CACHE.MAX_SIZE) {
      this.evictOldest();
    }

    this.cache.set(key, data);
    this.timestamps.set(key, Date.now() + ttl);
  }

  /**
   * 获取缓存
   */
  get(key) {
    const timestamp = this.timestamps.get(key);
    
    if (!timestamp || Date.now() > timestamp) {
      // 缓存过期
      this.delete(key);
      return null;
    }

    return this.cache.get(key);
  }

  /**
   * 删除缓存
   */
  delete(key) {
    this.cache.delete(key);
    this.timestamps.delete(key);
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear();
    this.timestamps.clear();
  }

  /**
   * 淘汰最旧的缓存项
   */
  evictOldest() {
    let oldestKey = null;
    let oldestTime = Infinity;

    for (const [key, timestamp] of this.timestamps) {
      if (timestamp < oldestTime) {
        oldestTime = timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.delete(oldestKey);
    }
  }

  /**
   * 启动清理定时器
   */
  startCleanupTimer() {
    setInterval(() => {
      this.cleanup();
    }, SCHEDULER_CONFIG.CACHE.CLEANUP_INTERVAL);
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, timestamp] of this.timestamps) {
      if (now > timestamp) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.delete(key));
  }

  /**
   * 生成缓存键
   */
  static generateKey(collection, query, user) {
    const queryStr = JSON.stringify(query);
    const userKey = user ? `${user.tenant_id}_${user._id}` : 'anonymous';
    return `${collection}_${userKey}_${this.hashString(queryStr)}`;
  }

  /**
   * 字符串哈希函数
   */
  static hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }
}

/**
 * 数据同步管理器
 */
class DataSyncManager {
  constructor() {
    this.syncQueue = [];
    this.syncing = false;
    this.lastSyncTime = 0;
  }

  /**
   * 添加同步任务
   */
  addSyncTask(task) {
    this.syncQueue.push({
      ...task,
      id: this.generateTaskId(),
      timestamp: Date.now(),
      attempts: 0
    });

    // 如果不在同步中，立即开始同步
    if (!this.syncing) {
      this.startSync();
    }
  }

  /**
   * 开始同步
   */
  async startSync() {
    if (this.syncing || this.syncQueue.length === 0) {
      return;
    }

    this.syncing = true;

    try {
      while (this.syncQueue.length > 0) {
        const batch = this.syncQueue.splice(0, SCHEDULER_CONFIG.SYNC.BATCH_SIZE);
        await this.processSyncBatch(batch);
      }
    } catch (error) {
      console.error('数据同步失败:', error);
    } finally {
      this.syncing = false;
      this.lastSyncTime = Date.now();
    }
  }

  /**
   * 处理同步批次
   */
  async processSyncBatch(batch) {
    const promises = batch.map(task => this.processSyncTask(task));
    const results = await Promise.allSettled(promises);

    // 处理失败的任务
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        const task = batch[index];
        this.handleSyncFailure(task, result.reason);
      }
    });
  }

  /**
   * 处理单个同步任务
   */
  async processSyncTask(task) {
    const { type, collection, data, operation } = task;

    switch (type) {
      case 'create':
        return await this.syncCreate(collection, data);
      case 'update':
        return await this.syncUpdate(collection, data);
      case 'delete':
        return await this.syncDelete(collection, data);
      default:
        throw new Error(`未知的同步任务类型: ${type}`);
    }
  }

  /**
   * 同步创建操作
   */
  async syncCreate(collection, data) {
    // 实现创建同步逻辑
    console.log(`同步创建: ${collection}`, data);
  }

  /**
   * 同步更新操作
   */
  async syncUpdate(collection, data) {
    // 实现更新同步逻辑
    console.log(`同步更新: ${collection}`, data);
  }

  /**
   * 同步删除操作
   */
  async syncDelete(collection, data) {
    // 实现删除同步逻辑
    console.log(`同步删除: ${collection}`, data);
  }

  /**
   * 处理同步失败
   */
  handleSyncFailure(task, error) {
    task.attempts++;
    task.lastError = error.message;

    if (task.attempts < SCHEDULER_CONFIG.RETRY.MAX_ATTEMPTS) {
      // 重新加入队列，延迟执行
      setTimeout(() => {
        this.syncQueue.push(task);
      }, this.calculateRetryDelay(task.attempts));
    } else {
      // 达到最大重试次数，记录错误
      console.error(`同步任务失败，已达到最大重试次数:`, task);
      this.logSyncFailure(task);
    }
  }

  /**
   * 计算重试延迟
   */
  calculateRetryDelay(attempts) {
    const delay = SCHEDULER_CONFIG.RETRY.INITIAL_DELAY * 
                  Math.pow(SCHEDULER_CONFIG.RETRY.BACKOFF_FACTOR, attempts - 1);
    return Math.min(delay, SCHEDULER_CONFIG.RETRY.MAX_DELAY);
  }

  /**
   * 记录同步失败
   */
  logSyncFailure(task) {
    // 这里可以将失败记录写入日志集合
    console.error('同步失败记录:', {
      taskId: task.id,
      type: task.type,
      collection: task.collection,
      attempts: task.attempts,
      lastError: task.lastError,
      timestamp: new Date()
    });
  }

  /**
   * 生成任务ID
   */
  generateTaskId() {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 数据调度器主类
 */
class DataScheduler {
  constructor() {
    this.cacheManager = new DataCacheManager();
    this.syncManager = new DataSyncManager();
    this.requestQueue = [];
    this.processing = false;
  }

  /**
   * 执行数据查询（带缓存）
   */
  async query(collection, queryParams, user, options = {}) {
    const { useCache = true, cacheTTL } = options;
    
    // 生成缓存键
    const cacheKey = DataCacheManager.generateKey(collection, queryParams, user);
    
    // 尝试从缓存获取
    if (useCache) {
      const cachedData = this.cacheManager.get(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    try {
      // 执行实际查询
      const result = await this.executeQuery(collection, queryParams, user);
      
      // 缓存结果
      if (useCache && result.success) {
        this.cacheManager.set(cacheKey, result, cacheTTL);
      }
      
      return result;
    } catch (error) {
      console.error('数据查询失败:', error);
      throw error;
    }
  }

  /**
   * 执行数据写入（带同步）
   */
  async write(collection, operation, data, user, options = {}) {
    const { sync = true, immediate = false } = options;

    try {
      // 执行写入操作
      const result = await this.executeWrite(collection, operation, data, user);
      
      // 清除相关缓存
      this.invalidateCache(collection, user);
      
      // 添加同步任务
      if (sync) {
        this.syncManager.addSyncTask({
          type: operation,
          collection,
          data: { ...data, _id: result._id },
          user,
          immediate
        });
      }
      
      return result;
    } catch (error) {
      console.error('数据写入失败:', error);
      throw error;
    }
  }

  /**
   * 执行实际查询
   */
  async executeQuery(collection, queryParams, user) {
    // 这里调用实际的数据库查询逻辑
    // 可以是云函数调用或直接数据库操作
    const { smartApiCall } = require('./api');
    
    return await smartApiCall('POST', `/data/${collection}/query`, {
      query: queryParams,
      user: user
    });
  }

  /**
   * 执行实际写入
   */
  async executeWrite(collection, operation, data, user) {
    const { smartApiCall } = require('./api');
    
    return await smartApiCall('POST', `/data/${collection}/${operation}`, {
      data: data,
      user: user
    });
  }

  /**
   * 使缓存失效
   */
  invalidateCache(collection, user) {
    // 删除相关的缓存项
    const userKey = user ? `${user.tenant_id}_${user._id}` : 'anonymous';
    const pattern = `${collection}_${userKey}_`;
    
    for (const key of this.cacheManager.cache.keys()) {
      if (key.startsWith(pattern)) {
        this.cacheManager.delete(key);
      }
    }
  }

  /**
   * 批量操作
   */
  async batch(operations, user, options = {}) {
    const { transaction = false } = options;
    
    if (transaction) {
      return await this.executeTransaction(operations, user);
    } else {
      return await this.executeBatch(operations, user);
    }
  }

  /**
   * 执行事务
   */
  async executeTransaction(operations, user) {
    // 实现事务逻辑
    console.log('执行事务操作:', operations);
  }

  /**
   * 执行批量操作
   */
  async executeBatch(operations, user) {
    const results = [];
    
    for (const operation of operations) {
      try {
        const result = await this.write(
          operation.collection,
          operation.type,
          operation.data,
          user,
          operation.options
        );
        results.push({ success: true, result });
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    
    return results;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      cache: {
        size: this.cacheManager.cache.size,
        hitRate: this.calculateCacheHitRate()
      },
      sync: {
        queueSize: this.syncManager.syncQueue.length,
        lastSyncTime: this.syncManager.lastSyncTime,
        syncing: this.syncManager.syncing
      }
    };
  }

  /**
   * 计算缓存命中率
   */
  calculateCacheHitRate() {
    // 这里需要实现缓存命中率统计逻辑
    return 0.85; // 示例值
  }
}

// 创建全局数据调度器实例
const globalDataScheduler = new DataScheduler();

/**
 * 数据操作辅助函数
 */
const DataHelper = {
  /**
   * 查询数据
   */
  async query(collection, query, user, options) {
    return await globalDataScheduler.query(collection, query, user, options);
  },

  /**
   * 创建数据
   */
  async create(collection, data, user, options) {
    return await globalDataScheduler.write(collection, 'create', data, user, options);
  },

  /**
   * 更新数据
   */
  async update(collection, data, user, options) {
    return await globalDataScheduler.write(collection, 'update', data, user, options);
  },

  /**
   * 删除数据
   */
  async delete(collection, data, user, options) {
    return await globalDataScheduler.write(collection, 'delete', data, user, options);
  },

  /**
   * 批量操作
   */
  async batch(operations, user, options) {
    return await globalDataScheduler.batch(operations, user, options);
  },

  /**
   * 清除缓存
   */
  clearCache(collection, user) {
    globalDataScheduler.invalidateCache(collection, user);
  },

  /**
   * 获取统计信息
   */
  getStats() {
    return globalDataScheduler.getStats();
  }
};

module.exports = {
  SCHEDULER_CONFIG,
  DataCacheManager,
  DataSyncManager,
  DataScheduler,
  DataHelper,
  globalDataScheduler
};
