/**
 * 数据脱敏工具
 * Data Masking Utility
 */

class DataMasking {
  constructor() {
    this.maskingRules = {
      phone: {
        pattern: /^(\d{3})\d{4}(\d{4})$/,
        replacement: '$1****$2'
      },
      idcard: {
        pattern: /^(\d{6})\d{8}(\d{4})$/,
        replacement: '$1********$2'
      },
      email: {
        pattern: /^(.).*@(.*)$/,
        replacement: '$1***@$2'
      },
      bankcard: {
        pattern: /^(\d{4})\d{8,12}(\d{4})$/,
        replacement: '$1****$2'
      },
      name: {
        pattern: /^(.)(.*)$/,
        replacement: '$1***'
      }
    };
  }

  /**
   * 脱敏敏感数据
   */
  maskSensitiveData(data, type) {
    if (!data || typeof data !== 'string') {
      return data;
    }

    const rule = this.maskingRules[type];
    if (!rule) {
      return this.defaultMask(data);
    }

    return data.replace(rule.pattern, rule.replacement);
  }

  /**
   * 默认脱敏规则
   */
  defaultMask(data) {
    if (data.length <= 2) {
      return '*'.repeat(data.length);
    }
    
    const start = data.substring(0, 1);
    const end = data.substring(data.length - 1);
    const middle = '*'.repeat(data.length - 2);
    
    return start + middle + end;
  }

  /**
   * 批量脱敏
   */
  maskBatch(dataObject, maskingConfig) {
    const masked = {};
    
    for (const [key, value] of Object.entries(dataObject)) {
      if (maskingConfig[key]) {
        masked[key] = this.maskSensitiveData(value, maskingConfig[key]);
      } else {
        masked[key] = value;
      }
    }
    
    return masked;
  }

  /**
   * 深度脱敏（处理嵌套对象）
   */
  deepMask(obj, maskingConfig) {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepMask(item, maskingConfig));
    }

    const masked = {};
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null) {
        masked[key] = this.deepMask(value, maskingConfig);
      } else if (maskingConfig[key]) {
        masked[key] = this.maskSensitiveData(value, maskingConfig[key]);
      } else {
        masked[key] = value;
      }
    }

    return masked;
  }
}

// 创建全局实例
const dataMasking = new DataMasking();

// 便捷方法
const maskSensitiveData = (data, type) => dataMasking.maskSensitiveData(data, type);

module.exports = {
  DataMasking,
  dataMasking,
  maskSensitiveData
};
