/**
 * 输入验证器
 * Input Validator
 */

class InputValidator {
  constructor() {
    this.patterns = {
      sql_injection: [
        /('|;|--|union|select|insert|delete|update|drop|create|alter|exec|execute)/i,
        /(script|javascript|vbscript|onload|onerror|onclick)/i
      ],
      xss_attack: [
        /<script[^>]*>.*?<\/script>/gi,
        /<iframe[^>]*>.*?<\/iframe>/gi,
        /javascript:/gi,
        /on\w+\s*=/gi
      ],
      html_tags: /<[^>]+>/g,
      special_chars: /['"<>&]/g
    };
  }

  /**
   * 验证输入数据
   */
  validateInput(input, type = 'text') {
    const result = {
      valid: true,
      errors: [],
      sanitized: input
    };

    if (!input || typeof input !== 'string') {
      result.valid = false;
      result.errors.push('invalid_input');
      return result;
    }

    // 检查SQL注入
    if (this.detectSQLInjection(input)) {
      result.valid = false;
      result.errors.push('sql_injection');
    }

    // 检查XSS攻击
    if (this.detectXSSAttack(input)) {
      result.valid = false;
      result.errors.push('xss_attack');
    }

    // 根据类型进行特定验证
    switch (type) {
      case 'email':
        if (!this.validateEmail(input)) {
          result.valid = false;
          result.errors.push('invalid_email');
        }
        break;
      case 'phone':
        if (!this.validatePhone(input)) {
          result.valid = false;
          result.errors.push('invalid_phone');
        }
        break;
      case 'url':
        if (!this.validateURL(input)) {
          result.valid = false;
          result.errors.push('invalid_url');
        }
        break;
    }

    // 如果验证通过，进行清理
    if (result.valid) {
      result.sanitized = this.sanitizeInput(input);
    }

    return result;
  }

  /**
   * 检测SQL注入
   */
  detectSQLInjection(input) {
    return this.patterns.sql_injection.some(pattern => pattern.test(input));
  }

  /**
   * 检测XSS攻击
   */
  detectXSSAttack(input) {
    return this.patterns.xss_attack.some(pattern => pattern.test(input));
  }

  /**
   * 清理输入数据
   */
  sanitizeInput(input) {
    if (!input || typeof input !== 'string') {
      return '';
    }

    let sanitized = input;

    // 移除HTML标签
    sanitized = sanitized.replace(this.patterns.html_tags, '');

    // 转义特殊字符
    sanitized = sanitized
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');

    return sanitized.trim();
  }

  /**
   * 验证邮箱格式
   */
  validateEmail(email) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(email);
  }

  /**
   * 验证手机号格式
   */
  validatePhone(phone) {
    const phonePattern = /^1[3-9]\d{9}$/;
    return phonePattern.test(phone);
  }

  /**
   * 验证URL格式
   */
  validateURL(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 验证密码强度
   */
  validatePasswordStrength(password) {
    const result = {
      valid: false,
      score: 0,
      requirements: {
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false
      }
    };

    if (!password || typeof password !== 'string') {
      return result;
    }

    // 检查长度
    if (password.length >= 8) {
      result.requirements.length = true;
      result.score += 1;
    }

    // 检查大写字母
    if (/[A-Z]/.test(password)) {
      result.requirements.uppercase = true;
      result.score += 1;
    }

    // 检查小写字母
    if (/[a-z]/.test(password)) {
      result.requirements.lowercase = true;
      result.score += 1;
    }

    // 检查数字
    if (/\d/.test(password)) {
      result.requirements.number = true;
      result.score += 1;
    }

    // 检查特殊字符
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      result.requirements.special = true;
      result.score += 1;
    }

    // 密码强度评级
    result.valid = result.score >= 3;

    return result;
  }

  /**
   * 验证身份证号
   */
  validateIDCard(idCard) {
    if (!idCard || typeof idCard !== 'string') {
      return false;
    }

    // 18位身份证号验证
    const idCardPattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    
    if (!idCardPattern.test(idCard)) {
      return false;
    }

    // 校验码验证
    const factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    
    let sum = 0;
    for (let i = 0; i < 17; i++) {
      sum += parseInt(idCard[i]) * factors[i];
    }
    
    const checkCode = checkCodes[sum % 11];
    return checkCode === idCard[17].toUpperCase();
  }

  /**
   * 批量验证
   */
  validateBatch(inputs) {
    const results = {};
    
    for (const [key, config] of Object.entries(inputs)) {
      const { value, type, required = false } = config;
      
      if (required && (!value || value.trim() === '')) {
        results[key] = {
          valid: false,
          errors: ['required'],
          sanitized: ''
        };
        continue;
      }
      
      if (value) {
        results[key] = this.validateInput(value, type);
      } else {
        results[key] = {
          valid: true,
          errors: [],
          sanitized: ''
        };
      }
    }
    
    return results;
  }
}

// 创建全局实例
const inputValidator = new InputValidator();

// 便捷方法
const validateInput = (input, type) => inputValidator.validateInput(input, type);
const sanitizeInput = (input) => inputValidator.sanitizeInput(input);

module.exports = {
  InputValidator,
  inputValidator,
  validateInput,
  sanitizeInput
};
