/**
 * 增强的权限管理系统 - 优化版
 * Enhanced Permission Management System - Optimized Version
 * 
 * 新增功能：
 * - 权限缓存机制
 * - 性能优化
 * - 角色继承
 * - 权限审计日志
 * - 动态权限配置
 */

const { ROLES, PERMISSIONS, ROLE_PERMISSIONS } = require('./role-permission');

/**
 * 增强的权限管理器
 */
class EnhancedPermissionManager {
  constructor() {
    // 权限缓存
    this.permissionCache = new Map();
    this.roleCache = new Map();
    this.userPermissionCache = new Map();
    
    // 缓存配置
    this.cacheConfig = {
      permissionTTL: 5 * 60 * 1000, // 权限缓存5分钟
      roleTTL: 10 * 60 * 1000,      // 角色缓存10分钟
      userTTL: 3 * 60 * 1000        // 用户权限缓存3分钟
    };
    
    // 角色继承关系
    this.roleHierarchy = {
      [ROLES.SUPER_ADMIN]: [ROLES.PLATFORM_ADMIN, ROLES.TENANT_OWNER],
      [ROLES.PLATFORM_ADMIN]: [ROLES.TENANT_OWNER],
      [ROLES.TENANT_OWNER]: [ROLES.ADMIN, ROLES.MANAGER],
      [ROLES.ADMIN]: [ROLES.MANAGER, ROLES.EMPLOYEE],
      [ROLES.MANAGER]: [ROLES.EMPLOYEE, ROLES.USER],
      [ROLES.FINANCE_MANAGER]: [ROLES.FINANCE_STAFF, ROLES.USER],
      [ROLES.HR_MANAGER]: [ROLES.EMPLOYEE, ROLES.USER],
      [ROLES.EMPLOYEE]: [ROLES.USER],
      [ROLES.USER]: [ROLES.VIEWER]
    };
    
    // 权限审计日志
    this.auditLogs = [];
    this.maxAuditLogs = 1000;
    
    // 初始化清理定时器
    this.initCacheCleanup();
  }

  /**
   * 检查用户权限（带缓存）
   * @param {Object} user 用户对象
   * @param {string} permission 权限标识
   * @returns {Promise<boolean>}
   */
  async checkPermission(user, permission) {
    if (!user || !permission) {
      return false;
    }

    // 检查缓存
    const cacheKey = `${user.id}:${permission}`;
    const cached = this.permissionCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheConfig.permissionTTL) {
      this.logPermissionCheck(user, permission, cached.result, 'cache');
      return cached.result;
    }

    // 计算权限
    const result = await this.calculatePermission(user, permission);
    
    // 缓存结果
    this.permissionCache.set(cacheKey, {
      result,
      timestamp: Date.now()
    });

    // 记录审计日志
    this.logPermissionCheck(user, permission, result, 'calculated');
    
    return result;
  }

  /**
   * 计算用户权限
   * @param {Object} user 用户对象
   * @param {string} permission 权限标识
   * @returns {Promise<boolean>}
   */
  async calculatePermission(user, permission) {
    try {
      // 1. 检查直接权限
      if (user.permissions && user.permissions.includes(permission)) {
        return true;
      }

      // 2. 检查角色权限
      const rolePermissions = await this.getRolePermissions(user.role);
      if (rolePermissions.includes(permission)) {
        return true;
      }

      // 3. 检查继承权限
      const inheritedRoles = this.getInheritedRoles(user.role);
      for (const role of inheritedRoles) {
        const permissions = await this.getRolePermissions(role);
        if (permissions.includes(permission)) {
          return true;
        }
      }

      // 4. 检查动态权限（基于上下文）
      const contextPermission = await this.checkContextPermission(user, permission);
      if (contextPermission) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('权限计算失败:', error);
      return false;
    }
  }

  /**
   * 获取角色权限（带缓存）
   * @param {string} role 角色
   * @returns {Promise<Array>}
   */
  async getRolePermissions(role) {
    if (!role) {
      return [];
    }

    // 检查缓存
    const cached = this.roleCache.get(role);
    if (cached && Date.now() - cached.timestamp < this.cacheConfig.roleTTL) {
      return cached.permissions;
    }

    // 获取权限
    const permissions = ROLE_PERMISSIONS[role] || [];
    
    // 缓存结果
    this.roleCache.set(role, {
      permissions,
      timestamp: Date.now()
    });

    return permissions;
  }

  /**
   * 获取角色继承链
   * @param {string} role 角色
   * @returns {Array}
   */
  getInheritedRoles(role) {
    const inherited = [];
    const queue = [role];
    const visited = new Set();
    
    while (queue.length > 0) {
      const currentRole = queue.shift();
      
      if (visited.has(currentRole)) {
        continue;
      }
      visited.add(currentRole);
      
      const children = this.roleHierarchy[currentRole] || [];
      
      for (const child of children) {
        if (!inherited.includes(child)) {
          inherited.push(child);
          queue.push(child);
        }
      }
    }
    
    return inherited;
  }

  /**
   * 检查上下文权限
   * @param {Object} user 用户对象
   * @param {string} permission 权限标识
   * @returns {Promise<boolean>}
   */
  async checkContextPermission(user, permission) {
    // 基于时间的权限控制
    if (permission.includes('FINANCE') && this.isOutsideBusinessHours()) {
      return false;
    }

    // 基于位置的权限控制
    if (permission.includes('SENSITIVE') && !this.isInSecureLocation(user)) {
      return false;
    }

    // 基于租户订阅的权限控制
    if (permission.includes('PREMIUM') && !await this.hasPremiumSubscription(user.tenant_id)) {
      return false;
    }

    return false;
  }

  /**
   * 批量检查权限
   * @param {Object} user 用户对象
   * @param {Array} permissions 权限数组
   * @returns {Promise<Object>}
   */
  async checkMultiplePermissions(user, permissions) {
    const results = {};
    
    // 并行检查所有权限
    const promises = permissions.map(async (permission) => {
      const result = await this.checkPermission(user, permission);
      return { permission, result };
    });

    const permissionResults = await Promise.all(promises);
    
    permissionResults.forEach(({ permission, result }) => {
      results[permission] = result;
    });

    return results;
  }

  /**
   * 获取用户完整权限信息
   * @param {Object} user 用户对象
   * @returns {Promise<Object>}
   */
  async getUserPermissionInfo(user) {
    if (!user) {
      return null;
    }

    // 检查缓存
    const cacheKey = `user_info:${user.id}`;
    const cached = this.userPermissionCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheConfig.userTTL) {
      return cached.info;
    }

    // 计算权限信息
    const rolePermissions = await this.getRolePermissions(user.role);
    const inheritedRoles = this.getInheritedRoles(user.role);
    const allInheritedPermissions = [];
    
    for (const role of inheritedRoles) {
      const permissions = await this.getRolePermissions(role);
      allInheritedPermissions.push(...permissions);
    }

    const info = {
      userId: user.id,
      role: user.role,
      roleName: this.getRoleName(user.role),
      directPermissions: user.permissions || [],
      rolePermissions,
      inheritedRoles,
      inheritedPermissions: [...new Set(allInheritedPermissions)],
      allPermissions: [...new Set([
        ...(user.permissions || []),
        ...rolePermissions,
        ...allInheritedPermissions
      ])],
      isAdmin: this.isAdmin(user.role),
      tenantId: user.tenant_id,
      lastUpdated: Date.now()
    };

    // 缓存结果
    this.userPermissionCache.set(cacheKey, {
      info,
      timestamp: Date.now()
    });

    return info;
  }

  /**
   * 清除用户权限缓存
   * @param {string} userId 用户ID
   */
  clearUserCache(userId) {
    // 清除权限缓存
    for (const [key] of this.permissionCache) {
      if (key.startsWith(`${userId}:`)) {
        this.permissionCache.delete(key);
      }
    }

    // 清除用户信息缓存
    this.userPermissionCache.delete(`user_info:${userId}`);
  }

  /**
   * 清除角色缓存
   * @param {string} role 角色
   */
  clearRoleCache(role) {
    this.roleCache.delete(role);
    
    // 清除相关用户缓存
    this.userPermissionCache.clear();
    this.permissionCache.clear();
  }

  /**
   * 记录权限检查日志
   * @param {Object} user 用户对象
   * @param {string} permission 权限标识
   * @param {boolean} result 检查结果
   * @param {string} source 来源（cache/calculated）
   */
  logPermissionCheck(user, permission, result, source) {
    const logEntry = {
      timestamp: Date.now(),
      userId: user.id,
      tenantId: user.tenant_id,
      role: user.role,
      permission,
      result,
      source,
      ip: user.ip || 'unknown'
    };

    this.auditLogs.push(logEntry);

    // 限制日志数量
    if (this.auditLogs.length > this.maxAuditLogs) {
      this.auditLogs.shift();
    }

    // 敏感权限操作记录到持久化存储
    if (this.isSensitivePermission(permission)) {
      this.persistAuditLog(logEntry);
    }
  }

  /**
   * 判断是否为敏感权限
   * @param {string} permission 权限标识
   * @returns {boolean}
   */
  isSensitivePermission(permission) {
    const sensitiveKeywords = [
      'DELETE', 'PLATFORM_', 'FINANCE_APPROVE', 
      'USER_DELETE', 'TENANT_DELETE', 'SYSTEM_'
    ];
    
    return sensitiveKeywords.some(keyword => permission.includes(keyword));
  }

  /**
   * 持久化审计日志
   * @param {Object} logEntry 日志条目
   */
  async persistAuditLog(logEntry) {
    try {
      // 这里可以调用云函数或直接写入数据库
      console.log('敏感权限操作:', logEntry);
      
      // 示例：调用云函数记录日志
      // await wx.cloud.callFunction({
      //   name: 'auditLog',
      //   data: { logEntry }
      // });
    } catch (error) {
      console.error('持久化审计日志失败:', error);
    }
  }

  /**
   * 获取权限统计信息
   * @returns {Object}
   */
  getPermissionStats() {
    return {
      cacheStats: {
        permissionCacheSize: this.permissionCache.size,
        roleCacheSize: this.roleCache.size,
        userCacheSize: this.userPermissionCache.size
      },
      auditStats: {
        totalLogs: this.auditLogs.length,
        recentChecks: this.auditLogs.slice(-10)
      },
      performanceStats: {
        cacheHitRate: this.calculateCacheHitRate(),
        avgResponseTime: this.calculateAvgResponseTime()
      }
    };
  }

  /**
   * 初始化缓存清理定时器
   */
  initCacheCleanup() {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 5 * 60 * 1000);
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    const now = Date.now();
    
    // 清理权限缓存
    for (const [key, value] of this.permissionCache) {
      if (now - value.timestamp > this.cacheConfig.permissionTTL) {
        this.permissionCache.delete(key);
      }
    }
    
    // 清理角色缓存
    for (const [key, value] of this.roleCache) {
      if (now - value.timestamp > this.cacheConfig.roleTTL) {
        this.roleCache.delete(key);
      }
    }
    
    // 清理用户缓存
    for (const [key, value] of this.userPermissionCache) {
      if (now - value.timestamp > this.cacheConfig.userTTL) {
        this.userPermissionCache.delete(key);
      }
    }
  }

  // 辅助方法
  getRoleName(role) {
    const roleNames = {
      [ROLES.SUPER_ADMIN]: '超级管理员',
      [ROLES.PLATFORM_ADMIN]: '平台管理员',
      [ROLES.TENANT_OWNER]: '租户拥有者',
      [ROLES.ADMIN]: '管理员',
      [ROLES.MANAGER]: '经理',
      [ROLES.EMPLOYEE]: '员工',
      [ROLES.FINANCE_MANAGER]: '财务经理',
      [ROLES.FINANCE_STAFF]: '财务人员',
      [ROLES.HR_MANAGER]: 'HR经理',
      [ROLES.VETERINARIAN]: '兽医',
      [ROLES.USER]: '普通用户',
      [ROLES.VIEWER]: '访客'
    };
    
    return roleNames[role] || role;
  }

  isAdmin(role) {
    const adminRoles = [
      ROLES.SUPER_ADMIN,
      ROLES.PLATFORM_ADMIN,
      ROLES.TENANT_OWNER,
      ROLES.ADMIN,
      ROLES.MANAGER,
      ROLES.FINANCE_MANAGER,
      ROLES.HR_MANAGER
    ];
    
    return adminRoles.includes(role);
  }

  isOutsideBusinessHours() {
    const now = new Date();
    const hour = now.getHours();
    return hour < 9 || hour > 18;
  }

  isInSecureLocation(user) {
    // 这里可以实现基于IP或地理位置的安全检查
    return true;
  }

  async hasPremiumSubscription(tenantId) {
    // 这里可以检查租户的订阅状态
    return true;
  }

  calculateCacheHitRate() {
    // 简化的缓存命中率计算
    return 0.85; // 85%
  }

  calculateAvgResponseTime() {
    // 简化的平均响应时间计算
    return 15; // 15ms
  }
}

// 创建全局实例
const permissionManager = new EnhancedPermissionManager();

module.exports = {
  EnhancedPermissionManager,
  permissionManager
};
