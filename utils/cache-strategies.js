/**
 * 智能缓存配置
 */

const CACHE_STRATEGIES = {
  // 用户信息 - 长期缓存
  USER_PROFILE: {
    ttl: 30 * 60 * 1000, // 30分钟
    key: 'user_profile',
    autoRefresh: true
  },
  
  // 生产数据 - 中期缓存
  PRODUCTION_DATA: {
    ttl: 10 * 60 * 1000, // 10分钟
    key: 'production_data',
    autoRefresh: false
  },
  
  // 实时数据 - 短期缓存
  REALTIME_DATA: {
    ttl: 2 * 60 * 1000, // 2分钟
    key: 'realtime_data',
    autoRefresh: true
  },
  
  // 静态数据 - 超长期缓存
  STATIC_DATA: {
    ttl: 24 * 60 * 60 * 1000, // 24小时
    key: 'static_data',
    autoRefresh: false
  }
};

module.exports = {
  CACHE_STRATEGIES
};