/**
 * 云开发API适配器
 * 将传统HTTP API调用转换为云函数调用
 * 保持与原有API接口的兼容性
 */

class CloudAPIAdapter {
  constructor() {
    this.app = getApp();
    this.cloud = this.app.globalData.cloud;
    this.db = this.app.globalData.db;
    
    // 云函数映射表
    this.functionMap = {
      // 认证相关
      '/api/v2/auth/login': 'login',
      '/api/v2/auth/wechat': 'login',
      '/api/v2/auth/refresh': 'refreshToken',
      '/api/v2/users/profile': 'getUserInfo',
      
      // 鹅群管理
      '/api/v2/flocks': 'flockManagement',
      '/api/v2/flocks/create': 'flockManagement',
      '/api/v2/flocks/update': 'flockManagement',
      '/api/v2/flocks/delete': 'flockManagement',
      
      // 健康管理
      '/api/v2/health/records': 'healthManagement',
      '/api/v2/health/stats': 'healthManagement',
      '/api/v2/health/ai-diagnosis': 'healthManagement',
      
      // 生产管理
      '/api/v2/production/records': 'productionManagement',
      '/api/v2/production/environment': 'productionManagement',
      '/api/v2/production/finance': 'productionManagement',
      
      // 商城管理
      '/api/v2/shop/products': 'shopManagement',
      '/api/v2/shop/orders': 'shopManagement',
      '/api/v2/shop/cart': 'shopManagement',
      
      // OA办公
      '/api/v2/oa/applications': 'oaManagement',
      '/api/v2/oa/approvals': 'oaManagement',
      
      // 系统管理
      '/api/v2/system/config': 'systemManagement',
      '/api/v2/system/announcements': 'systemManagement',
      '/api/v2/system/knowledge': 'systemManagement'
    };
  }

  /**
   * 将HTTP方法和URL转换为云函数调用参数
   */
  mapToCloudFunction(method, url, data = {}) {
    // 清理URL，移除查询参数
    const cleanUrl = url.split('?')[0];
    
    // 获取云函数名称
    let functionName = this.functionMap[cleanUrl];
    
    // 如果没有直接映射，尝试模糊匹配
    if (!functionName) {
      for (const [pattern, func] of Object.entries(this.functionMap)) {
        if (cleanUrl.includes(pattern.replace('/api/v2/', ''))) {
          functionName = func;
          break;
        }
      }
    }
    
    if (!functionName) {
      throw new Error(`未找到对应的云函数: ${url}`);
    }
    
    // 确定操作类型
    let action = this.getActionFromMethodAndUrl(method, url);
    
    return {
      functionName,
      action,
      data: {
        action,
        data,
        ...this.parseUrlParams(url)
      }
    };
  }

  /**
   * 根据HTTP方法和URL确定操作类型
   */
  getActionFromMethodAndUrl(method, url) {
    const cleanUrl = url.split('?')[0];
    
    // 根据HTTP方法确定基本操作
    switch (method.toUpperCase()) {
      case 'GET':
        if (cleanUrl.includes('/stats') || cleanUrl.includes('/summary')) {
          return 'stats';
        }
        if (cleanUrl.match(/\/\d+$/)) {
          return 'detail';
        }
        return 'list';
        
      case 'POST':
        if (cleanUrl.includes('/login') || cleanUrl.includes('/auth')) {
          return 'login';
        }
        if (cleanUrl.includes('/ai-diagnosis')) {
          return 'aiDiagnosis';
        }
        return 'create';
        
      case 'PUT':
        return 'update';
        
      case 'DELETE':
        return 'delete';
        
      default:
        return 'unknown';
    }
  }

  /**
   * 解析URL参数
   */
  parseUrlParams(url) {
    const params = {};
    const urlParts = url.split('?');
    
    if (urlParts.length > 1) {
      const queryString = urlParts[1];
      const pairs = queryString.split('&');
      
      pairs.forEach(pair => {
        const [key, value] = pair.split('=');
        if (key && value) {
          params[decodeURIComponent(key)] = decodeURIComponent(value);
        }
      });
    }
    
    // 提取路径中的ID参数
    const pathParts = urlParts[0].split('/');
    const lastPart = pathParts[pathParts.length - 1];
    if (/^\d+$/.test(lastPart)) {
      params.id = lastPart;
    }
    
    return params;
  }

  /**
   * 执行云函数调用
   */
  async callCloudFunction(functionName, data) {
    try {
      console.log(`调用云函数: ${functionName}`, data);
      
      const result = await this.cloud.callFunction({
        name: functionName,
        data: data
      });
      
      console.log(`云函数 ${functionName} 调用结果:`, result);
      
      if (result.errMsg && result.errMsg !== 'cloud.callFunction:ok') {
        throw new Error(`云函数调用失败: ${result.errMsg}`);
      }
      
      return result.result;
      
    } catch (error) {
      console.error(`云函数 ${functionName} 调用失败:`, error);
      throw error;
    }
  }

  /**
   * 适配器主要方法 - 将HTTP请求转换为云函数调用
   */
  async request(method, url, data = {}, options = {}) {
    try {
      // 检查云开发是否可用
      if (!this.cloud) {
        throw new Error('云开发环境未初始化');
      }
      
      // 映射到云函数
      const { functionName, action, data: functionData } = this.mapToCloudFunction(method, url, data);
      
      // 调用云函数
      const result = await this.callCloudFunction(functionName, functionData);
      
      // 处理响应格式，保持与HTTP API的兼容性
      return this.formatResponse(result);
      
    } catch (error) {
      console.error('云API适配器调用失败:', error);
      
      // 如果云函数调用失败，可以考虑回退到HTTP请求
      if (options.fallbackToHttp) {
        console.log('回退到HTTP请求');
        return this.fallbackToHttpRequest(method, url, data, options);
      }
      
      throw error;
    }
  }

  /**
   * 格式化响应，保持与HTTP API的兼容性
   */
  formatResponse(cloudResult) {
    // 云函数返回的结果通常已经是处理好的格式
    if (cloudResult && typeof cloudResult === 'object') {
      // 如果云函数返回了标准格式，直接返回
      if (cloudResult.hasOwnProperty('success') || cloudResult.hasOwnProperty('code')) {
        return cloudResult;
      }
      
      // 否则包装成标准格式
      return {
        success: true,
        data: cloudResult,
        message: '操作成功'
      };
    }
    
    return cloudResult;
  }

  /**
   * 回退到HTTP请求（可选）
   */
  async fallbackToHttpRequest(method, url, data, options) {
    // 这里可以调用原有的HTTP请求方法
    const request = require('./request.js');
    
    switch (method.toUpperCase()) {
      case 'GET':
        return request.get(url, data);
      case 'POST':
        return request.post(url, data);
      case 'PUT':
        return request.put(url, data);
      case 'DELETE':
        return request.del(url);
      default:
        throw new Error(`不支持的HTTP方法: ${method}`);
    }
  }

  /**
   * GET请求适配
   */
  get(url, params = {}, options = {}) {
    const urlWithParams = params && Object.keys(params).length > 0 
      ? `${url}?${new URLSearchParams(params).toString()}`
      : url;
    return this.request('GET', urlWithParams, {}, options);
  }

  /**
   * POST请求适配
   */
  post(url, data = {}, options = {}) {
    return this.request('POST', url, data, options);
  }

  /**
   * PUT请求适配
   */
  put(url, data = {}, options = {}) {
    return this.request('PUT', url, data, options);
  }

  /**
   * DELETE请求适配
   */
  delete(url, options = {}) {
    return this.request('DELETE', url, {}, options);
  }
}

// 创建全局实例
const cloudAPIAdapter = new CloudAPIAdapter();

// 导出适配器和便捷方法
module.exports = {
  CloudAPIAdapter,
  cloudAPIAdapter,
  
  // 便捷方法
  get: (url, params, options) => cloudAPIAdapter.get(url, params, options),
  post: (url, data, options) => cloudAPIAdapter.post(url, data, options),
  put: (url, data, options) => cloudAPIAdapter.put(url, data, options),
  delete: (url, options) => cloudAPIAdapter.delete(url, options)
};
