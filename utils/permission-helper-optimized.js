/**
 * 优化的权限助手工具
 * Optimized Permission Helper
 * 
 * 提供高性能的权限检查API，集成缓存管理
 */

const permissionCacheManager = require('./permission-cache-manager');
const { PERMISSIONS, ROLES } = require('./role-permission');

class PermissionHelper {
  constructor() {
    this.cacheManager = permissionCacheManager;
  }

  /**
   * 快速权限检查（单个权限）
   * @param {string} permission 权限标识
   * @param {Object} options 选项
   * @returns {Promise<boolean>}
   */
  async can(permission, options = {}) {
    const userId = this.getCurrentUserId();
    return await this.cacheManager.checkPermission(userId, permission, options);
  }

  /**
   * 快速权限检查（多个权限 - 任一）
   * @param {Array} permissions 权限数组
   * @param {Object} options 选项
   * @returns {Promise<boolean>}
   */
  async canAny(permissions, options = {}) {
    const userId = this.getCurrentUserId();
    return await this.cacheManager.checkPermission(userId, permissions, {
      ...options,
      requireAll: false
    });
  }

  /**
   * 快速权限检查（多个权限 - 全部）
   * @param {Array} permissions 权限数组
   * @param {Object} options 选项
   * @returns {Promise<boolean>}
   */
  async canAll(permissions, options = {}) {
    const userId = this.getCurrentUserId();
    return await this.cacheManager.checkPermission(userId, permissions, {
      ...options,
      requireAll: true
    });
  }

  /**
   * 财务模块权限检查（优化版）
   */
  async finance() {
    const userId = this.getCurrentUserId();
    
    // 批量检查财务相关权限
    const permissionChecks = [
      { permission: PERMISSIONS.FINANCE_VIEW_ALL },
      { permission: PERMISSIONS.FINANCE_VIEW_OWN },
      { permission: PERMISSIONS.FINANCE_CREATE },
      { permission: PERMISSIONS.FINANCE_EDIT },
      { permission: PERMISSIONS.FINANCE_DELETE },
      { permission: PERMISSIONS.FINANCE_APPROVE },
      { permission: PERMISSIONS.FINANCE_REPORTS },
      { permission: PERMISSIONS.FINANCE_EXPORT },
      { permission: PERMISSIONS.FINANCE_BUDGET }
    ];
    
    const results = await this.cacheManager.batchCheckPermissions(userId, permissionChecks);
    
    return {
      canViewAll: results[0],
      canViewOwn: results[1],
      canCreate: results[2],
      canEdit: results[3],
      canDelete: results[4],
      canApprove: results[5],
      canViewReports: results[6],
      canExport: results[7],
      canManageBudget: results[8],
      
      // 便捷方法
      canView: results[0] || results[1],
      canManage: results[2] || results[3] || results[4],
      isFinanceRole: results[0] || results[5] || results[6] || results[7] || results[8]
    };
  }

  /**
   * 健康管理权限检查（优化版）
   */
  async health() {
    const userId = this.getCurrentUserId();
    
    const permissionChecks = [
      { permission: PERMISSIONS.HEALTH_VIEW_ALL },
      { permission: PERMISSIONS.HEALTH_VIEW_OWN },
      { permission: PERMISSIONS.HEALTH_CREATE },
      { permission: PERMISSIONS.HEALTH_EDIT },
      { permission: PERMISSIONS.HEALTH_DELETE },
      { permission: PERMISSIONS.HEALTH_DIAGNOSE }
    ];
    
    const results = await this.cacheManager.batchCheckPermissions(userId, permissionChecks);
    
    return {
      canViewAll: results[0],
      canViewOwn: results[1],
      canCreate: results[2],
      canEdit: results[3],
      canDelete: results[4],
      canDiagnose: results[5],
      
      // 便捷方法
      canView: results[0] || results[1],
      canManage: results[2] || results[3] || results[4],
      isHealthRole: results[0] || results[5]
    };
  }

  /**
   * 生产管理权限检查（优化版）
   */
  async production() {
    const userId = this.getCurrentUserId();
    
    const permissionChecks = [
      { permission: PERMISSIONS.PRODUCTION_VIEW_ALL },
      { permission: PERMISSIONS.PRODUCTION_VIEW_OWN },
      { permission: PERMISSIONS.PRODUCTION_CREATE },
      { permission: PERMISSIONS.PRODUCTION_EDIT },
      { permission: PERMISSIONS.PRODUCTION_DELETE },
      { permission: PERMISSIONS.PRODUCTION_REPORTS }
    ];
    
    const results = await this.cacheManager.batchCheckPermissions(userId, permissionChecks);
    
    return {
      canViewAll: results[0],
      canViewOwn: results[1],
      canCreate: results[2],
      canEdit: results[3],
      canDelete: results[4],
      canViewReports: results[5],
      
      // 便捷方法
      canView: results[0] || results[1],
      canManage: results[2] || results[3] || results[4]
    };
  }

  /**
   * 用户管理权限检查（优化版）
   */
  async user() {
    const userId = this.getCurrentUserId();
    
    const permissionChecks = [
      { permission: PERMISSIONS.USER_VIEW_ALL },
      { permission: PERMISSIONS.USER_CREATE },
      { permission: PERMISSIONS.USER_EDIT },
      { permission: PERMISSIONS.USER_DELETE },
      { permission: PERMISSIONS.USER_ASSIGN_ROLE }
    ];
    
    const results = await this.cacheManager.batchCheckPermissions(userId, permissionChecks);
    
    return {
      canViewAll: results[0],
      canCreate: results[1],
      canEdit: results[2],
      canDelete: results[3],
      canAssignRole: results[4],
      
      // 便捷方法
      canManage: results[1] || results[2] || results[3] || results[4],
      isUserAdmin: results[0] && results[4]
    };
  }

  /**
   * 系统管理权限检查（优化版）
   */
  async system() {
    const userId = this.getCurrentUserId();
    
    const permissionChecks = [
      { permission: PERMISSIONS.SYSTEM_CONFIG },
      { permission: PERMISSIONS.SYSTEM_BACKUP },
      { permission: PERMISSIONS.SYSTEM_LOGS },
      { permission: PERMISSIONS.SYSTEM_MONITOR }
    ];
    
    const results = await this.cacheManager.batchCheckPermissions(userId, permissionChecks);
    
    return {
      canConfig: results[0],
      canBackup: results[1],
      canViewLogs: results[2],
      canMonitor: results[3],
      
      // 便捷方法
      isSystemAdmin: results[0] || results[1] || results[2] || results[3]
    };
  }

  /**
   * 资源级权限检查
   * @param {string} permission 权限标识
   * @param {string} resourceId 资源ID
   * @param {string} resourceOwnerId 资源拥有者ID
   * @returns {Promise<boolean>}
   */
  async canAccessResource(permission, resourceId, resourceOwnerId) {
    const userId = this.getCurrentUserId();
    return await this.cacheManager.checkPermission(userId, permission, {
      resourceId,
      resourceOwnerId
    });
  }

  /**
   * 检查是否为管理员
   * @returns {Promise<boolean>}
   */
  async isAdmin() {
    const userPermissions = await this.cacheManager.getUserPermissions(this.getCurrentUserId());
    return userPermissions.isAdmin || false;
  }

  /**
   * 检查是否为特定角色
   * @param {string} role 角色
   * @returns {Promise<boolean>}
   */
  async hasRole(role) {
    const userPermissions = await this.cacheManager.getUserPermissions(this.getCurrentUserId());
    return userPermissions.role === role;
  }

  /**
   * 检查是否为任一角色
   * @param {Array} roles 角色数组
   * @returns {Promise<boolean>}
   */
  async hasAnyRole(roles) {
    const userPermissions = await this.cacheManager.getUserPermissions(this.getCurrentUserId());
    return roles.includes(userPermissions.role);
  }

  /**
   * 获取用户完整权限信息
   * @returns {Promise<Object>}
   */
  async getUserPermissions() {
    return await this.cacheManager.getUserPermissions(this.getCurrentUserId());
  }

  /**
   * 清除当前用户权限缓存
   */
  clearCache() {
    const userId = this.getCurrentUserId();
    this.cacheManager.clearUserPermissions(userId);
    this.cacheManager.clearPermissionChecks(userId);
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return this.cacheManager.getStats();
  }

  /**
   * 预加载权限（用于页面初始化）
   * @param {Array} permissions 需要预加载的权限数组
   */
  async preloadPermissions(permissions) {
    const userId = this.getCurrentUserId();
    const permissionChecks = permissions.map(permission => ({ permission }));
    await this.cacheManager.batchCheckPermissions(userId, permissionChecks);
  }

  /**
   * 获取当前用户ID
   * @private
   */
  getCurrentUserId() {
    return wx.getStorageSync('userId') || 'anonymous';
  }
}

// 创建全局实例
const permissionHelper = new PermissionHelper();

// 导出便捷方法
module.exports = {
  // 实例
  permissionHelper,
  
  // 便捷方法
  can: (permission, options) => permissionHelper.can(permission, options),
  canAny: (permissions, options) => permissionHelper.canAny(permissions, options),
  canAll: (permissions, options) => permissionHelper.canAll(permissions, options),
  
  // 模块权限
  finance: () => permissionHelper.finance(),
  health: () => permissionHelper.health(),
  production: () => permissionHelper.production(),
  user: () => permissionHelper.user(),
  system: () => permissionHelper.system(),
  
  // 角色检查
  isAdmin: () => permissionHelper.isAdmin(),
  hasRole: (role) => permissionHelper.hasRole(role),
  hasAnyRole: (roles) => permissionHelper.hasAnyRole(roles),
  
  // 资源权限
  canAccessResource: (permission, resourceId, resourceOwnerId) => 
    permissionHelper.canAccessResource(permission, resourceId, resourceOwnerId),
  
  // 缓存管理
  clearCache: () => permissionHelper.clearCache(),
  getCacheStats: () => permissionHelper.getCacheStats(),
  preloadPermissions: (permissions) => permissionHelper.preloadPermissions(permissions),
  
  // 用户权限
  getUserPermissions: () => permissionHelper.getUserPermissions()
};
