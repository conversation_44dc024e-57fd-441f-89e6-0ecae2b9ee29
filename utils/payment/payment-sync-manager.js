/**
 * 支付状态同步管理器
 * Payment Status Sync Manager
 */

const { apiClient } = require('../api-client-final.js');
const { wechatPayService } = require('./wechat-pay-service.js');

class PaymentSyncManager {
  constructor() {
    this.syncQueue = new Map(); // 同步队列
    this.syncInterval = 5000; // 5秒同步间隔
    this.maxRetries = 5; // 最大重试次数
    this.syncTimer = null;
    this.isRunning = false;
  }

  /**
   * 启动支付状态同步
   */
  start() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.syncTimer = setInterval(() => {
      this.processSyncQueue();
    }, this.syncInterval);

    console.log('支付状态同步管理器已启动');
  }

  /**
   * 停止支付状态同步
   */
  stop() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
    this.isRunning = false;
    console.log('支付状态同步管理器已停止');
  }

  /**
   * 添加支付订单到同步队列
   */
  addPaymentToSync(orderId, paymentData = {}) {
    const syncItem = {
      orderId: orderId,
      paymentData: paymentData,
      addedAt: Date.now(),
      retryCount: 0,
      lastSyncAt: null,
      status: 'pending' // pending, syncing, completed, failed
    };

    this.syncQueue.set(orderId, syncItem);
    console.log(`支付订单 ${orderId} 已添加到同步队列`);

    // 如果同步器未运行，启动它
    if (!this.isRunning) {
      this.start();
    }
  }

  /**
   * 从同步队列移除订单
   */
  removeFromSync(orderId) {
    if (this.syncQueue.has(orderId)) {
      this.syncQueue.delete(orderId);
      console.log(`支付订单 ${orderId} 已从同步队列移除`);
    }

    // 如果队列为空，停止同步器
    if (this.syncQueue.size === 0) {
      this.stop();
    }
  }

  /**
   * 处理同步队列
   */
  async processSyncQueue() {
    if (this.syncQueue.size === 0) {
      return;
    }

    console.log(`开始处理支付同步队列，待处理订单数: ${this.syncQueue.size}`);

    const promises = [];
    for (const [orderId, syncItem] of this.syncQueue.entries()) {
      if (syncItem.status === 'syncing') {
        continue; // 跳过正在同步的订单
      }

      promises.push(this.syncPaymentStatus(orderId, syncItem));
    }

    await Promise.allSettled(promises);
  }

  /**
   * 同步单个支付订单状态
   */
  async syncPaymentStatus(orderId, syncItem) {
    try {
      // 更新状态为同步中
      syncItem.status = 'syncing';
      syncItem.lastSyncAt = Date.now();

      // 查询支付状态
      const paymentStatus = await wechatPayService.queryPaymentStatus(orderId);
      
      // 根据支付状态处理
      await this.handlePaymentStatusUpdate(orderId, paymentStatus, syncItem);

    } catch (error) {
      console.error(`同步支付状态失败 (订单: ${orderId}):`, error);
      await this.handleSyncError(orderId, syncItem, error);
    }
  }

  /**
   * 处理支付状态更新
   */
  async handlePaymentStatusUpdate(orderId, paymentStatus, syncItem) {
    const { status, transactionId, paidAt } = paymentStatus;

    switch (status) {
      case 'SUCCESS':
      case 'PAID':
        // 支付成功
        await this.handlePaymentSuccess(orderId, paymentStatus, syncItem);
        break;

      case 'FAILED':
      case 'CLOSED':
        // 支付失败或关闭
        await this.handlePaymentFailure(orderId, paymentStatus, syncItem);
        break;

      case 'USERPAYING':
      case 'PENDING':
        // 支付中，继续等待
        await this.handlePaymentPending(orderId, paymentStatus, syncItem);
        break;

      case 'REFUND':
        // 已退款
        await this.handlePaymentRefund(orderId, paymentStatus, syncItem);
        break;

      default:
        console.warn(`未知的支付状态: ${status} (订单: ${orderId})`);
        syncItem.status = 'pending'; // 重置为待处理状态
    }
  }

  /**
   * 处理支付成功
   */
  async handlePaymentSuccess(orderId, paymentStatus, syncItem) {
    try {
      // 更新本地订单状态
      await this.updateLocalOrderStatus(orderId, 'paid', {
        transactionId: paymentStatus.transactionId,
        paidAt: paymentStatus.paidAt || new Date().toISOString(),
        paymentMethod: 'wechat',
        amount: paymentStatus.amount
      });

      // 触发支付成功事件
      this.triggerPaymentEvent('payment_success', {
        orderId: orderId,
        paymentStatus: paymentStatus
      });

      // 从同步队列移除
      syncItem.status = 'completed';
      this.removeFromSync(orderId);

      console.log(`支付成功处理完成 (订单: ${orderId})`);

    } catch (error) {
      console.error(`处理支付成功失败 (订单: ${orderId}):`, error);
      throw error;
    }
  }

  /**
   * 处理支付失败
   */
  async handlePaymentFailure(orderId, paymentStatus, syncItem) {
    try {
      // 更新本地订单状态
      await this.updateLocalOrderStatus(orderId, 'payment_failed', {
        failureReason: paymentStatus.failureReason || '支付失败',
        failedAt: new Date().toISOString()
      });

      // 触发支付失败事件
      this.triggerPaymentEvent('payment_failed', {
        orderId: orderId,
        paymentStatus: paymentStatus
      });

      // 从同步队列移除
      syncItem.status = 'failed';
      this.removeFromSync(orderId);

      console.log(`支付失败处理完成 (订单: ${orderId})`);

    } catch (error) {
      console.error(`处理支付失败失败 (订单: ${orderId}):`, error);
      throw error;
    }
  }

  /**
   * 处理支付中状态
   */
  async handlePaymentPending(orderId, paymentStatus, syncItem) {
    // 检查是否超时
    const timeoutMinutes = 30; // 30分钟超时
    const timeoutMs = timeoutMinutes * 60 * 1000;
    
    if (Date.now() - syncItem.addedAt > timeoutMs) {
      console.warn(`支付超时 (订单: ${orderId})`);
      await this.handlePaymentTimeout(orderId, syncItem);
      return;
    }

    // 重置状态为待处理，继续等待
    syncItem.status = 'pending';
    console.log(`支付仍在处理中 (订单: ${orderId})`);
  }

  /**
   * 处理支付退款
   */
  async handlePaymentRefund(orderId, paymentStatus, syncItem) {
    try {
      // 更新本地订单状态
      await this.updateLocalOrderStatus(orderId, 'refunded', {
        refundId: paymentStatus.refundId,
        refundAmount: paymentStatus.refundAmount,
        refundedAt: paymentStatus.refundedAt || new Date().toISOString()
      });

      // 触发退款事件
      this.triggerPaymentEvent('payment_refunded', {
        orderId: orderId,
        paymentStatus: paymentStatus
      });

      // 从同步队列移除
      syncItem.status = 'completed';
      this.removeFromSync(orderId);

      console.log(`退款处理完成 (订单: ${orderId})`);

    } catch (error) {
      console.error(`处理退款失败 (订单: ${orderId}):`, error);
      throw error;
    }
  }

  /**
   * 处理支付超时
   */
  async handlePaymentTimeout(orderId, syncItem) {
    try {
      // 更新本地订单状态
      await this.updateLocalOrderStatus(orderId, 'payment_timeout', {
        timeoutAt: new Date().toISOString(),
        timeoutReason: '支付超时'
      });

      // 触发超时事件
      this.triggerPaymentEvent('payment_timeout', {
        orderId: orderId
      });

      // 从同步队列移除
      syncItem.status = 'failed';
      this.removeFromSync(orderId);

      console.log(`支付超时处理完成 (订单: ${orderId})`);

    } catch (error) {
      console.error(`处理支付超时失败 (订单: ${orderId}):`, error);
      throw error;
    }
  }

  /**
   * 处理同步错误
   */
  async handleSyncError(orderId, syncItem, error) {
    syncItem.retryCount++;
    syncItem.status = 'pending';

    if (syncItem.retryCount >= this.maxRetries) {
      console.error(`支付同步达到最大重试次数 (订单: ${orderId})`);
      
      // 触发同步失败事件
      this.triggerPaymentEvent('sync_failed', {
        orderId: orderId,
        error: error.message
      });

      // 从同步队列移除
      syncItem.status = 'failed';
      this.removeFromSync(orderId);
    } else {
      console.log(`支付同步将重试 (订单: ${orderId}, 重试次数: ${syncItem.retryCount})`);
    }
  }

  /**
   * 更新本地订单状态
   */
  async updateLocalOrderStatus(orderId, status, metadata = {}) {
    try {
      const response = await apiClient.put(`/api/v2/orders/${orderId}/status`, {
        status: status,
        metadata: metadata,
        updatedAt: new Date().toISOString()
      });

      if (!response.success) {
        throw new Error(response.message || '更新订单状态失败');
      }

      return response.data;
    } catch (error) {
      console.error('更新本地订单状态失败:', error);
      throw error;
    }
  }

  /**
   * 触发支付事件
   */
  triggerPaymentEvent(eventType, eventData) {
    const event = new CustomEvent(`payment_${eventType}`, {
      detail: eventData,
      bubbles: true,
      cancelable: true
    });

    // 在小程序环境中，可以通过全局事件总线分发事件
    if (typeof getApp === 'function') {
      const app = getApp();
      if (app.globalData && app.globalData.eventBus) {
        app.globalData.eventBus.emit(`payment_${eventType}`, eventData);
      }
    }

    // 也可以直接在document上分发事件（如果在web环境中）
    if (typeof document !== 'undefined') {
      document.dispatchEvent(event);
    }

    console.log(`支付事件已触发: ${eventType}`, eventData);
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    const queueItems = Array.from(this.syncQueue.values());
    
    return {
      isRunning: this.isRunning,
      queueSize: this.syncQueue.size,
      pendingCount: queueItems.filter(item => item.status === 'pending').length,
      syncingCount: queueItems.filter(item => item.status === 'syncing').length,
      completedCount: queueItems.filter(item => item.status === 'completed').length,
      failedCount: queueItems.filter(item => item.status === 'failed').length,
      syncInterval: this.syncInterval,
      maxRetries: this.maxRetries
    };
  }

  /**
   * 手动触发同步
   */
  async manualSync(orderId = null) {
    if (orderId) {
      // 同步指定订单
      const syncItem = this.syncQueue.get(orderId);
      if (syncItem) {
        await this.syncPaymentStatus(orderId, syncItem);
      } else {
        throw new Error(`订单 ${orderId} 不在同步队列中`);
      }
    } else {
      // 同步所有订单
      await this.processSyncQueue();
    }
  }
}

// 创建全局实例
const paymentSyncManager = new PaymentSyncManager();

module.exports = {
  PaymentSyncManager,
  paymentSyncManager
};
