/**
 * 支付系统统一导出文件
 * Payment System Unified Export
 */

const { wechatPayService } = require('./wechat-pay-service.js');
const { paymentSyncManager } = require('./payment-sync-manager.js');
const { paymentErrorHandler } = require('./payment-error-handler.js');

/**
 * 统一支付管理器
 */
class PaymentManager {
  constructor() {
    this.wechatPay = wechatPayService;
    this.syncManager = paymentSyncManager;
    this.errorHandler = paymentErrorHandler;
    this.isInitialized = false;
  }

  /**
   * 初始化支付系统
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('正在初始化支付系统...');

      // 启动支付状态同步管理器
      this.syncManager.start();

      // 绑定全局错误处理
      this.bindGlobalErrorHandlers();

      this.isInitialized = true;
      console.log('支付系统初始化完成');

    } catch (error) {
      console.error('支付系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 发起支付
   */
  async pay(orderData) {
    try {
      // 确保系统已初始化
      if (!this.isInitialized) {
        await this.initialize();
      }

      // 验证订单数据
      this.validateOrderData(orderData);

      // 添加到同步队列
      this.syncManager.addPaymentToSync(orderData.orderId, orderData);

      // 发起微信支付
      const paymentResult = await this.wechatPay.processPayment(orderData);

      return {
        success: true,
        data: paymentResult,
        message: '支付成功'
      };

    } catch (error) {
      console.error('支付失败:', error);
      
      // 使用错误处理器处理错误
      const errorResult = this.errorHandler.handlePaymentError(error, {
        orderId: orderData.orderId,
        amount: orderData.amount,
        action: 'pay'
      });

      return errorResult;
    }
  }

  /**
   * 查询支付状态
   */
  async queryPaymentStatus(orderId) {
    try {
      const status = await this.wechatPay.queryPaymentStatus(orderId);
      return {
        success: true,
        data: status,
        message: '查询成功'
      };
    } catch (error) {
      console.error('查询支付状态失败:', error);
      
      const errorResult = this.errorHandler.handlePaymentError(error, {
        orderId: orderId,
        action: 'query_status'
      });

      return errorResult;
    }
  }

  /**
   * 申请退款
   */
  async refund(refundData) {
    try {
      const refundResult = await this.wechatPay.requestRefund(refundData);
      
      // 添加退款订单到同步队列
      this.syncManager.addPaymentToSync(refundData.orderId, {
        ...refundData,
        type: 'refund'
      });

      return {
        success: true,
        data: refundResult,
        message: '退款申请成功'
      };

    } catch (error) {
      console.error('申请退款失败:', error);
      
      const errorResult = this.errorHandler.handlePaymentError(error, {
        orderId: refundData.orderId,
        refundAmount: refundData.refundAmount,
        action: 'refund'
      });

      return errorResult;
    }
  }

  /**
   * 验证订单数据
   */
  validateOrderData(orderData) {
    const requiredFields = ['orderId', 'amount', 'description'];
    
    for (const field of requiredFields) {
      if (!orderData[field]) {
        throw new Error(`缺少必要的订单字段: ${field}`);
      }
    }

    // 验证金额
    if (typeof orderData.amount !== 'number' || orderData.amount <= 0) {
      throw new Error('订单金额必须大于0');
    }

    // 验证订单ID格式
    if (!/^[A-Za-z0-9_-]+$/.test(orderData.orderId)) {
      throw new Error('订单ID格式不正确');
    }
  }

  /**
   * 绑定全局错误处理
   */
  bindGlobalErrorHandlers() {
    // 监听支付相关事件
    if (typeof getApp === 'function') {
      const app = getApp();
      if (app.globalData && !app.globalData.eventBus) {
        app.globalData.eventBus = {
          listeners: {},
          emit: function(event, data) {
            if (this.listeners[event]) {
              this.listeners[event].forEach(callback => callback(data));
            }
          },
          on: function(event, callback) {
            if (!this.listeners[event]) {
              this.listeners[event] = [];
            }
            this.listeners[event].push(callback);
          }
        };
      }

      // 监听支付事件
      app.globalData.eventBus.on('payment_success', (data) => {
        console.log('支付成功事件:', data);
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });
      });

      app.globalData.eventBus.on('payment_failed', (data) => {
        console.log('支付失败事件:', data);
        wx.showToast({
          title: '支付失败',
          icon: 'error'
        });
      });

      app.globalData.eventBus.on('payment_timeout', (data) => {
        console.log('支付超时事件:', data);
        wx.showModal({
          title: '支付超时',
          content: '支付超时，请重新发起支付',
          showCancel: false
        });
      });
    }
  }

  /**
   * 获取支付系统状态
   */
  getSystemStatus() {
    return {
      initialized: this.isInitialized,
      wechatPay: this.wechatPay.getPaymentStats(),
      syncManager: this.syncManager.getSyncStatus(),
      errorStats: this.errorHandler.getErrorStats()
    };
  }

  /**
   * 销毁支付系统
   */
  destroy() {
    if (this.syncManager) {
      this.syncManager.stop();
    }
    
    if (this.errorHandler) {
      this.errorHandler.clearErrorLog();
    }

    this.isInitialized = false;
    console.log('支付系统已销毁');
  }
}

// 创建全局实例
const paymentManager = new PaymentManager();

// 便捷方法
const PaymentAPI = {
  // 发起支付
  pay: (orderData) => paymentManager.pay(orderData),
  
  // 查询状态
  queryStatus: (orderId) => paymentManager.queryPaymentStatus(orderId),
  
  // 申请退款
  refund: (refundData) => paymentManager.refund(refundData),
  
  // 初始化
  init: () => paymentManager.initialize(),
  
  // 获取状态
  getStatus: () => paymentManager.getSystemStatus(),
  
  // 销毁
  destroy: () => paymentManager.destroy()
};

// 主要导出
module.exports = {
  // 核心类
  PaymentManager,
  
  // 全局实例
  paymentManager,
  
  // 子模块
  wechatPayService,
  paymentSyncManager,
  paymentErrorHandler,
  
  // 便捷API
  PaymentAPI,
  
  // 便捷方法
  ...PaymentAPI,
  
  // 重构信息
  __refactored: true,
  __version: '2.0',
  __modules: [
    'utils/payment/wechat-pay-service.js',
    'utils/payment/payment-sync-manager.js',
    'utils/payment/payment-error-handler.js'
  ]
};
