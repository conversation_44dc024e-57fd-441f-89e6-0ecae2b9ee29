/**
 * 微信支付服务模块
 * WeChat Pay Service Module
 */

const { apiClient } = require('../api-client-final.js');
const { THIRD_PARTY_CONFIG } = require('../../constants/config.constants.js');

class WechatPayService {
  constructor() {
    this.config = THIRD_PARTY_CONFIG.PAYMENT.WECHAT_PAY;
    this.isEnabled = this.config.ENABLED;
    this.paymentQueue = new Map(); // 支付请求队列
    this.retryConfig = { maxRetries: 3, retryDelay: 2000 };
  }

  /**
   * 检查微信支付是否可用
   */
  isAvailable() {
    return this.isEnabled && typeof wx !== 'undefined' && wx.requestPayment;
  }

  /**
   * 创建支付订单
   */
  async createPaymentOrder(orderData) {
    if (!this.isAvailable()) {
      throw new Error('微信支付不可用');
    }

    try {
      // 调用后端API创建支付订单
      const response = await apiClient.post('/api/v2/payment/wechat/create-order', {
        orderId: orderData.orderId,
        amount: orderData.amount,
        description: orderData.description || '智慧养鹅云服务',
        userId: orderData.userId,
        openid: orderData.openid || await this.getOpenId(),
        notifyUrl: orderData.notifyUrl || '/api/v2/payment/wechat/notify',
        metadata: orderData.metadata || {}
      });

      if (!response.success) {
        throw new Error(response.message || '创建支付订单失败');
      }

      return response.data;
    } catch (error) {
      console.error('创建微信支付订单失败:', error);
      throw error;
    }
  }

  /**
   * 发起微信支付
   */
  async requestPayment(paymentParams) {
    return new Promise((resolve, reject) => {
      // 验证支付参数
      if (!this.validatePaymentParams(paymentParams)) {
        reject(new Error('支付参数验证失败'));
        return;
      }

      // 调用微信支付API
      wx.requestPayment({
        timeStamp: paymentParams.timeStamp,
        nonceStr: paymentParams.nonceStr,
        package: paymentParams.package,
        signType: paymentParams.signType || 'RSA',
        paySign: paymentParams.paySign,
        success: (res) => {
          console.log('微信支付成功:', res);
          resolve({
            success: true,
            data: res,
            message: '支付成功'
          });
        },
        fail: (err) => {
          console.error('微信支付失败:', err);
          const errorMessage = this.parsePaymentError(err);
          reject(new Error(errorMessage));
        }
      });
    });
  }

  /**
   * 完整的支付流程
   */
  async processPayment(orderData) {
    const paymentId = this.generatePaymentId();
    
    try {
      // 防重复支付检查
      if (this.paymentQueue.has(orderData.orderId)) {
        throw new Error('支付正在处理中，请勿重复操作');
      }

      this.paymentQueue.set(orderData.orderId, paymentId);

      // 1. 创建支付订单
      const paymentOrder = await this.createPaymentOrder(orderData);
      
      // 2. 发起微信支付
      const paymentResult = await this.requestPayment(paymentOrder.paymentParams);
      
      // 3. 验证支付结果
      const verificationResult = await this.verifyPaymentResult(paymentOrder.orderId);
      
      // 4. 更新订单状态
      await this.updateOrderStatus(orderData.orderId, 'paid', {
        paymentId: paymentOrder.paymentId,
        transactionId: verificationResult.transactionId,
        paidAt: new Date().toISOString()
      });

      return {
        success: true,
        orderId: orderData.orderId,
        paymentId: paymentOrder.paymentId,
        transactionId: verificationResult.transactionId,
        message: '支付成功'
      };

    } catch (error) {
      console.error('支付流程失败:', error);
      
      // 更新订单状态为支付失败
      try {
        await this.updateOrderStatus(orderData.orderId, 'payment_failed', {
          error: error.message,
          failedAt: new Date().toISOString()
        });
      } catch (updateError) {
        console.error('更新订单状态失败:', updateError);
      }

      throw error;
    } finally {
      // 清理支付队列
      this.paymentQueue.delete(orderData.orderId);
    }
  }

  /**
   * 验证支付参数
   */
  validatePaymentParams(params) {
    const requiredFields = ['timeStamp', 'nonceStr', 'package', 'paySign'];
    
    for (const field of requiredFields) {
      if (!params[field]) {
        console.error(`缺少必要的支付参数: ${field}`);
        return false;
      }
    }

    // 验证时间戳格式
    if (!/^\d{10}$/.test(params.timeStamp)) {
      console.error('时间戳格式错误');
      return false;
    }

    // 验证随机字符串长度
    if (params.nonceStr.length < 16 || params.nonceStr.length > 32) {
      console.error('随机字符串长度错误');
      return false;
    }

    return true;
  }

  /**
   * 解析支付错误
   */
  parsePaymentError(error) {
    const errorMap = {
      'requestPayment:fail cancel': '用户取消支付',
      'requestPayment:fail (payment canceled by user)': '用户取消支付',
      'requestPayment:fail invalid_request': '支付参数错误',
      'requestPayment:fail system_error': '系统错误，请重试',
      'requestPayment:fail network_error': '网络错误，请检查网络连接',
      'requestPayment:fail insufficient_balance': '余额不足',
      'requestPayment:fail payment_timeout': '支付超时'
    };

    return errorMap[error.errMsg] || error.errMsg || '支付失败，请重试';
  }

  /**
   * 验证支付结果
   */
  async verifyPaymentResult(orderId) {
    try {
      const response = await apiClient.get(`/api/v2/payment/wechat/verify/${orderId}`);
      
      if (!response.success) {
        throw new Error(response.message || '支付验证失败');
      }

      return response.data;
    } catch (error) {
      console.error('支付结果验证失败:', error);
      throw error;
    }
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(orderId, status, metadata = {}) {
    try {
      const response = await apiClient.put(`/api/v2/orders/${orderId}/status`, {
        status: status,
        metadata: metadata,
        updatedAt: new Date().toISOString()
      });

      if (!response.success) {
        throw new Error(response.message || '更新订单状态失败');
      }

      return response.data;
    } catch (error) {
      console.error('更新订单状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户OpenID
   */
  async getOpenId() {
    try {
      // 从本地存储获取
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && userInfo.openid) {
        return userInfo.openid;
      }

      // 调用API获取
      const response = await apiClient.get('/api/v2/auth/openid');
      if (response.success && response.data.openid) {
        return response.data.openid;
      }

      throw new Error('无法获取用户OpenID');
    } catch (error) {
      console.error('获取OpenID失败:', error);
      throw error;
    }
  }

  /**
   * 生成支付ID
   */
  generatePaymentId() {
    return 'PAY_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 查询支付状态
   */
  async queryPaymentStatus(orderId) {
    try {
      const response = await apiClient.get(`/api/v2/payment/wechat/status/${orderId}`);
      
      if (!response.success) {
        throw new Error(response.message || '查询支付状态失败');
      }

      return response.data;
    } catch (error) {
      console.error('查询支付状态失败:', error);
      throw error;
    }
  }

  /**
   * 申请退款
   */
  async requestRefund(refundData) {
    try {
      const response = await apiClient.post('/api/v2/payment/wechat/refund', {
        orderId: refundData.orderId,
        refundAmount: refundData.refundAmount,
        refundReason: refundData.refundReason || '用户申请退款',
        notifyUrl: refundData.notifyUrl || '/api/v2/payment/wechat/refund-notify'
      });

      if (!response.success) {
        throw new Error(response.message || '申请退款失败');
      }

      return response.data;
    } catch (error) {
      console.error('申请退款失败:', error);
      throw error;
    }
  }

  /**
   * 获取支付统计
   */
  getPaymentStats() {
    return {
      totalPayments: this.paymentQueue.size,
      isEnabled: this.isEnabled,
      isAvailable: this.isAvailable(),
      config: {
        enabled: this.config.ENABLED,
        hasAppId: !!this.config.APP_ID,
        hasMchId: !!this.config.MCH_ID,
        hasApiKey: !!this.config.API_KEY
      }
    };
  }
}

// 创建全局实例
const wechatPayService = new WechatPayService();

module.exports = {
  WechatPayService,
  wechatPayService
};
