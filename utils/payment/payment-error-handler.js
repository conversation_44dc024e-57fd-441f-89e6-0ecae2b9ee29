/**
 * 支付异常处理模块
 * Payment Error Handler Module
 */

class PaymentErrorHandler {
  constructor() {
    this.errorTypes = {
      NETWORK_ERROR: 'network_error',
      PARAMETER_ERROR: 'parameter_error',
      PERMISSION_ERROR: 'permission_error',
      BUSINESS_ERROR: 'business_error',
      SYSTEM_ERROR: 'system_error',
      USER_CANCEL: 'user_cancel',
      TIMEOUT_ERROR: 'timeout_error',
      INSUFFICIENT_BALANCE: 'insufficient_balance',
      DUPLICATE_ORDER: 'duplicate_order',
      ORDER_NOT_FOUND: 'order_not_found'
    };

    this.errorMessages = {
      [this.errorTypes.NETWORK_ERROR]: '网络连接异常，请检查网络后重试',
      [this.errorTypes.PARAMETER_ERROR]: '支付参数错误，请重新发起支付',
      [this.errorTypes.PERMISSION_ERROR]: '支付权限不足，请联系客服',
      [this.errorTypes.BUSINESS_ERROR]: '业务处理异常，请稍后重试',
      [this.errorTypes.SYSTEM_ERROR]: '系统异常，请稍后重试',
      [this.errorTypes.USER_CANCEL]: '用户取消支付',
      [this.errorTypes.TIMEOUT_ERROR]: '支付超时，请重新发起支付',
      [this.errorTypes.INSUFFICIENT_BALANCE]: '账户余额不足',
      [this.errorTypes.DUPLICATE_ORDER]: '订单重复，请勿重复支付',
      [this.errorTypes.ORDER_NOT_FOUND]: '订单不存在'
    };

    this.retryableErrors = [
      this.errorTypes.NETWORK_ERROR,
      this.errorTypes.SYSTEM_ERROR,
      this.errorTypes.TIMEOUT_ERROR
    ];

    this.errorLog = [];
    this.maxLogSize = 100;
  }

  /**
   * 处理支付错误
   */
  handlePaymentError(error, context = {}) {
    const errorInfo = this.analyzeError(error, context);
    
    // 记录错误日志
    this.logError(errorInfo);
    
    // 根据错误类型处理
    return this.processError(errorInfo);
  }

  /**
   * 分析错误类型
   */
  analyzeError(error, context) {
    const errorInfo = {
      originalError: error,
      context: context,
      timestamp: new Date().toISOString(),
      errorType: this.errorTypes.SYSTEM_ERROR,
      errorMessage: '未知错误',
      isRetryable: false,
      suggestedAction: 'contact_support'
    };

    // 根据错误信息判断错误类型
    if (error.message || error.errMsg) {
      const errorMsg = (error.message || error.errMsg).toLowerCase();
      
      if (errorMsg.includes('cancel') || errorMsg.includes('用户取消')) {
        errorInfo.errorType = this.errorTypes.USER_CANCEL;
        errorInfo.suggestedAction = 'user_cancelled';
      } else if (errorMsg.includes('network') || errorMsg.includes('网络')) {
        errorInfo.errorType = this.errorTypes.NETWORK_ERROR;
        errorInfo.suggestedAction = 'retry_payment';
      } else if (errorMsg.includes('timeout') || errorMsg.includes('超时')) {
        errorInfo.errorType = this.errorTypes.TIMEOUT_ERROR;
        errorInfo.suggestedAction = 'retry_payment';
      } else if (errorMsg.includes('parameter') || errorMsg.includes('参数')) {
        errorInfo.errorType = this.errorTypes.PARAMETER_ERROR;
        errorInfo.suggestedAction = 'recreate_order';
      } else if (errorMsg.includes('balance') || errorMsg.includes('余额')) {
        errorInfo.errorType = this.errorTypes.INSUFFICIENT_BALANCE;
        errorInfo.suggestedAction = 'check_balance';
      } else if (errorMsg.includes('duplicate') || errorMsg.includes('重复')) {
        errorInfo.errorType = this.errorTypes.DUPLICATE_ORDER;
        errorInfo.suggestedAction = 'check_order_status';
      } else if (errorMsg.includes('not found') || errorMsg.includes('不存在')) {
        errorInfo.errorType = this.errorTypes.ORDER_NOT_FOUND;
        errorInfo.suggestedAction = 'recreate_order';
      } else if (errorMsg.includes('permission') || errorMsg.includes('权限')) {
        errorInfo.errorType = this.errorTypes.PERMISSION_ERROR;
        errorInfo.suggestedAction = 'contact_support';
      }
    }

    // 设置错误消息
    errorInfo.errorMessage = this.errorMessages[errorInfo.errorType] || error.message || '支付异常';
    
    // 设置是否可重试
    errorInfo.isRetryable = this.retryableErrors.includes(errorInfo.errorType);

    return errorInfo;
  }

  /**
   * 处理错误
   */
  processError(errorInfo) {
    const result = {
      success: false,
      errorType: errorInfo.errorType,
      errorMessage: errorInfo.errorMessage,
      isRetryable: errorInfo.isRetryable,
      suggestedAction: errorInfo.suggestedAction,
      timestamp: errorInfo.timestamp
    };

    // 根据建议的操作类型处理
    switch (errorInfo.suggestedAction) {
      case 'user_cancelled':
        result.userAction = {
          type: 'info',
          title: '支付已取消',
          message: '您已取消支付，如需继续请重新发起支付',
          buttons: [
            { text: '重新支付', action: 'retry_payment' },
            { text: '返回', action: 'go_back' }
          ]
        };
        break;

      case 'retry_payment':
        result.userAction = {
          type: 'error',
          title: '支付失败',
          message: errorInfo.errorMessage,
          buttons: [
            { text: '重试', action: 'retry_payment' },
            { text: '取消', action: 'cancel' }
          ]
        };
        break;

      case 'recreate_order':
        result.userAction = {
          type: 'error',
          title: '订单异常',
          message: errorInfo.errorMessage,
          buttons: [
            { text: '重新下单', action: 'recreate_order' },
            { text: '返回', action: 'go_back' }
          ]
        };
        break;

      case 'check_balance':
        result.userAction = {
          type: 'warning',
          title: '余额不足',
          message: errorInfo.errorMessage,
          buttons: [
            { text: '充值', action: 'recharge' },
            { text: '返回', action: 'go_back' }
          ]
        };
        break;

      case 'check_order_status':
        result.userAction = {
          type: 'info',
          title: '订单状态异常',
          message: errorInfo.errorMessage,
          buttons: [
            { text: '查看订单', action: 'view_order' },
            { text: '返回', action: 'go_back' }
          ]
        };
        break;

      case 'contact_support':
        result.userAction = {
          type: 'error',
          title: '支付异常',
          message: errorInfo.errorMessage,
          buttons: [
            { text: '联系客服', action: 'contact_support' },
            { text: '返回', action: 'go_back' }
          ]
        };
        break;

      default:
        result.userAction = {
          type: 'error',
          title: '支付失败',
          message: errorInfo.errorMessage,
          buttons: [
            { text: '确定', action: 'confirm' }
          ]
        };
    }

    return result;
  }

  /**
   * 记录错误日志
   */
  logError(errorInfo) {
    // 添加到错误日志
    this.errorLog.unshift(errorInfo);
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // 输出到控制台
    console.error('支付错误:', {
      type: errorInfo.errorType,
      message: errorInfo.errorMessage,
      context: errorInfo.context,
      timestamp: errorInfo.timestamp
    });

    // 上报错误到监控系统（如果有的话）
    this.reportError(errorInfo);
  }

  /**
   * 上报错误到监控系统
   */
  async reportError(errorInfo) {
    try {
      // 这里可以集成第三方错误监控服务
      // 比如 Sentry, Bugsnag 等
      
      // 示例：上报到自定义监控接口
      if (typeof wx !== 'undefined' && wx.request) {
        wx.request({
          url: '/api/v2/monitoring/payment-errors',
          method: 'POST',
          data: {
            errorType: errorInfo.errorType,
            errorMessage: errorInfo.errorMessage,
            context: errorInfo.context,
            timestamp: errorInfo.timestamp,
            userAgent: wx.getSystemInfoSync(),
            appVersion: wx.getAccountInfoSync()?.miniProgram?.version
          },
          fail: (err) => {
            console.warn('上报支付错误失败:', err);
          }
        });
      }
    } catch (error) {
      console.warn('上报支付错误异常:', error);
    }
  }

  /**
   * 显示错误提示
   */
  showErrorDialog(errorResult) {
    const { userAction } = errorResult;
    
    if (!userAction) {
      wx.showToast({
        title: errorResult.errorMessage,
        icon: 'none',
        duration: 3000
      });
      return Promise.resolve('toast_shown');
    }

    return new Promise((resolve) => {
      const buttons = userAction.buttons || [{ text: '确定', action: 'confirm' }];
      
      if (buttons.length === 1) {
        // 单按钮提示
        wx.showModal({
          title: userAction.title,
          content: userAction.message,
          showCancel: false,
          confirmText: buttons[0].text,
          success: (res) => {
            resolve(buttons[0].action);
          }
        });
      } else {
        // 双按钮提示
        wx.showModal({
          title: userAction.title,
          content: userAction.message,
          cancelText: buttons[1].text,
          confirmText: buttons[0].text,
          success: (res) => {
            if (res.confirm) {
              resolve(buttons[0].action);
            } else {
              resolve(buttons[1].action);
            }
          }
        });
      }
    });
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      totalErrors: this.errorLog.length,
      errorsByType: {},
      recentErrors: this.errorLog.slice(0, 10),
      errorTrends: this.calculateErrorTrends()
    };

    // 按类型统计错误
    this.errorLog.forEach(error => {
      const type = error.errorType;
      stats.errorsByType[type] = (stats.errorsByType[type] || 0) + 1;
    });

    return stats;
  }

  /**
   * 计算错误趋势
   */
  calculateErrorTrends() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const oneDay = 24 * oneHour;

    const trends = {
      lastHour: 0,
      lastDay: 0,
      lastWeek: 0
    };

    this.errorLog.forEach(error => {
      const errorTime = new Date(error.timestamp).getTime();
      const timeDiff = now - errorTime;

      if (timeDiff <= oneHour) {
        trends.lastHour++;
      }
      if (timeDiff <= oneDay) {
        trends.lastDay++;
      }
      if (timeDiff <= 7 * oneDay) {
        trends.lastWeek++;
      }
    });

    return trends;
  }

  /**
   * 清理错误日志
   */
  clearErrorLog() {
    this.errorLog = [];
    console.log('支付错误日志已清理');
  }
}

// 创建全局实例
const paymentErrorHandler = new PaymentErrorHandler();

module.exports = {
  PaymentErrorHandler,
  paymentErrorHandler
};
