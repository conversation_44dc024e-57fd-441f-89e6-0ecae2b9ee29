/**
 * 统一API客户端 - 最终版本
 * 整合所有API调用逻辑，支持云函数和HTTP双模式
 * 基于《具体代码修改建议.md》的设计方案
 */

// 初始化微信小程序polyfills
require('./miniprogram-polyfills.js');

const { environmentConfig } = require('./environment-config.js');
const ErrorHandler = require('./error-handler.js');

class FinalAPIClient {
  constructor() {
    this.version = 'v2';
    this.baseUrl = this.getEnvironmentBaseUrl();
    this.cloudEnabled = this.checkCloudAvailability();
    this.requestQueue = new Map();
    this.retryConfig = { maxRetries: 3, retryDelay: 1000 };
    this.stats = {
      totalRequests: 0,
      successRequests: 0,
      failedRequests: 0,
      cloudRequests: 0,
      httpRequests: 0
    };
  }

  /**
   * 获取环境基础URL
   */
  getEnvironmentBaseUrl() {
    try {
      if (typeof environmentConfig !== 'undefined' && environmentConfig.getApiBaseUrl) {
        return environmentConfig.getApiBaseUrl();
      }
    } catch (error) {
      console.warn('环境配置获取失败，使用默认配置:', error);
    }
    
    // 回退到全局配置
    const app = getApp();
    return app?.globalData?.baseUrl || 'http://localhost:3000';
  }

  /**
   * 检查云函数可用性
   */
  checkCloudAvailability() {
    try {
      const app = getApp();
      return app?.globalData?.isCloudEnabled && typeof wx !== 'undefined' && wx.cloud;
    } catch (error) {
      console.warn('云函数检查失败:', error);
      return false;
    }
  }

  /**
   * 生成请求ID
   */
  generateRequestId(endpoint, options = {}) {
    const key = `${options.method || 'GET'}_${endpoint}_${JSON.stringify(options.data || {})}`;
    // 使用polyfills提供的btoa函数
    return btoa(key).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }

  /**
   * 统一请求方法
   */
  async request(endpoint, options = {}) {
    const requestId = this.generateRequestId(endpoint, options);
    
    // 请求去重
    if (this.requestQueue.has(requestId)) {
      console.log('复用待处理请求:', endpoint);
      return this.requestQueue.get(requestId);
    }

    const requestPromise = this.executeRequest(endpoint, options);
    this.requestQueue.set(requestId, requestPromise);
    
    try {
      const result = await requestPromise;
      this.stats.totalRequests++;
      this.stats.successRequests++;
      return result;
    } catch (error) {
      this.stats.totalRequests++;
      this.stats.failedRequests++;
      throw error;
    } finally {
      this.requestQueue.delete(requestId);
    }
  }

  /**
   * 执行请求 - 智能路由
   */
  async executeRequest(endpoint, options) {
    // 优先使用云函数
    if (this.cloudEnabled && this.isCloudEndpoint(endpoint)) {
      try {
        console.log('使用云函数调用:', endpoint);
        const result = await this.callCloudFunction(endpoint, options);
        this.stats.cloudRequests++;
        return result;
      } catch (cloudError) {
        console.warn('云函数调用失败，回退到HTTP:', cloudError);
      }
    }
    
    // 回退到HTTP请求
    console.log('使用HTTP请求:', endpoint);
    const result = await this.callHttpAPI(endpoint, options);
    this.stats.httpRequests++;
    return result;
  }

  /**
   * 判断是否为云函数端点
   */
  isCloudEndpoint(endpoint) {
    const cloudEndpoints = [
      '/api/v2/auth/',
      '/api/v2/health/',
      '/api/v2/production/',
      '/api/v2/profile/'
    ];
    return cloudEndpoints.some(pattern => endpoint.includes(pattern));
  }

  /**
   * 云函数调用
   */
  async callCloudFunction(endpoint, options) {
    const { functionName, action } = this.mapEndpointToCloudFunction(endpoint);
    
    return await wx.cloud.callFunction({
      name: functionName,
      data: {
        action,
        ...options.data,
        _requestId: this.generateRequestId(),
        _timestamp: Date.now()
      }
    });
  }

  /**
   * 映射端点到云函数
   */
  mapEndpointToCloudFunction(endpoint) {
    const mappings = {
      '/api/v2/auth/': { functionName: 'auth', action: 'handle' },
      '/api/v2/health/': { functionName: 'health', action: 'handle' },
      '/api/v2/production/': { functionName: 'production', action: 'handle' },
      '/api/v2/profile/': { functionName: 'profile', action: 'handle' }
    };

    for (const [pattern, config] of Object.entries(mappings)) {
      if (endpoint.includes(pattern)) {
        return config;
      }
    }

    return { functionName: 'default', action: 'handle' };
  }

  /**
   * HTTP API调用
   */
  async callHttpAPI(endpoint, options) {
    const requestConfig = {
      url: `${this.baseUrl}${endpoint}`,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': this.getAuthToken(),
        'X-Tenant-ID': this.getTenantId(),
        'X-Request-ID': this.generateRequestId(),
        ...options.headers
      },
      timeout: options.timeout || 10000
    };

    return new Promise((resolve, reject) => {
      wx.request({
        ...requestConfig,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(this.formatResponse(res.data));
          } else {
            reject(this.createError(res));
          }
        },
        fail: (error) => {
          reject(this.createError(error));
        }
      });
    });
  }

  /**
   * 获取认证Token
   */
  getAuthToken() {
    try {
      return wx.getStorageSync('token') || '';
    } catch (error) {
      console.warn('获取Token失败:', error);
      return '';
    }
  }

  /**
   * 获取租户ID
   */
  getTenantId() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      return userInfo?.tenantId || '';
    } catch (error) {
      console.warn('获取租户ID失败:', error);
      return '';
    }
  }

  /**
   * 格式化响应
   */
  formatResponse(data) {
    return {
      success: true,
      data: data.data || data,
      message: data.message || '请求成功',
      code: data.code || 0,
      timestamp: Date.now()
    };
  }

  /**
   * 创建错误对象
   */
  createError(error) {
    const apiError = new Error(error.message || '请求失败');
    apiError.code = error.statusCode || error.code || -1;
    apiError.type = 'API_ERROR';
    apiError.originalError = error;
    return apiError;
  }

  /**
   * GET请求
   */
  get(url, options = {}) {
    return this.request(url, { ...options, method: 'GET' });
  }

  /**
   * POST请求
   */
  post(url, data, options = {}) {
    return this.request(url, { ...options, method: 'POST', data });
  }

  /**
   * PUT请求
   */
  put(url, data, options = {}) {
    return this.request(url, { ...options, method: 'PUT', data });
  }

  /**
   * DELETE请求
   */
  delete(url, options = {}) {
    return this.request(url, { ...options, method: 'DELETE' });
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return { ...this.stats };
  }
}

// 创建全局实例
const apiClient = new FinalAPIClient();

// 导出便捷方法
module.exports = {
  FinalAPIClient,
  apiClient,
  get: (url, options) => apiClient.get(url, options),
  post: (url, data, options) => apiClient.post(url, data, options),
  put: (url, data, options) => apiClient.put(url, data, options),
  delete: (url, options) => apiClient.delete(url, options)
};
