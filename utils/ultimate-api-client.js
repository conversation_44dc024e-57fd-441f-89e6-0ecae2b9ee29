/**
 * 终极统一API客户端 - 第三阶段实现
 * Ultimate Unified API Client - Phase 3 Implementation
 * 
 * 整合所有现有API客户端的优点：
 * - api.js 的云函数回退机制
 * - api-client-unified.js 的高级功能
 * - api-client-final.js 的智能路由
 * - optimized-api-client.js 的性能优化
 * 
 * 新增功能：
 * - 智能缓存策略
 * - 请求去重和批量处理
 * - 性能监控和统计
 * - 自动重试和错误恢复
 * - 多租户数据隔离集成
 * - 权限验证集成
 */

const { enhancedDataIsolation } = require('./enhanced-data-isolation');
const { permissionManager } = require('./enhanced-permission-manager');

/**
 * 终极统一API客户端类
 */
class UltimateAPIClient {
  constructor(options = {}) {
    // 基础配置
    this.baseURL = options.baseURL || this.getEnvironmentBaseURL();
    this.timeout = options.timeout || 10000;
    this.retryTimes = options.retryTimes || 3;
    this.retryDelay = options.retryDelay || 1000;
    
    // 云开发配置
    this.cloudEnabled = this.checkCloudAvailability();
    this.cloudFunctions = this.initCloudFunctionMapping();
    
    // 缓存配置
    this.cache = new Map();
    this.cacheConfig = {
      defaultTTL: 5 * 60 * 1000, // 5分钟
      maxSize: 500,
      enableCache: true
    };
    
    // 请求队列和去重
    this.requestQueue = new Map();
    this.pendingRequests = new Map();
    
    // 性能统计
    this.stats = {
      totalRequests: 0,
      successRequests: 0,
      failedRequests: 0,
      cacheHits: 0,
      cloudRequests: 0,
      httpRequests: 0,
      avgResponseTime: 0,
      errors: [],
      startTime: Date.now()
    };
    
    // 拦截器
    this.requestInterceptors = [];
    this.responseInterceptors = [];
    
    // 初始化
    this.initDefaultInterceptors();
    this.initPerformanceMonitoring();
  }

  /**
   * 获取环境基础URL
   */
  getEnvironmentBaseURL() {
    try {
      const app = getApp();
      if (app?.globalData?.baseUrl) {
        return app.globalData.baseUrl;
      }
    } catch (error) {
      console.warn('获取环境配置失败:', error);
    }
    
    // 根据环境返回不同的URL
    if (typeof wx !== 'undefined') {
      return 'https://api.zhihuiyange.com'; // 生产环境
    }
    return 'http://localhost:3000'; // 开发环境
  }

  /**
   * 检查云开发可用性
   */
  checkCloudAvailability() {
    try {
      return typeof wx !== 'undefined' && 
             typeof wx.cloud !== 'undefined' && 
             wx.cloud.callFunction;
    } catch (error) {
      return false;
    }
  }

  /**
   * 初始化云函数映射
   */
  initCloudFunctionMapping() {
    return new Map([
      ['/api/v2/auth/', 'auth'],
      ['/api/v2/health/', 'health'],
      ['/api/v2/production/', 'production'],
      ['/api/v2/flocks/', 'flockManagementV2'],
      ['/api/v2/materials/', 'materialManagement'],
      ['/api/v2/finance/', 'financeManagement'],
      ['/api/admin/', 'admin'],
      ['/api/v2/users/', 'userManagement'],
      ['/api/v2/tenants/', 'tenantManagement']
    ]);
  }

  /**
   * 初始化默认拦截器
   */
  initDefaultInterceptors() {
    // 请求拦截器：添加认证和租户信息
    this.addRequestInterceptor(async (config) => {
      const user = this.getCurrentUser();
      
      if (user) {
        config.headers = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.token || user.access_token}`,
          'X-Tenant-ID': user.tenant_id,
          'X-User-ID': user._id || user.id,
          'X-Request-ID': this.generateRequestId(),
          'X-Client-Version': '3.0.0',
          ...config.headers
        };

        // 权限验证
        const requiredPermission = this.extractPermissionFromURL(config.url, config.method);
        if (requiredPermission) {
          const hasPermission = await permissionManager.checkPermission(user, requiredPermission);
          if (!hasPermission) {
            throw new Error(`权限不足：缺少 ${requiredPermission} 权限`);
          }
        }
      }
      
      return config;
    });

    // 请求拦截器：数据隔离验证
    this.addRequestInterceptor(async (config) => {
      const user = this.getCurrentUser();
      
      if (user && ['POST', 'PUT', 'PATCH'].includes(config.method?.toUpperCase())) {
        const collection = this.extractCollectionFromURL(config.url);
        if (collection && config.data) {
          try {
            config.data = await enhancedDataIsolation.validateWriteData(
              user, 
              collection, 
              config.data,
              { operation: config.method }
            );
          } catch (error) {
            console.error('数据隔离验证失败:', error);
            throw error;
          }
        }
      }
      
      return config;
    });

    // 响应拦截器：成功处理
    this.addResponseInterceptor(
      (response) => {
        this.updateStats('success');
        return this.normalizeResponse(response);
      },
      (error) => {
        this.updateStats('error', error);
        return this.handleError(error);
      }
    );
  }

  /**
   * 初始化性能监控
   */
  initPerformanceMonitoring() {
    // 每分钟清理过期缓存
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 60 * 1000);

    // 每5分钟输出性能统计
    setInterval(() => {
      console.log('API客户端性能统计:', this.getStats());
    }, 5 * 60 * 1000);
  }

  /**
   * 主要请求方法
   */
  async request(config) {
    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      // 1. 标准化配置
      config = this.normalizeConfig(config);

      // 2. 应用请求拦截器
      for (const interceptor of this.requestInterceptors) {
        config = await interceptor(config);
      }

      // 3. 检查缓存
      if (config.method === 'GET' && this.cacheConfig.enableCache && config.cache !== false) {
        const cached = this.getFromCache(config);
        if (cached) {
          this.stats.cacheHits++;
          return cached;
        }
      }

      // 4. 请求去重
      const requestKey = this.generateRequestKey(config);
      if (this.pendingRequests.has(requestKey)) {
        return await this.pendingRequests.get(requestKey);
      }

      // 5. 执行请求
      const requestPromise = this.executeRequestWithRetry(config);
      this.pendingRequests.set(requestKey, requestPromise);

      const response = await requestPromise;

      // 6. 应用响应拦截器
      let processedResponse = response;
      for (const interceptor of this.responseInterceptors) {
        if (typeof interceptor === 'function') {
          processedResponse = await interceptor(processedResponse);
        } else if (interceptor.fulfilled) {
          processedResponse = await interceptor.fulfilled(processedResponse);
        }
      }

      // 7. 缓存响应
      if (config.method === 'GET' && this.cacheConfig.enableCache && config.cache !== false) {
        this.setCache(config, processedResponse);
      }

      // 8. 清理pending请求
      this.pendingRequests.delete(requestKey);

      // 9. 更新性能统计
      this.updateResponseTime(Date.now() - startTime);

      return processedResponse;

    } catch (error) {
      this.pendingRequests.delete(requestKey);
      
      // 应用错误拦截器
      for (const interceptor of this.responseInterceptors) {
        if (interceptor.rejected) {
          error = await interceptor.rejected(error);
        }
      }
      
      throw error;
    }
  }

  /**
   * 带重试的请求执行
   */
  async executeRequestWithRetry(config, attempt = 1) {
    try {
      return await this.executeRequest(config);
    } catch (error) {
      if (attempt < this.retryTimes && this.shouldRetry(error)) {
        console.warn(`请求失败，第${attempt}次重试:`, error.message);
        await this.delay(this.retryDelay * attempt);
        return this.executeRequestWithRetry(config, attempt + 1);
      }
      throw error;
    }
  }

  /**
   * 执行请求 - 智能路由
   */
  async executeRequest(config) {
    const { url, method, data } = config;

    // 优先使用云函数
    if (this.cloudEnabled && this.shouldUseCloudFunction(url)) {
      try {
        const result = await this.callCloudFunction(url, method, data, config);
        this.stats.cloudRequests++;
        return result;
      } catch (cloudError) {
        console.warn('云函数调用失败，回退到HTTP:', cloudError.message);
        // 继续执行HTTP请求
      }
    }

    // 回退到HTTP请求
    const result = await this.callHTTPAPI(config);
    this.stats.httpRequests++;
    return result;
  }

  /**
   * 判断是否应该使用云函数
   */
  shouldUseCloudFunction(url) {
    for (const [pattern] of this.cloudFunctions) {
      if (url.includes(pattern)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 调用云函数
   */
  async callCloudFunction(url, method, data, config) {
    const functionName = this.getCloudFunctionName(url);
    const action = this.extractActionFromURL(url, method);

    const cloudData = {
      action,
      data: data || {},
      method: method.toUpperCase(),
      url,
      headers: config.headers || {},
      query: config.params || {}
    };

    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: functionName,
        data: cloudData,
        success: (res) => {
          if (res.result) {
            if (res.result.success !== false) {
              resolve(res.result);
            } else {
              reject(new Error(res.result.error || '云函数执行失败'));
            }
          } else {
            reject(new Error('云函数返回数据格式错误'));
          }
        },
        fail: (error) => {
          reject(new Error(`云函数调用失败: ${error.errMsg || error.message}`));
        }
      });
    });
  }

  /**
   * 调用HTTP API
   */
  async callHTTPAPI(config) {
    const { url, method, data, headers, timeout, params } = config;

    let fullURL = this.buildFullURL(url);
    
    // 处理查询参数
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams(params);
      fullURL += (fullURL.includes('?') ? '&' : '?') + searchParams.toString();
    }

    const requestConfig = {
      url: fullURL,
      method: method.toUpperCase(),
      data: data || {},
      header: {
        'Content-Type': 'application/json',
        ...headers
      },
      timeout: timeout || this.timeout
    };

    return new Promise((resolve, reject) => {
      wx.request({
        ...requestConfig,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(this.formatResponse(res.data, res));
          } else {
            reject(this.createHTTPError(res));
          }
        },
        fail: (error) => {
          reject(this.createNetworkError(error));
        }
      });
    });
  }

  /**
   * 便捷方法
   */
  get(url, config = {}) {
    return this.request({
      ...config,
      url,
      method: 'GET'
    });
  }

  post(url, data, config = {}) {
    return this.request({
      ...config,
      url,
      method: 'POST',
      data
    });
  }

  put(url, data, config = {}) {
    return this.request({
      ...config,
      url,
      method: 'PUT',
      data
    });
  }

  patch(url, data, config = {}) {
    return this.request({
      ...config,
      url,
      method: 'PATCH',
      data
    });
  }

  delete(url, config = {}) {
    return this.request({
      ...config,
      url,
      method: 'DELETE'
    });
  }

  /**
   * 安全查询（集成数据隔离）
   */
  async secureQuery(collection, query = {}, options = {}) {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('用户未登录');
    }

    const secureQuery = await enhancedDataIsolation.createSecureQuery(
      user, 
      collection, 
      query, 
      options
    );

    const url = `/api/v2/${collection}`;
    return this.get(url, {
      params: secureQuery,
      ...options
    });
  }

  /**
   * 批量请求
   */
  async batch(requests, options = {}) {
    const { concurrency = 5, failFast = false } = options;
    const results = [];
    const errors = [];

    for (let i = 0; i < requests.length; i += concurrency) {
      const batch = requests.slice(i, i + concurrency);
      
      const batchPromises = batch.map(async (request, index) => {
        try {
          const result = await this.request(request);
          return { index: i + index, success: true, data: result };
        } catch (error) {
          const errorResult = { index: i + index, success: false, error };
          errors.push(errorResult);
          
          if (failFast) {
            throw error;
          }
          
          return errorResult;
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults.map(r => r.value || r.reason));
    }

    return {
      results,
      errors,
      successCount: results.filter(r => r.success).length,
      errorCount: errors.length
    };
  }

  // 辅助方法
  normalizeConfig(config) {
    return {
      method: 'GET',
      headers: {},
      cache: true,
      ...config,
      url: config.url?.trim(),
      method: config.method?.toUpperCase() || 'GET'
    };
  }

  normalizeResponse(response) {
    // 统一响应格式
    if (response && typeof response === 'object') {
      return {
        success: true,
        data: response.data || response,
        message: response.message || 'success',
        code: response.code || 200,
        timestamp: Date.now(),
        ...response
      };
    }
    
    return {
      success: true,
      data: response,
      message: 'success',
      code: 200,
      timestamp: Date.now()
    };
  }

  getCurrentUser() {
    try {
      const app = getApp();
      return app?.globalData?.user || null;
    } catch (error) {
      return null;
    }
  }

  generateRequestId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  generateRequestKey(config) {
    const keyData = {
      method: config.method,
      url: config.url,
      data: config.data,
      params: config.params
    };
    return JSON.stringify(keyData);
  }

  buildFullURL(url) {
    if (url.startsWith('http')) {
      return url;
    }
    return `${this.baseURL}${url}`;
  }

  getCloudFunctionName(url) {
    for (const [pattern, functionName] of this.cloudFunctions) {
      if (url.includes(pattern)) {
        return functionName;
      }
    }
    return 'common';
  }

  extractActionFromURL(url, method) {
    const parts = url.split('/').filter(Boolean);
    const lastPart = parts[parts.length - 1];
    
    // 如果最后一部分是ID（数字），则使用倒数第二部分
    if (/^\d+$/.test(lastPart) && parts.length > 1) {
      return parts[parts.length - 2];
    }
    
    return lastPart || method.toLowerCase();
  }

  extractCollectionFromURL(url) {
    const match = url.match(/\/api\/v\d+\/(\w+)/);
    return match ? match[1] : null;
  }

  extractPermissionFromURL(url, method) {
    const collection = this.extractCollectionFromURL(url);
    if (!collection) return null;

    const action = method?.toUpperCase();
    const permissionMap = {
      'GET': 'VIEW',
      'POST': 'CREATE',
      'PUT': 'EDIT',
      'PATCH': 'EDIT',
      'DELETE': 'DELETE'
    };

    const permission = permissionMap[action];
    if (permission) {
      return `${collection.toUpperCase()}_${permission}`;
    }

    return null;
  }

  formatResponse(data, res) {
    return {
      data,
      status: res.statusCode,
      headers: res.header,
      config: res.config
    };
  }

  createHTTPError(res) {
    const error = new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`);
    error.response = res;
    error.status = res.statusCode;
    return error;
  }

  createNetworkError(error) {
    const networkError = new Error(`网络错误: ${error.errMsg || '网络连接失败'}`);
    networkError.originalError = error;
    return networkError;
  }

  shouldRetry(error) {
    // 网络错误或5xx服务器错误可以重试
    return error.status >= 500 || error.errMsg?.includes('timeout') || error.errMsg?.includes('fail');
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  handleError(error) {
    console.error('API请求错误:', error);
    
    // 记录错误统计
    this.stats.errors.push({
      timestamp: Date.now(),
      message: error.message,
      stack: error.stack,
      url: error.config?.url
    });

    // 限制错误日志数量
    if (this.stats.errors.length > 100) {
      this.stats.errors.shift();
    }

    return Promise.reject(error);
  }

  updateStats(type, error = null) {
    if (type === 'success') {
      this.stats.successRequests++;
    } else if (type === 'error') {
      this.stats.failedRequests++;
    }
  }

  updateResponseTime(time) {
    this.stats.avgResponseTime = 
      (this.stats.avgResponseTime + time) / 2;
  }

  // 缓存相关方法
  getFromCache(config) {
    const key = this.generateRequestKey(config);
    const cached = this.cache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.cacheConfig.defaultTTL) {
      return cached.data;
    }
    
    return null;
  }

  setCache(config, data) {
    const key = this.generateRequestKey(config);
    
    // 检查缓存大小限制
    if (this.cache.size >= this.cacheConfig.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  cleanupExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache) {
      if (now - value.timestamp > this.cacheConfig.defaultTTL) {
        this.cache.delete(key);
      }
    }
  }

  clearCache() {
    this.cache.clear();
  }

  // 拦截器方法
  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor);
  }

  addResponseInterceptor(fulfilled, rejected) {
    this.responseInterceptors.push({
      fulfilled,
      rejected
    });
  }

  // 统计信息
  getStats() {
    const uptime = Date.now() - this.stats.startTime;
    
    return {
      ...this.stats,
      uptime: `${Math.floor(uptime / 1000)}秒`,
      cacheHitRate: this.stats.totalRequests > 0 
        ? (this.stats.cacheHits / this.stats.totalRequests * 100).toFixed(2) + '%'
        : '0%',
      successRate: this.stats.totalRequests > 0
        ? (this.stats.successRequests / this.stats.totalRequests * 100).toFixed(2) + '%'
        : '0%',
      avgResponseTime: Math.round(this.stats.avgResponseTime) + 'ms',
      cacheSize: this.cache.size,
      pendingRequests: this.pendingRequests.size,
      cloudFunctionUsage: this.stats.totalRequests > 0
        ? (this.stats.cloudRequests / this.stats.totalRequests * 100).toFixed(2) + '%'
        : '0%'
    };
  }
}

// 创建全局实例
const ultimateAPIClient = new UltimateAPIClient();

// 业务API模块 - 整合所有现有API调用
const businessAPI = {
  // 认证相关 - 整合自 auth 模块
  auth: {
    login: (data) => ultimateAPIClient.post('/api/v2/auth/login', data),
    logout: () => ultimateAPIClient.post('/api/v2/auth/logout'),
    register: (data) => ultimateAPIClient.post('/api/v2/auth/register', data),
    wechatLogin: (data) => ultimateAPIClient.post('/api/v2/auth/wechat', data),
    refreshToken: (data) => ultimateAPIClient.post('/api/v2/auth/refresh', data),
    getUserInfo: () => ultimateAPIClient.get('/api/v2/auth/user-info'),
    changePassword: (data) => ultimateAPIClient.put('/api/v2/auth/change-password', data),
    resetPassword: (data) => ultimateAPIClient.post('/api/v2/auth/reset-password', data)
  },

  // 首页相关 - 整合自 home 模块
  home: {
    getHomeData: () => ultimateAPIClient.get('/api/v1/home/<USER>', { cache: true }),
    getAnnouncements: (params) => ultimateAPIClient.get('/api/v1/home/<USER>', { params }),
    getWeather: (params) => ultimateAPIClient.get('/api/v1/home/<USER>', { params, cache: true }),
    getQuickStats: () => ultimateAPIClient.get('/api/v1/home/<USER>', { cache: true })
  },

  // 鹅群管理 - 整合自 production/flocks 模块
  flocks: {
    getList: (params) => ultimateAPIClient.secureQuery('flocks', params),
    getDetail: (id) => ultimateAPIClient.get(`/api/v2/flocks/${id}`),
    create: (data) => ultimateAPIClient.post('/api/v2/flocks', data),
    update: (id, data) => ultimateAPIClient.put(`/api/v2/flocks/${id}`, data),
    delete: (id) => ultimateAPIClient.delete(`/api/v2/flocks/${id}`),
    getStats: (id) => ultimateAPIClient.get(`/api/v2/flocks/${id}/stats`),
    batchUpdate: (data) => ultimateAPIClient.post('/api/v2/flocks/batch-update', data)
  },

  // 健康记录 - 整合自 health 模块
  health: {
    getRecords: (params) => ultimateAPIClient.secureQuery('health_records', params),
    createRecord: (data) => ultimateAPIClient.post('/api/v2/health/records', data),
    updateRecord: (id, data) => ultimateAPIClient.put(`/api/v2/health/records/${id}`, data),
    deleteRecord: (id) => ultimateAPIClient.delete(`/api/v2/health/records/${id}`),
    getStats: (params) => ultimateAPIClient.get('/api/v2/health/stats', { params }),
    aiDiagnosis: (data) => ultimateAPIClient.post('/api/v2/health/ai-diagnosis', data),
    getVaccinationPlan: (flockId) => ultimateAPIClient.get(`/api/v2/health/vaccination-plan/${flockId}`),
    updateVaccinationPlan: (flockId, data) => ultimateAPIClient.put(`/api/v2/health/vaccination-plan/${flockId}`, data)
  },

  // 物料管理 - 整合自 materials 模块
  materials: {
    getList: (params) => ultimateAPIClient.secureQuery('materials', params),
    create: (data) => ultimateAPIClient.post('/api/v2/materials', data),
    update: (id, data) => ultimateAPIClient.put(`/api/v2/materials/${id}`, data),
    delete: (id) => ultimateAPIClient.delete(`/api/v2/materials/${id}`),
    getStockMovements: (params) => ultimateAPIClient.get('/api/v2/materials/stock-movements', { params }),
    createStockMovement: (data) => ultimateAPIClient.post('/api/v2/materials/stock-movements', data),
    getLowStockAlerts: () => ultimateAPIClient.get('/api/v2/materials/low-stock-alerts'),
    getInventoryReport: (params) => ultimateAPIClient.get('/api/v2/materials/inventory-report', { params })
  },

  // 财务管理 - 整合自 finance 模块
  finance: {
    getRecords: (params) => ultimateAPIClient.secureQuery('financial_records', params),
    createRecord: (data) => ultimateAPIClient.post('/api/v2/finance/records', data),
    updateRecord: (id, data) => ultimateAPIClient.put(`/api/v2/finance/records/${id}`, data),
    deleteRecord: (id) => ultimateAPIClient.delete(`/api/v2/finance/records/${id}`),
    getStats: (params) => ultimateAPIClient.get('/api/v2/finance/stats', { params }),
    getReports: (params) => ultimateAPIClient.get('/api/v2/finance/reports', { params }),
    getCostAllocation: (params) => ultimateAPIClient.get('/api/v2/finance/cost-allocation', { params }),
    generateReport: (data) => ultimateAPIClient.post('/api/v2/finance/generate-report', data)
  },

  // 商城 - 整合自 shop 模块（使用V1 API）
  shop: {
    getProducts: (params) => ultimateAPIClient.get('/api/v1/shop/products', { params, cache: true }),
    getProductDetail: (id) => ultimateAPIClient.get(`/api/v1/shop/products/${id}`, { cache: true }),
    getCategories: () => ultimateAPIClient.get('/api/v1/shop/categories', { cache: true }),
    createOrder: (data) => ultimateAPIClient.post('/api/v1/shop/orders', data),
    getUserOrders: (params) => ultimateAPIClient.get('/api/v1/shop/orders', { params }),
    getOrderDetail: (id) => ultimateAPIClient.get(`/api/v1/shop/orders/${id}`),
    cancelOrder: (id) => ultimateAPIClient.put(`/api/v1/shop/orders/${id}/cancel`),
    addToCart: (data) => ultimateAPIClient.post('/api/v1/shop/cart', data),
    getCart: () => ultimateAPIClient.get('/api/v1/shop/cart'),
    updateCartItem: (id, data) => ultimateAPIClient.put(`/api/v1/shop/cart/${id}`, data),
    removeCartItem: (id) => ultimateAPIClient.delete(`/api/v1/shop/cart/${id}`),
    clearCart: () => ultimateAPIClient.delete('/api/v1/shop/cart')
  },

  // 用户管理 - 整合自 profile 模块
  profile: {
    getSettings: () => ultimateAPIClient.get('/api/v2/users/settings'),
    updateSettings: (data) => ultimateAPIClient.put('/api/v2/users/settings', data),
    getProfile: () => ultimateAPIClient.get('/api/v2/users/profile'),
    updateProfile: (data) => ultimateAPIClient.put('/api/v2/users/profile', data),
    getHelpList: () => ultimateAPIClient.get('/api/v2/users/help', { cache: true }),
    getFeedback: (params) => ultimateAPIClient.get('/api/v2/users/feedback', { params }),
    submitFeedback: (data) => ultimateAPIClient.post('/api/v2/users/feedback', data)
  },

  // 平台管理 - 管理员功能
  admin: {
    // 今日鹅价管理
    goosePrices: {
      getList: (params) => ultimateAPIClient.get('/api/admin/goose-prices', { params }),
      create: (data) => ultimateAPIClient.post('/api/admin/goose-prices', data),
      update: (id, data) => ultimateAPIClient.put(`/api/admin/goose-prices/${id}`, data),
      delete: (id) => ultimateAPIClient.delete(`/api/admin/goose-prices/${id}`),
      publish: (id) => ultimateAPIClient.put(`/api/admin/goose-prices/${id}/publish`)
    },

    // 平台公告管理
    announcements: {
      getList: (params) => ultimateAPIClient.get('/api/admin/announcements', { params }),
      create: (data) => ultimateAPIClient.post('/api/admin/announcements', data),
      update: (id, data) => ultimateAPIClient.put(`/api/admin/announcements/${id}`, data),
      delete: (id) => ultimateAPIClient.delete(`/api/admin/announcements/${id}`),
      publish: (id) => ultimateAPIClient.put(`/api/admin/announcements/${id}/publish`)
    },

    // 租户管理
    tenants: {
      getList: (params) => ultimateAPIClient.get('/api/admin/tenants', { params }),
      create: (data) => ultimateAPIClient.post('/api/admin/tenants', data),
      update: (id, data) => ultimateAPIClient.put(`/api/admin/tenants/${id}`, data),
      delete: (id) => ultimateAPIClient.delete(`/api/admin/tenants/${id}`),
      suspend: (id) => ultimateAPIClient.put(`/api/admin/tenants/${id}/suspend`),
      activate: (id) => ultimateAPIClient.put(`/api/admin/tenants/${id}/activate`),
      getStats: () => ultimateAPIClient.get('/api/admin/tenants/stats')
    },

    // 系统管理
    system: {
      getConfig: () => ultimateAPIClient.get('/api/admin/system/config'),
      updateConfig: (data) => ultimateAPIClient.put('/api/admin/system/config', data),
      getStats: () => ultimateAPIClient.get('/api/admin/system/stats'),
      getLogs: (params) => ultimateAPIClient.get('/api/admin/system/logs', { params }),
      backup: () => ultimateAPIClient.post('/api/admin/system/backup'),
      getBackups: () => ultimateAPIClient.get('/api/admin/system/backups')
    }
  },

  // 通用工具方法
  utils: {
    upload: (filePath, options = {}) => {
      return new Promise((resolve, reject) => {
        wx.uploadFile({
          url: ultimateAPIClient.buildFullURL('/api/v2/upload'),
          filePath,
          name: 'file',
          header: {
            'Authorization': `Bearer ${ultimateAPIClient.getCurrentUser()?.token}`,
            'X-Tenant-ID': ultimateAPIClient.getCurrentUser()?.tenant_id
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              resolve(data);
            } catch (error) {
              reject(new Error('上传响应解析失败'));
            }
          },
          fail: reject,
          ...options
        });
      });
    },

    download: (url, options = {}) => {
      return new Promise((resolve, reject) => {
        wx.downloadFile({
          url: ultimateAPIClient.buildFullURL(url),
          success: resolve,
          fail: reject,
          ...options
        });
      });
    },

    // 批量操作
    batchRequest: (requests, options) => ultimateAPIClient.batch(requests, options),

    // 缓存管理
    clearCache: () => ultimateAPIClient.clearCache(),

    // 统计信息
    getStats: () => ultimateAPIClient.getStats()
  }
};

module.exports = {
  UltimateAPIClient,
  ultimateAPIClient,
  businessAPI,
  // 向后兼容的导出
  apiClient: ultimateAPIClient,
  api: businessAPI
};
