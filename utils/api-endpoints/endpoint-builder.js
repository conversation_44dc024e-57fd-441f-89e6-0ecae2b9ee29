/**
 * API端点构建器
 * API Endpoint Builder
 */

/**
 * API版本定义
 */
const API_VERSIONS = {
  V1: '/api/v1',
  V2: '/api/v2',
  TENANT: '/api/v1/tenant',
  PLATFORM: '/api/v1/platform'
};

/**
 * API端点构建器类
 */
class ApiEndpointBuilder {
  constructor(baseUrl = '', version = API_VERSIONS.V2) {
    this.baseUrl = baseUrl;
    this.version = version;
  }

  /**
   * 设置基础URL
   */
  setBaseUrl(baseUrl) {
    this.baseUrl = baseUrl;
    return this;
  }

  /**
   * 设置API版本
   */
  setVersion(version) {
    this.version = version;
    return this;
  }

  /**
   * 构建完整的API端点路径
   */
  buildEndpoint(category, endpoint, params = {}) {
    const categoryConfig = this.getEndpointCategory(category);
    if (!categoryConfig) {
      throw new Error(`API分类 ${category} 不存在`);
    }

    const endpointConfig = categoryConfig.endpoints[endpoint];
    if (!endpointConfig) {
      throw new Error(`API端点 ${category}.${endpoint} 不存在`);
    }

    // 使用特定版本或默认版本
    const version = categoryConfig.version || this.version;
    let path = `${version}${categoryConfig.base}${endpointConfig.path}`;

    // 替换路径参数
    Object.keys(params).forEach(key => {
      path = path.replace(`:${key}`, params[key]);
    });

    return {
      ...endpointConfig,
      fullPath: `${this.baseUrl}${path}`,
      category: category,
      name: endpoint
    };
  }

  /**
   * 获取端点配置
   */
  getEndpointConfig(category, endpoint) {
    const categoryConfig = this.getEndpointCategory(category);
    if (!categoryConfig) {
      return null;
    }

    const endpointConfig = categoryConfig.endpoints[endpoint];
    if (!endpointConfig) {
      return null;
    }

    return {
      ...endpointConfig,
      category: category,
      name: endpoint,
      basePath: categoryConfig.base
    };
  }

  /**
   * 根据权限获取端点
   */
  getEndpointsByPermissions(permissions) {
    const result = {};
    const allEndpoints = this.getAllEndpoints();
    
    Object.keys(allEndpoints).forEach(category => {
      result[category] = {};
      Object.keys(allEndpoints[category]).forEach(endpoint => {
        const config = allEndpoints[category][endpoint];
        if (!config.permissions || this.hasPermissions(permissions, config.permissions)) {
          result[category][endpoint] = config;
        }
      });
    });

    return result;
  }

  /**
   * 检查权限
   */
  hasPermissions(userPermissions, requiredPermissions) {
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }
    
    return requiredPermissions.some(permission => 
      userPermissions.includes(permission)
    );
  }

  /**
   * 获取所有端点
   */
  getAllEndpoints() {
    const allEndpoints = {};
    const categories = this.getAllCategories();
    
    Object.keys(categories).forEach(category => {
      allEndpoints[category] = {};
      Object.keys(categories[category].endpoints).forEach(endpoint => {
        allEndpoints[category][endpoint] = this.getEndpointConfig(category, endpoint);
      });
    });

    return allEndpoints;
  }

  /**
   * 获取端点分类
   */
  getEndpointCategory(category) {
    const categories = this.getAllCategories();
    return categories[category] || null;
  }

  /**
   * 获取所有分类（需要在子类中实现）
   */
  getAllCategories() {
    // 这个方法将在统一导出文件中被重写
    return {};
  }

  /**
   * 生成端点URL
   */
  generateUrl(category, endpoint, params = {}, query = {}) {
    const endpointInfo = this.buildEndpoint(category, endpoint, params);
    let url = endpointInfo.fullPath;
    
    // 添加查询参数
    const queryString = Object.keys(query)
      .filter(key => query[key] !== undefined && query[key] !== null)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`)
      .join('&');
    
    if (queryString) {
      url += `?${queryString}`;
    }
    
    return url;
  }

  /**
   * 验证端点配置
   */
  validateEndpoint(category, endpoint) {
    try {
      const config = this.getEndpointConfig(category, endpoint);
      if (!config) {
        return { valid: false, error: '端点不存在' };
      }
      
      if (!config.method) {
        return { valid: false, error: '缺少HTTP方法' };
      }
      
      if (!config.path) {
        return { valid: false, error: '缺少路径' };
      }
      
      return { valid: true, config };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }

  /**
   * 获取端点统计信息
   */
  getStatistics() {
    const categories = this.getAllCategories();
    const stats = {
      totalCategories: 0,
      totalEndpoints: 0,
      endpointsByMethod: {},
      endpointsByPermission: {},
      cacheableEndpoints: 0
    };
    
    Object.keys(categories).forEach(categoryName => {
      stats.totalCategories++;
      const category = categories[categoryName];
      
      Object.keys(category.endpoints).forEach(endpointName => {
        stats.totalEndpoints++;
        const endpoint = category.endpoints[endpointName];
        
        // 按方法统计
        const method = endpoint.method || 'GET';
        stats.endpointsByMethod[method] = (stats.endpointsByMethod[method] || 0) + 1;
        
        // 按权限统计
        if (endpoint.permissions) {
          endpoint.permissions.forEach(permission => {
            stats.endpointsByPermission[permission] = (stats.endpointsByPermission[permission] || 0) + 1;
          });
        }
        
        // 可缓存端点统计
        if (endpoint.cache) {
          stats.cacheableEndpoints++;
        }
      });
    });
    
    return stats;
  }
}

module.exports = {
  ApiEndpointBuilder,
  API_VERSIONS
};
