/**
 * 业务相关API端点定义
 * Business API Endpoints (Flocks, Health, Production)
 */

const FLOCKS_ENDPOINTS = {
  base: '/flocks',
  version: '/api/v2',
  endpoints: {
    LIST: {
      path: '',
      method: 'GET',
      description: '获取鹅群列表',
      permissions: ['FLOCK_READ'],
      cache: true,
      cacheTTL: 180
    },
    CREATE: {
      path: '',
      method: 'POST',
      description: '创建新鹅群',
      permissions: ['FLOCK_CREATE'],
      cache: false
    },
    DETAIL: {
      path: '/:id',
      method: 'GET',
      description: '获取鹅群详情',
      permissions: ['FLOCK_READ'],
      cache: true,
      cacheTTL: 300
    },
    UPDATE: {
      path: '/:id',
      method: 'PUT',
      description: '更新鹅群信息',
      permissions: ['FLOCK_UPDATE'],
      cache: false
    },
    DELETE: {
      path: '/:id',
      method: 'DELETE',
      description: '删除鹅群',
      permissions: ['FLOCK_DELETE'],
      cache: false
    },
    STATISTICS: {
      path: '/:id/statistics',
      method: 'GET',
      description: '获取鹅群统计数据',
      permissions: ['FLOCK_READ'],
      cache: true,
      cacheTTL: 600
    }
  }
};

const HEALTH_ENDPOINTS = {
  base: '/health',
  version: '/api/v2',
  endpoints: {
    RECORDS: {
      path: '/records',
      method: 'GET',
      description: '获取健康记录列表',
      permissions: ['HEALTH_READ'],
      cache: true,
      cacheTTL: 300
    },
    CREATE_RECORD: {
      path: '/records',
      method: 'POST',
      description: '创建健康记录',
      permissions: ['HEALTH_CREATE'],
      cache: false
    },
    RECORD_DETAIL: {
      path: '/records/:id',
      method: 'GET',
      description: '获取健康记录详情',
      permissions: ['HEALTH_READ'],
      cache: true,
      cacheTTL: 300
    },
    UPDATE_RECORD: {
      path: '/records/:id',
      method: 'PUT',
      description: '更新健康记录',
      permissions: ['HEALTH_UPDATE'],
      cache: false
    },
    DELETE_RECORD: {
      path: '/records/:id',
      method: 'DELETE',
      description: '删除健康记录',
      permissions: ['HEALTH_DELETE'],
      cache: false
    },
    KNOWLEDGE_BASE: {
      path: '/knowledge',
      method: 'GET',
      description: '获取健康知识库',
      permissions: ['HEALTH_READ'],
      cache: true,
      cacheTTL: 3600
    },
    AI_DIAGNOSIS: {
      path: '/ai-diagnosis',
      method: 'POST',
      description: 'AI健康诊断',
      permissions: ['HEALTH_AI'],
      cache: false
    },
    HEALTH_REPORT: {
      path: '/report',
      method: 'GET',
      description: '生成健康报告',
      permissions: ['HEALTH_READ'],
      cache: true,
      cacheTTL: 1800
    }
  }
};

const PRODUCTION_ENDPOINTS = {
  base: '/production',
  version: '/api/v2',
  endpoints: {
    RECORDS: {
      path: '/records',
      method: 'GET',
      description: '获取生产记录列表',
      permissions: ['PRODUCTION_READ'],
      cache: true,
      cacheTTL: 300
    },
    CREATE_RECORD: {
      path: '/records',
      method: 'POST',
      description: '创建生产记录',
      permissions: ['PRODUCTION_CREATE'],
      cache: false
    },
    RECORD_DETAIL: {
      path: '/records/:id',
      method: 'GET',
      description: '获取生产记录详情',
      permissions: ['PRODUCTION_READ'],
      cache: true,
      cacheTTL: 300
    },
    UPDATE_RECORD: {
      path: '/records/:id',
      method: 'PUT',
      description: '更新生产记录',
      permissions: ['PRODUCTION_UPDATE'],
      cache: false
    },
    DELETE_RECORD: {
      path: '/records/:id',
      method: 'DELETE',
      description: '删除生产记录',
      permissions: ['PRODUCTION_DELETE'],
      cache: false
    },
    STATISTICS: {
      path: '/statistics',
      method: 'GET',
      description: '获取生产统计数据',
      permissions: ['PRODUCTION_READ'],
      cache: true,
      cacheTTL: 600
    },
    TRENDS: {
      path: '/trends',
      method: 'GET',
      description: '获取生产趋势分析',
      permissions: ['PRODUCTION_READ'],
      cache: true,
      cacheTTL: 1800
    }
  }
};

module.exports = {
  FLOCKS_ENDPOINTS,
  HEALTH_ENDPOINTS,
  PRODUCTION_ENDPOINTS
};
