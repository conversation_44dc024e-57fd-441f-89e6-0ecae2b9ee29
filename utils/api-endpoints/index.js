/**
 * API端点统一导出文件
 * 替换原来的 utils/api-endpoints.js 大文件
 * 提供向后兼容的接口
 */

// 导入所有端点模块
const { AUTH_ENDPOINTS } = require('./auth-endpoints.js');
const { FLOCKS_ENDPOINTS, HEALTH_ENDPOINTS, PRODUCTION_ENDPOINTS } = require('./business-endpoints.js');
const { USERS_ENDPOINTS, INVENTORY_ENDPOINTS, FINANCE_ENDPOINTS, OA_ENDPOINTS } = require('./management-endpoints.js');
const { SHOP_ENDPOINTS, AI_ENDPOINTS, SYSTEM_ENDPOINTS, PLATFORM_ENDPOINTS } = require('./system-endpoints.js');
const { ApiEndpointBuilder, API_VERSIONS } = require('./endpoint-builder.js');

// 统一的API端点配置
const API_ENDPOINTS = {
  AUTH: AUTH_ENDPOINTS,
  FLOCKS: FLOCKS_ENDPOINTS,
  HEALTH: HEALTH_ENDPOINTS,
  PRODUCTION: PRODUCTION_ENDPOINTS,
  USERS: USERS_ENDPOINTS,
  INVENTORY: INVENTORY_ENDPOINTS,
  FINANCE: FINANCE_ENDPOINTS,
  OA: OA_ENDPOINTS,
  SHOP: SHOP_ENDPOINTS,
  AI: AI_ENDPOINTS,
  SYSTEM: SYSTEM_ENDPOINTS,
  PLATFORM: PLATFORM_ENDPOINTS
};

// 扩展的API端点构建器
class ExtendedApiEndpointBuilder extends ApiEndpointBuilder {
  getAllCategories() {
    return API_ENDPOINTS;
  }
}

// 创建默认构建器实例
const defaultBuilder = new ExtendedApiEndpointBuilder();

// 便捷方法
const endpoints = {
  // 构建方法
  build: (category, endpoint, params) => defaultBuilder.buildEndpoint(category, endpoint, params),
  get: (category, endpoint) => defaultBuilder.getEndpointConfig(category, endpoint),
  getAll: () => defaultBuilder.getAllEndpoints(),
  getByPermissions: (permissions) => defaultBuilder.getEndpointsByPermissions(permissions),
  generateUrl: (category, endpoint, params, query) => defaultBuilder.generateUrl(category, endpoint, params, query),
  validate: (category, endpoint) => defaultBuilder.validateEndpoint(category, endpoint),
  getStats: () => defaultBuilder.getStatistics(),
  
  // 创建构建器
  createBuilder: (baseUrl, version) => new ExtendedApiEndpointBuilder(baseUrl, version),
  
  // 常量
  VERSIONS: API_VERSIONS,
  ENDPOINTS: API_ENDPOINTS
};

// 向后兼容的快捷方法
const EndpointUtils = {
  // 获取完整URL
  getFullUrl(category, endpoint, params = {}, baseUrl = '') {
    const builder = new ExtendedApiEndpointBuilder(baseUrl);
    const info = builder.buildEndpoint(category, endpoint, params);
    return info.fullPath;
  },

  // 检查端点是否需要权限
  requiresPermission(category, endpoint) {
    const config = defaultBuilder.getEndpointConfig(category, endpoint);
    return config && config.permissions && config.permissions.length > 0;
  },

  // 检查端点是否可缓存
  isCacheable(category, endpoint) {
    const config = defaultBuilder.getEndpointConfig(category, endpoint);
    return config && config.cache === true;
  },

  // 获取端点的缓存TTL
  getCacheTTL(category, endpoint) {
    const config = defaultBuilder.getEndpointConfig(category, endpoint);
    return config && config.cacheTTL ? config.cacheTTL : 300; // 默认5分钟
  },

  // 按分类获取端点
  getEndpointsByCategory(category) {
    const categoryConfig = API_ENDPOINTS[category];
    return categoryConfig ? categoryConfig.endpoints : {};
  },

  // 获取所有GET端点
  getReadOnlyEndpoints() {
    const result = {};
    Object.keys(API_ENDPOINTS).forEach(category => {
      result[category] = {};
      Object.keys(API_ENDPOINTS[category].endpoints).forEach(endpoint => {
        const config = API_ENDPOINTS[category].endpoints[endpoint];
        if (config.method === 'GET') {
          result[category][endpoint] = config;
        }
      });
    });
    return result;
  },

  // 获取所有需要权限的端点
  getProtectedEndpoints() {
    const result = {};
    Object.keys(API_ENDPOINTS).forEach(category => {
      result[category] = {};
      Object.keys(API_ENDPOINTS[category].endpoints).forEach(endpoint => {
        const config = API_ENDPOINTS[category].endpoints[endpoint];
        if (config.permissions && config.permissions.length > 0) {
          result[category][endpoint] = config;
        }
      });
    });
    return result;
  }
};

// 预设的端点组合
const EndpointPresets = {
  // 基础CRUD操作
  CRUD: {
    LIST: 'LIST',
    CREATE: 'CREATE', 
    DETAIL: 'DETAIL',
    UPDATE: 'UPDATE',
    DELETE: 'DELETE'
  },

  // 常用业务操作
  BUSINESS: {
    STATISTICS: 'STATISTICS',
    REPORTS: 'REPORTS',
    EXPORT: 'EXPORT',
    IMPORT: 'IMPORT'
  },

  // 认证相关
  AUTH: {
    LOGIN: 'LOGIN',
    LOGOUT: 'LOGOUT',
    REFRESH: 'REFRESH_TOKEN',
    USER_INFO: 'USER_INFO'
  }
};

// 主要导出对象（保持向后兼容）
module.exports = {
  // 核心类和实例
  ApiEndpointBuilder: ExtendedApiEndpointBuilder,
  defaultBuilder,

  // 端点配置
  API_ENDPOINTS,
  API_VERSIONS,

  // 便捷方法
  ...endpoints,

  // 工具方法
  EndpointUtils,
  EndpointPresets,

  // 向后兼容的导出
  endpoints,
  
  // 重构信息
  __refactored: true,
  __version: '2.0',
  __originalSize: '910 lines',
  __newSize: '5 modules',
  __modules: [
    'utils/api-endpoints/auth-endpoints.js',
    'utils/api-endpoints/business-endpoints.js',
    'utils/api-endpoints/management-endpoints.js',
    'utils/api-endpoints/system-endpoints.js',
    'utils/api-endpoints/endpoint-builder.js'
  ]
};
