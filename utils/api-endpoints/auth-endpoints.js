/**
 * 认证相关API端点定义
 * Authentication API Endpoints
 */

const AUTH_ENDPOINTS = {
  base: '/auth',
  version: '/api/v2',
  endpoints: {
    LOGIN: {
      path: '/login',
      method: 'POST',
      description: '用户登录',
      permissions: null, // 无需权限
      cache: false
    },
    REGISTER: {
      path: '/register', 
      method: 'POST',
      description: '用户注册',
      permissions: null,
      cache: false
    },
    LOGOUT: {
      path: '/logout',
      method: 'POST',
      description: '用户登出',
      permissions: null,
      cache: false
    },
    REFRESH_TOKEN: {
      path: '/refresh',
      method: 'POST',
      description: '刷新访问令牌',
      permissions: null,
      cache: false
    },
    USER_INFO: {
      path: '/userinfo',
      method: 'GET',
      description: '获取用户信息',
      permissions: ['USER_READ'],
      cache: true,
      cacheTTL: 300 // 5分钟
    },
    CHANGE_PASSWORD: {
      path: '/password',
      method: 'PUT',
      description: '修改密码',
      permissions: ['USER_UPDATE'],
      cache: false
    },
    RESET_PASSWORD: {
      path: '/reset-password',
      method: 'POST',
      description: '重置密码',
      permissions: null,
      cache: false
    },
    VERIFY_EMAIL: {
      path: '/verify-email',
      method: 'POST',
      description: '验证邮箱',
      permissions: null,
      cache: false
    },
    SEND_VERIFICATION: {
      path: '/send-verification',
      method: 'POST',
      description: '发送验证码',
      permissions: null,
      cache: false
    }
  }
};

module.exports = {
  AUTH_ENDPOINTS
};
