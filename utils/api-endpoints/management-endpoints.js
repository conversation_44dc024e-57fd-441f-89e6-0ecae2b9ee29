/**
 * 管理相关API端点定义
 * Management API Endpoints (Users, Inventory, Finance, OA)
 */

const USERS_ENDPOINTS = {
  base: '/users',
  version: '/api/v2',
  endpoints: {
    LIST: {
      path: '',
      method: 'GET',
      description: '获取用户列表',
      permissions: ['USER_MANAGE'],
      cache: true,
      cacheTTL: 300
    },
    CREATE: {
      path: '',
      method: 'POST',
      description: '创建新用户',
      permissions: ['USER_CREATE'],
      cache: false
    },
    DETAIL: {
      path: '/:id',
      method: 'GET',
      description: '获取用户详情',
      permissions: ['USER_READ'],
      cache: true,
      cacheTTL: 300
    },
    UPDATE: {
      path: '/:id',
      method: 'PUT',
      description: '更新用户信息',
      permissions: ['USER_UPDATE'],
      cache: false
    },
    DELETE: {
      path: '/:id',
      method: 'DELETE',
      description: '删除用户',
      permissions: ['USER_DELETE'],
      cache: false
    },
    PROFILE: {
      path: '/:id/profile',
      method: 'GET',
      description: '获取用户档案',
      permissions: ['USER_READ'],
      cache: true,
      cacheTTL: 600
    },
    PERMISSIONS: {
      path: '/:id/permissions',
      method: 'GET',
      description: '获取用户权限',
      permissions: ['USER_MANAGE'],
      cache: true,
      cacheTTL: 300
    },
    ASSIGN_ROLE: {
      path: '/:id/role',
      method: 'PUT',
      description: '分配用户角色',
      permissions: ['ROLE_ASSIGN'],
      cache: false
    }
  }
};

const INVENTORY_ENDPOINTS = {
  base: '/inventory',
  version: '/api/v2',
  endpoints: {
    LIST: {
      path: '',
      method: 'GET',
      description: '获取库存列表',
      permissions: ['INVENTORY_READ'],
      cache: true,
      cacheTTL: 180
    },
    CREATE: {
      path: '',
      method: 'POST',
      description: '创建库存项目',
      permissions: ['INVENTORY_CREATE'],
      cache: false
    },
    DETAIL: {
      path: '/:id',
      method: 'GET',
      description: '获取库存详情',
      permissions: ['INVENTORY_READ'],
      cache: true,
      cacheTTL: 300
    },
    UPDATE: {
      path: '/:id',
      method: 'PUT',
      description: '更新库存信息',
      permissions: ['INVENTORY_UPDATE'],
      cache: false
    },
    DELETE: {
      path: '/:id',
      method: 'DELETE',
      description: '删除库存项目',
      permissions: ['INVENTORY_DELETE'],
      cache: false
    },
    CATEGORIES: {
      path: '/categories',
      method: 'GET',
      description: '获取库存分类',
      permissions: ['INVENTORY_READ'],
      cache: true,
      cacheTTL: 1800
    },
    LOW_STOCK: {
      path: '/low-stock',
      method: 'GET',
      description: '获取低库存警告',
      permissions: ['INVENTORY_READ'],
      cache: true,
      cacheTTL: 300
    },
    STATISTICS: {
      path: '/statistics',
      method: 'GET',
      description: '获取库存统计',
      permissions: ['INVENTORY_READ'],
      cache: true,
      cacheTTL: 600
    }
  }
};

const FINANCE_ENDPOINTS = {
  base: '/finance',
  version: '/api/v2',
  endpoints: {
    RECORDS: {
      path: '/records',
      method: 'GET',
      description: '获取财务记录',
      permissions: ['FINANCE_READ'],
      cache: true,
      cacheTTL: 300
    },
    CREATE_RECORD: {
      path: '/records',
      method: 'POST',
      description: '创建财务记录',
      permissions: ['FINANCE_CREATE'],
      cache: false
    },
    REPORTS: {
      path: '/reports',
      method: 'GET',
      description: '获取财务报表',
      permissions: ['FINANCE_READ'],
      cache: true,
      cacheTTL: 1800
    },
    EXPORT: {
      path: '/export',
      method: 'GET',
      description: '导出财务数据',
      permissions: ['FINANCE_EXPORT'],
      cache: false
    },
    REIMBURSEMENTS: {
      path: '/reimbursements',
      method: 'GET',
      description: '获取报销申请',
      permissions: ['FINANCE_READ'],
      cache: true,
      cacheTTL: 300
    },
    CREATE_REIMBURSEMENT: {
      path: '/reimbursements',
      method: 'POST',
      description: '创建报销申请',
      permissions: ['FINANCE_CREATE'],
      cache: false
    },
    APPROVE_REIMBURSEMENT: {
      path: '/reimbursements/:id/approve',
      method: 'PUT',
      description: '审批报销申请',
      permissions: ['FINANCE_APPROVE'],
      cache: false
    }
  }
};

const OA_ENDPOINTS = {
  base: '/oa',
  version: '/api/v2',
  endpoints: {
    TASKS: {
      path: '/tasks',
      method: 'GET',
      description: '获取任务列表',
      permissions: ['OA_READ'],
      cache: true,
      cacheTTL: 300
    },
    CREATE_TASK: {
      path: '/tasks',
      method: 'POST',
      description: '创建新任务',
      permissions: ['OA_CREATE'],
      cache: false
    },
    TASK_DETAIL: {
      path: '/tasks/:id',
      method: 'GET',
      description: '获取任务详情',
      permissions: ['OA_READ'],
      cache: true,
      cacheTTL: 300
    },
    UPDATE_TASK: {
      path: '/tasks/:id',
      method: 'PUT',
      description: '更新任务',
      permissions: ['OA_UPDATE'],
      cache: false
    },
    ASSIGN_TASK: {
      path: '/tasks/:id/assign',
      method: 'PUT',
      description: '分配任务',
      permissions: ['OA_ASSIGN'],
      cache: false
    },
    APPROVALS: {
      path: '/approvals',
      method: 'GET',
      description: '获取审批列表',
      permissions: ['OA_APPROVE'],
      cache: true,
      cacheTTL: 300
    },
    PROCESS_APPROVAL: {
      path: '/approvals/:id/process',
      method: 'PUT',
      description: '处理审批',
      permissions: ['OA_APPROVE'],
      cache: false
    }
  }
};

module.exports = {
  USERS_ENDPOINTS,
  INVENTORY_ENDPOINTS,
  FINANCE_ENDPOINTS,
  OA_ENDPOINTS
};
