/**
 * 系统和平台相关API端点定义
 * System and Platform API Endpoints
 */

const SHOP_ENDPOINTS = {
  base: '/shop',
  version: '/api/v2',
  endpoints: {
    PRODUCTS: {
      path: '/products',
      method: 'GET',
      description: '获取商品列表',
      permissions: ['SHOP_READ'],
      cache: true,
      cacheTTL: 600
    },
    PRODUCT_DETAIL: {
      path: '/products/:id',
      method: 'GET',
      description: '获取商品详情',
      permissions: ['SHOP_READ'],
      cache: true,
      cacheTTL: 1800
    },
    CREATE_PRODUCT: {
      path: '/products',
      method: 'POST',
      description: '创建商品',
      permissions: ['SHOP_CREATE'],
      cache: false
    },
    UPDATE_PRODUCT: {
      path: '/products/:id',
      method: 'PUT',
      description: '更新商品',
      permissions: ['SHOP_UPDATE'],
      cache: false
    },
    DELETE_PRODUCT: {
      path: '/products/:id',
      method: 'DELETE',
      description: '删除商品',
      permissions: ['SHOP_DELETE'],
      cache: false
    },
    ORDERS: {
      path: '/orders',
      method: 'GET',
      description: '获取订单列表',
      permissions: ['ORDER_READ'],
      cache: true,
      cacheTTL: 300
    },
    CREATE_ORDER: {
      path: '/orders',
      method: 'POST',
      description: '创建订单',
      permissions: ['ORDER_CREATE'],
      cache: false
    },
    ORDER_DETAIL: {
      path: '/orders/:id',
      method: 'GET',
      description: '获取订单详情',
      permissions: ['ORDER_READ'],
      cache: true,
      cacheTTL: 300
    }
  }
};

const AI_ENDPOINTS = {
  base: '/ai',
  version: '/api/v2',
  endpoints: {
    CHAT: {
      path: '/chat',
      method: 'POST',
      description: 'AI对话',
      permissions: ['AI_CHAT'],
      cache: false
    },
    DIAGNOSIS: {
      path: '/diagnosis',
      method: 'POST',
      description: 'AI诊断',
      permissions: ['AI_DIAGNOSIS'],
      cache: false
    },
    IMAGE_RECOGNITION: {
      path: '/image-recognition',
      method: 'POST',
      description: 'AI图像识别',
      permissions: ['AI_IMAGE'],
      cache: false
    },
    CONFIG: {
      path: '/config',
      method: 'GET',
      description: '获取AI配置',
      permissions: ['AI_CONFIG'],
      cache: true,
      cacheTTL: 1800
    },
    UPDATE_CONFIG: {
      path: '/config',
      method: 'PUT',
      description: '更新AI配置',
      permissions: ['AI_CONFIG'],
      cache: false
    },
    STATISTICS: {
      path: '/statistics',
      method: 'GET',
      description: '获取AI使用统计',
      permissions: ['AI_STATS'],
      cache: true,
      cacheTTL: 600
    }
  }
};

const SYSTEM_ENDPOINTS = {
  base: '/system',
  version: '/api/v2',
  endpoints: {
    SETTINGS: {
      path: '/settings',
      method: 'GET',
      description: '获取系统设置',
      permissions: ['SYSTEM_READ'],
      cache: true,
      cacheTTL: 1800
    },
    UPDATE_SETTINGS: {
      path: '/settings',
      method: 'PUT',
      description: '更新系统设置',
      permissions: ['SYSTEM_UPDATE'],
      cache: false
    },
    ANNOUNCEMENTS: {
      path: '/announcements',
      method: 'GET',
      description: '获取系统公告',
      permissions: null,
      cache: true,
      cacheTTL: 3600
    },
    DATA_EXPORT: {
      path: '/export',
      method: 'POST',
      description: '数据导出',
      permissions: ['DATA_EXPORT'],
      cache: false
    },
    DATA_IMPORT: {
      path: '/import',
      method: 'POST',
      description: '数据导入',
      permissions: ['DATA_IMPORT'],
      cache: false
    },
    BACKUP: {
      path: '/backup',
      method: 'POST',
      description: '系统备份',
      permissions: ['SYSTEM_BACKUP'],
      cache: false
    }
  }
};

const PLATFORM_ENDPOINTS = {
  base: '/platform',
  version: '/api/v1/platform',
  endpoints: {
    TENANTS: {
      path: '/tenants',
      method: 'GET',
      description: '获取租户列表',
      permissions: ['PLATFORM_ADMIN'],
      cache: true,
      cacheTTL: 300
    },
    CREATE_TENANT: {
      path: '/tenants',
      method: 'POST',
      description: '创建租户',
      permissions: ['PLATFORM_ADMIN'],
      cache: false
    },
    TENANT_DETAIL: {
      path: '/tenants/:id',
      method: 'GET',
      description: '获取租户详情',
      permissions: ['PLATFORM_ADMIN'],
      cache: true,
      cacheTTL: 600
    },
    UPDATE_TENANT: {
      path: '/tenants/:id',
      method: 'PUT',
      description: '更新租户信息',
      permissions: ['PLATFORM_ADMIN'],
      cache: false
    },
    SUSPEND_TENANT: {
      path: '/tenants/:id/suspend',
      method: 'PUT',
      description: '暂停租户',
      permissions: ['PLATFORM_ADMIN'],
      cache: false
    },
    ACTIVATE_TENANT: {
      path: '/tenants/:id/activate',
      method: 'PUT',
      description: '激活租户',
      permissions: ['PLATFORM_ADMIN'],
      cache: false
    },
    PLATFORM_ANALYTICS: {
      path: '/analytics',
      method: 'GET',
      description: '平台分析数据',
      permissions: ['PLATFORM_ADMIN'],
      cache: true,
      cacheTTL: 1800
    },
    SUBSCRIPTION_PLANS: {
      path: '/subscription-plans',
      method: 'GET',
      description: '获取订阅计划',
      permissions: ['PLATFORM_ADMIN'],
      cache: true,
      cacheTTL: 3600
    },
    SYSTEM_MONITOR: {
      path: '/monitor',
      method: 'GET',
      description: '系统监控',
      permissions: ['PLATFORM_ADMIN'],
      cache: true,
      cacheTTL: 60
    }
  }
};

module.exports = {
  SHOP_ENDPOINTS,
  AI_ENDPOINTS,
  SYSTEM_ENDPOINTS,
  PLATFORM_ENDPOINTS
};
