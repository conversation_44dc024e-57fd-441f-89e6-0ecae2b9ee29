/**
 * 数据库查询优化器
 */

class QueryOptimizer {
  constructor() {
    this.queryCache = new Map();
    this.slowQueryThreshold = 200; // 200ms
  }

  /**
   * 优化查询 - 添加缓存和性能监控
   */
  async optimizedQuery(key, queryFunction, options = {}) {
    const startTime = Date.now();
    const { useCache = true, cacheTTL = 300000 } = options;
    
    try {
      // 检查缓存
      if (useCache) {
        const cached = this.queryCache.get(key);
        if (cached && cached.expiry > Date.now()) {
          console.log(`📊 查询缓存命中: ${key}`);
          return cached.data;
        }
      }
      
      // 执行查询
      const result = await queryFunction();
      const duration = Date.now() - startTime;
      
      // 记录慢查询
      if (duration > this.slowQueryThreshold) {
        console.warn(`🐌 慢查询检测: ${key} 耗时 ${duration}ms`);
      }
      
      // 缓存结果
      if (useCache && result) {
        this.queryCache.set(key, {
          data: result,
          expiry: Date.now() + cacheTTL
        });
      }
      
      return result;
    } catch (error) {
      console.error(`查询错误 ${key}:`, error);
      throw error;
    }
  }

  /**
   * 批量查询优化
   */
  async batchQuery(queries) {
    const startTime = Date.now();
    
    try {
      const results = await Promise.allSettled(
        queries.map(({ key, queryFunction, options }) => 
          this.optimizedQuery(key, queryFunction, options)
        )
      );
      
      const duration = Date.now() - startTime;
      console.log(`📊 批量查询完成: ${queries.length}个查询, 耗时 ${duration}ms`);
      
      return results;
    } catch (error) {
      console.error('批量查询错误:', error);
      throw error;
    }
  }

  /**
   * 清理过期缓存
   */
  cleanupCache() {
    const now = Date.now();
    for (const [key, value] of this.queryCache.entries()) {
      if (value.expiry <= now) {
        this.queryCache.delete(key);
      }
    }
  }
}

const queryOptimizer = new QueryOptimizer();

// 定期清理缓存
setInterval(() => {
  queryOptimizer.cleanupCache();
}, 5 * 60 * 1000); // 每5分钟清理一次

module.exports = {
  QueryOptimizer,
  queryOptimizer
};