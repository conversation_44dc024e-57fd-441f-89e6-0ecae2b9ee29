/**
 * 统一API接口规范
 * Unified API Interface Standards
 * 
 * 功能：
 * - 统一的云函数接口规范
 * - 多租户数据访问控制
 * - API版本管理
 * - 请求响应标准化
 * - 错误处理统一化
 */

const { DataIsolationMiddleware, DataAccessLogger } = require('./data-isolation');
const { getCurrentUserInfo } = require('./auth');

/**
 * API版本配置
 */
const API_VERSIONS = {
  V1: 'v1',
  V2: 'v2',
  CURRENT: 'v2'
};

/**
 * 标准响应格式
 */
const RESPONSE_FORMAT = {
  SUCCESS: {
    success: true,
    code: 200,
    message: '操作成功',
    data: null,
    timestamp: null,
    version: API_VERSIONS.CURRENT
  },
  ERROR: {
    success: false,
    code: 500,
    message: '操作失败',
    error: null,
    timestamp: null,
    version: API_VERSIONS.CURRENT
  }
};

/**
 * 错误代码定义
 */
const ERROR_CODES = {
  // 认证相关
  AUTH_REQUIRED: 401,
  AUTH_INVALID: 401,
  AUTH_EXPIRED: 401,
  
  // 权限相关
  PERMISSION_DENIED: 403,
  TENANT_ACCESS_DENIED: 403,
  
  // 参数相关
  INVALID_PARAMS: 400,
  MISSING_PARAMS: 400,
  INVALID_FORMAT: 400,
  
  // 资源相关
  RESOURCE_NOT_FOUND: 404,
  RESOURCE_CONFLICT: 409,
  RESOURCE_LOCKED: 423,
  
  // 业务相关
  BUSINESS_ERROR: 422,
  QUOTA_EXCEEDED: 429,
  
  // 系统相关
  INTERNAL_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
  DATABASE_ERROR: 500
};

/**
 * API标准化处理器
 */
class APIStandardHandler {
  constructor(event, context) {
    this.event = event;
    this.context = context;
    this.version = event.version || API_VERSIONS.CURRENT;
    this.startTime = Date.now();
  }

  /**
   * 处理API请求
   */
  async handle(handler) {
    try {
      // 1. 参数验证
      const validatedParams = await this.validateParams();
      
      // 2. 用户认证
      const user = await this.authenticateUser();
      
      // 3. 权限检查
      await this.checkPermissions(user);
      
      // 4. 数据隔离处理
      const secureContext = this.createSecureContext(user);
      
      // 5. 执行业务逻辑
      const result = await handler(validatedParams, secureContext);
      
      // 6. 记录访问日志
      await this.logAccess(user, 'success', result);
      
      // 7. 返回标准响应
      return this.createSuccessResponse(result);
      
    } catch (error) {
      // 错误处理和日志记录
      await this.logAccess(null, 'error', error);
      return this.createErrorResponse(error);
    }
  }

  /**
   * 参数验证
   */
  async validateParams() {
    const { action, data = {}, page = 1, limit = 20 } = this.event;
    
    if (!action) {
      throw new APIError('缺少必要参数：action', ERROR_CODES.MISSING_PARAMS);
    }

    // 分页参数验证
    const validatedPage = Math.max(1, parseInt(page));
    const validatedLimit = Math.min(100, Math.max(1, parseInt(limit)));

    return {
      action,
      data,
      page: validatedPage,
      limit: validatedLimit,
      ...this.event
    };
  }

  /**
   * 用户认证
   */
  async authenticateUser() {
    try {
      const user = await getCurrentUserInfo(this.context.OPENID);
      
      if (!user) {
        throw new APIError('用户未认证', ERROR_CODES.AUTH_REQUIRED);
      }

      if (user.status !== 'active') {
        throw new APIError('用户账户已被禁用', ERROR_CODES.AUTH_INVALID);
      }

      return user;
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      throw new APIError('认证失败', ERROR_CODES.AUTH_INVALID);
    }
  }

  /**
   * 权限检查
   */
  async checkPermissions(user) {
    // 检查租户状态
    if (user.tenant_id) {
      const tenant = await this.getTenantInfo(user.tenant_id);
      
      if (!tenant || tenant.status !== 'active') {
        throw new APIError('租户账户已被暂停', ERROR_CODES.TENANT_ACCESS_DENIED);
      }

      // 检查订阅状态
      if (tenant.subscription_end && new Date(tenant.subscription_end) < new Date()) {
        throw new APIError('租户订阅已过期', ERROR_CODES.TENANT_ACCESS_DENIED);
      }
    }

    // 具体的权限检查由业务逻辑处理
    return true;
  }

  /**
   * 创建安全上下文
   */
  createSecureContext(user) {
    return {
      user,
      version: this.version,
      requestId: this.generateRequestId(),
      timestamp: new Date(),
      
      // 数据访问方法
      secureQuery: (collection, query = {}) => {
        return DataIsolationMiddleware.createSecureQuery(user, collection, query);
      },
      
      // 数据验证方法
      validateData: (collection, data) => {
        return DataIsolationMiddleware.validateWriteData(user, collection, data);
      },
      
      // 日志记录方法
      logDataAccess: (collection, operation, query, result) => {
        return DataAccessLogger.logDataAccess(user, collection, operation, query, result);
      }
    };
  }

  /**
   * 创建成功响应
   */
  createSuccessResponse(data) {
    return {
      ...RESPONSE_FORMAT.SUCCESS,
      data,
      timestamp: new Date().toISOString(),
      version: this.version,
      requestId: this.generateRequestId(),
      executionTime: Date.now() - this.startTime
    };
  }

  /**
   * 创建错误响应
   */
  createErrorResponse(error) {
    const response = {
      ...RESPONSE_FORMAT.ERROR,
      timestamp: new Date().toISOString(),
      version: this.version,
      requestId: this.generateRequestId(),
      executionTime: Date.now() - this.startTime
    };

    if (error instanceof APIError) {
      response.code = error.code;
      response.message = error.message;
      response.error = error.details;
    } else {
      response.code = ERROR_CODES.INTERNAL_ERROR;
      response.message = '系统内部错误';
      response.error = process.env.NODE_ENV === 'development' ? error.message : null;
    }

    return response;
  }

  /**
   * 获取租户信息
   */
  async getTenantInfo(tenantId) {
    try {
      const cloud = require('wx-server-sdk');
      const db = cloud.database();
      
      const result = await db.collection('tenants').doc(tenantId).get();
      return result.data;
    } catch (error) {
      console.error('获取租户信息失败:', error);
      return null;
    }
  }

  /**
   * 记录访问日志
   */
  async logAccess(user, status, result) {
    try {
      const logData = {
        requestId: this.generateRequestId(),
        user_id: user?._id,
        tenant_id: user?.tenant_id,
        action: this.event.action,
        status,
        executionTime: Date.now() - this.startTime,
        timestamp: new Date(),
        version: this.version
      };

      if (status === 'error') {
        logData.error = result.message;
        logData.errorCode = result.code;
      }

      // 记录到日志集合
      const cloud = require('wx-server-sdk');
      const db = cloud.database();
      
      await db.collection('api_access_logs').add({
        data: logData
      });
    } catch (error) {
      console.error('记录API访问日志失败:', error);
    }
  }

  /**
   * 生成请求ID
   */
  generateRequestId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * API错误类
 */
class APIError extends Error {
  constructor(message, code = ERROR_CODES.INTERNAL_ERROR, details = null) {
    super(message);
    this.name = 'APIError';
    this.code = code;
    this.details = details;
  }
}

/**
 * API装饰器 - 用于云函数
 */
function apiHandler(handler) {
  return async (event, context) => {
    const apiHandler = new APIStandardHandler(event, context);
    return await apiHandler.handle(handler);
  };
}

/**
 * 分页辅助函数
 */
class PaginationHelper {
  static createPaginationResponse(data, page, limit, total) {
    return {
      list: data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(total),
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  static validatePaginationParams(page, limit) {
    const validatedPage = Math.max(1, parseInt(page) || 1);
    const validatedLimit = Math.min(100, Math.max(1, parseInt(limit) || 20));
    
    return {
      page: validatedPage,
      limit: validatedLimit,
      skip: (validatedPage - 1) * validatedLimit
    };
  }
}

/**
 * 数据验证辅助函数
 */
class ValidationHelper {
  static validateRequired(data, fields) {
    const missing = fields.filter(field => !data[field]);
    if (missing.length > 0) {
      throw new APIError(`缺少必要参数：${missing.join(', ')}`, ERROR_CODES.MISSING_PARAMS);
    }
  }

  static validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new APIError('邮箱格式不正确', ERROR_CODES.INVALID_FORMAT);
    }
  }

  static validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      throw new APIError('手机号格式不正确', ERROR_CODES.INVALID_FORMAT);
    }
  }

  static validateLength(value, min, max, fieldName) {
    if (value.length < min || value.length > max) {
      throw new APIError(`${fieldName}长度必须在${min}-${max}之间`, ERROR_CODES.INVALID_FORMAT);
    }
  }
}

module.exports = {
  API_VERSIONS,
  ERROR_CODES,
  APIStandardHandler,
  APIError,
  apiHandler,
  PaginationHelper,
  ValidationHelper
};
