/**
 * 环境检测工具
 * 统一处理不同环境下的差异
 */

const MiniProgramCompatibility = require('./miniprogram-compatibility.js');

class EnvironmentDetector {
  static detect() {
    const env = {
      isMiniProgram: MiniProgramCompatibility.isMiniProgram(),
      isDevelopment: MiniProgramCompatibility.isDevelopment(),
      info: MiniProgramCompatibility.getEnvironmentInfo()
    };
    
    console.log('🔍 环境检测结果:', env);
    return env;
  }

  static getConfig() {
    const env = this.detect();
    
    return {
      // API配置
      baseURL: env.isDevelopment ? 'http://localhost:3001' : 'https://api.zhihuiyange.com',
      timeout: env.isMiniProgram ? 10000 : 5000,
      
      // 日志配置
      enableConsoleLog: env.isDevelopment,
      enableErrorReport: !env.isDevelopment,
      
      // 缓存配置
      cacheEnabled: true,
      cacheTTL: env.isMiniProgram ? 300000 : 600000, // 小程序缓存时间更短
      
      // 性能配置
      enablePerformanceMonitor: env.isDevelopment,
      performanceThreshold: env.isMiniProgram ? 200 : 100
    };
  }
}

module.exports = EnvironmentDetector;