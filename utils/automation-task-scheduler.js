/**
 * 自动化任务调度器
 */

class AutomationTaskScheduler {
  constructor() {
    this.tasks = new Map();
    this.runningTasks = new Set();
    this.taskHistory = [];
  }

  /**
   * 注册自动化任务
   */
  registerTask(name, taskFunction, schedule = {}) {
    this.tasks.set(name, {
      name,
      function: taskFunction,
      schedule,
      lastRun: null,
      nextRun: this.calculateNextRun(schedule),
      enabled: true
    });
    
    console.log(`📋 注册自动化任务: ${name}`);
  }

  /**
   * 执行任务
   */
  async executeTask(taskName, force = false) {
    const task = this.tasks.get(taskName);
    if (!task) {
      throw new Error(`任务不存在: ${taskName}`);
    }
    
    if (!force && !task.enabled) {
      console.log(`⏸️ 任务已禁用: ${taskName}`);
      return;
    }
    
    if (this.runningTasks.has(taskName)) {
      console.log(`⏳ 任务正在运行: ${taskName}`);
      return;
    }
    
    this.runningTasks.add(taskName);
    const startTime = Date.now();
    
    try {
      console.log(`🚀 开始执行任务: ${taskName}`);
      const result = await task.function();
      
      const duration = Date.now() - startTime;
      task.lastRun = new Date();
      task.nextRun = this.calculateNextRun(task.schedule);
      
      // 记录任务历史
      this.taskHistory.push({
        name: taskName,
        startTime: new Date(startTime),
        endTime: new Date(),
        duration,
        success: true,
        result
      });
      
      console.log(`✅ 任务执行成功: ${taskName}, 耗时: ${duration}ms`);
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // 记录失败历史
      this.taskHistory.push({
        name: taskName,
        startTime: new Date(startTime),
        endTime: new Date(),
        duration,
        success: false,
        error: error.message
      });
      
      console.error(`❌ 任务执行失败: ${taskName}`, error);
      throw error;
      
    } finally {
      this.runningTasks.delete(taskName);
    }
  }

  /**
   * 启动调度器
   */
  start() {
    console.log('🎯 启动任务调度器');
    
    // 每分钟检查一次待执行任务
    setInterval(() => {
      this.checkAndExecuteTasks();
    }, 60 * 1000);
  }

  /**
   * 检查并执行待执行任务
   */
  async checkAndExecuteTasks() {
    const now = new Date();
    
    for (const [name, task] of this.tasks.entries()) {
      if (task.enabled && task.nextRun && now >= task.nextRun) {
        try {
          await this.executeTask(name);
        } catch (error) {
          console.error(`定时任务执行失败: ${name}`, error);
        }
      }
    }
  }

  /**
   * 计算下次运行时间
   */
  calculateNextRun(schedule) {
    if (!schedule.interval) return null;
    
    const now = new Date();
    return new Date(now.getTime() + schedule.interval);
  }

  /**
   * 获取任务状态
   */
  getTaskStatus() {
    const status = {};
    
    for (const [name, task] of this.tasks.entries()) {
      status[name] = {
        enabled: task.enabled,
        lastRun: task.lastRun,
        nextRun: task.nextRun,
        isRunning: this.runningTasks.has(name)
      };
    }
    
    return status;
  }
}

// 创建全局任务调度器
const taskScheduler = new AutomationTaskScheduler();

// 注册常用自动化任务
taskScheduler.registerTask('dataBackup', async () => {
  console.log('📦 执行数据备份...');
  // 数据备份逻辑
  return { success: true, backupSize: '10MB' };
}, { interval: 24 * 60 * 60 * 1000 }); // 每天执行

taskScheduler.registerTask('cacheCleanup', async () => {
  console.log('🧹 清理缓存...');
  // 缓存清理逻辑
  return { success: true, cleanedItems: 50 };
}, { interval: 6 * 60 * 60 * 1000 }); // 每6小时执行

module.exports = {
  AutomationTaskScheduler,
  taskScheduler
};