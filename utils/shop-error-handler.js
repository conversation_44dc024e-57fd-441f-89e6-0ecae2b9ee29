/**
 * 商城模块错误处理工具
 * 专门处理商城相关的API错误和用户交互
 */

class ShopErrorHandler {
  /**
   * 处理商品详情加载错误
   */
  static handleProductDetailError(error, productId) {
    console.error('商品详情加载失败:', error);
    
    const errorMessages = {
      'PRODUCT_NOT_FOUND': '商品不存在或已下架',
      'ACCESS_DENIED': '无权限访问该商品',
      'NETWORK_ERROR': '网络连接失败，请检查网络',
      'TIMEOUT': '请求超时，请重试',
      'SERVER_ERROR': '服务器错误，请稍后重试',
      'INVALID_PRODUCT_ID': '商品ID无效'
    };
    
    const message = errorMessages[error.code] || '加载失败，请重试';
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
    
    return {
      shouldRetry: ['NETWORK_ERROR', 'TIMEOUT', 'SERVER_ERROR'].includes(error.code),
      shouldGoBack: ['PRODUCT_NOT_FOUND', 'ACCESS_DENIED', 'INVALID_PRODUCT_ID'].includes(error.code),
      shouldUseMockData: ['PRODUCT_NOT_FOUND', 'NETWORK_ERROR'].includes(error.code),
      message
    };
  }

  /**
   * 处理API调用错误
   */
  static handleAPIError(error, context = '') {
    console.error(`商城API错误 [${context}]:`, error);
    
    // 根据HTTP状态码判断错误类型
    if (error.status === 404) {
      return this.handleProductDetailError({ code: 'PRODUCT_NOT_FOUND' });
    }
    
    if (error.status === 403) {
      return this.handleProductDetailError({ code: 'ACCESS_DENIED' });
    }
    
    if (error.status >= 500) {
      return this.handleProductDetailError({ code: 'SERVER_ERROR' });
    }
    
    if (error.code === 'TIMEOUT') {
      return this.handleProductDetailError({ code: 'TIMEOUT' });
    }
    
    if (error.code === 'NETWORK_ERROR') {
      return this.handleProductDetailError({ code: 'NETWORK_ERROR' });
    }
    
    return this.handleProductDetailError({ code: 'UNKNOWN_ERROR' });
  }

  /**
   * 处理商品列表加载错误
   */
  static handleProductListError(error) {
    console.error('商品列表加载失败:', error);
    
    wx.showToast({
      title: '商品列表加载失败',
      icon: 'none',
      duration: 2000
    });
    
    return {
      shouldRetry: true,
      shouldUseMockData: true
    };
  }

  /**
   * 处理购物车操作错误
   */
  static handleCartError(error, operation = 'unknown') {
    console.error(`购物车操作失败 [${operation}]:`, error);
    
    const operationMessages = {
      'add': '添加到购物车失败',
      'update': '更新购物车失败',
      'remove': '移除商品失败',
      'get': '获取购物车失败'
    };
    
    const message = operationMessages[operation] || '购物车操作失败';
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
    
    return {
      shouldRetry: error.status >= 500,
      shouldUseLocalStorage: true
    };
  }

  /**
   * 处理订单相关错误
   */
  static handleOrderError(error, operation = 'unknown') {
    console.error(`订单操作失败 [${operation}]:`, error);
    
    const operationMessages = {
      'create': '创建订单失败',
      'pay': '支付失败',
      'cancel': '取消订单失败',
      'get': '获取订单失败'
    };
    
    const message = operationMessages[operation] || '订单操作失败';
    
    wx.showModal({
      title: '操作失败',
      content: message,
      showCancel: false,
      confirmText: '确定'
    });
    
    return {
      shouldRetry: error.status >= 500,
      shouldGoBack: operation === 'create' && error.status === 400
    };
  }

  /**
   * 显示网络错误提示
   */
  static showNetworkError() {
    wx.showModal({
      title: '网络错误',
      content: '网络连接失败，请检查网络设置后重试',
      showCancel: true,
      cancelText: '取消',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          // 返回重试标识
          return { shouldRetry: true };
        }
      }
    });
  }

  /**
   * 显示权限错误提示
   */
  static showPermissionError() {
    wx.showModal({
      title: '权限不足',
      content: '您没有权限执行此操作，请联系管理员',
      showCancel: false,
      confirmText: '确定'
    });
  }

  /**
   * 显示服务器错误提示
   */
  static showServerError() {
    wx.showModal({
      title: '服务器错误',
      content: '服务器暂时无法响应，请稍后重试',
      showCancel: true,
      cancelText: '取消',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          return { shouldRetry: true };
        }
      }
    });
  }

  /**
   * 通用错误处理方法
   */
  static handleError(error, context = '', options = {}) {
    const {
      showToast = true,
      showModal = false,
      autoRetry = false,
      useMockData = false
    } = options;
    
    console.error(`商城错误 [${context}]:`, error);
    
    let errorInfo = this.handleAPIError(error, context);
    
    if (showModal && !showToast) {
      wx.showModal({
        title: '操作失败',
        content: errorInfo.message,
        showCancel: errorInfo.shouldRetry,
        cancelText: '取消',
        confirmText: errorInfo.shouldRetry ? '重试' : '确定'
      });
    }
    
    return {
      ...errorInfo,
      autoRetry,
      useMockData
    };
  }
}

module.exports = ShopErrorHandler;
