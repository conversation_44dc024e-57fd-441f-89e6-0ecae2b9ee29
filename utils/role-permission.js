/**
 * 增强的角色权限管理系统
 * Enhanced Role and Permission Management System
 * 
 * 统一管理微信小程序中的角色和权限控制，实现基于角色的访问控制(RBAC)
 */

const { getCurrentUserInfo } = require('./user-info');

/**
 * 用户角色定义（支持层级结构）
 */
const ROLES = {
  // 系统级角色（最高权限）
  SUPER_ADMIN: 'super_admin',
  PLATFORM_ADMIN: 'platform_admin',
  
  // 租户级角色（管理层）
  TENANT_OWNER: 'owner',
  ADMIN: 'admin',
  MANAGER: 'manager',
  
  // 专业角色（专业权限）
  FINANCE_MANAGER: 'finance_manager',
  FINANCE_STAFF: 'finance_staff',
  HR_MANAGER: 'hr_manager',
  VETERINARIAN: 'veterinarian',
  
  // 基础角色（标准权限）
  EMPLOYEE: 'employee',
  USER: 'user',
  VIEWER: 'viewer'
};

/**
 * 权限定义（按功能模块分组）
 */
const PERMISSIONS = {
  // ========== 平台级管理权限 ==========
  // 今日鹅价管理权限
  PLATFORM_GOOSE_PRICE_VIEW: 'platform.goose_price.view',       // 查看鹅价信息
  PLATFORM_GOOSE_PRICE_MANAGE: 'platform.goose_price.manage',   // 管理鹅价信息
  PLATFORM_GOOSE_PRICE_PUBLISH: 'platform.goose_price.publish', // 发布鹅价信息

  // 平台公告管理权限
  PLATFORM_ANNOUNCEMENT_VIEW: 'platform.announcement.view',     // 查看平台公告
  PLATFORM_ANNOUNCEMENT_MANAGE: 'platform.announcement.manage', // 管理平台公告
  PLATFORM_ANNOUNCEMENT_PUBLISH: 'platform.announcement.publish', // 发布平台公告

  // 知识库管理权限
  PLATFORM_KNOWLEDGE_VIEW: 'platform.knowledge.view',           // 查看知识库
  PLATFORM_KNOWLEDGE_MANAGE: 'platform.knowledge.manage',       // 管理知识库
  PLATFORM_KNOWLEDGE_PUBLISH: 'platform.knowledge.publish',     // 发布知识库内容

  // 商城模块管理权限
  PLATFORM_SHOP_VIEW: 'platform.shop.view',                     // 查看商城模块
  PLATFORM_SHOP_MANAGE: 'platform.shop.manage',                 // 管理商城模块
  PLATFORM_SHOP_CONFIG: 'platform.shop.config',                 // 配置商城参数

  // 租户管理权限
  PLATFORM_TENANT_VIEW: 'platform.tenant.view',                 // 查看租户信息
  PLATFORM_TENANT_CREATE: 'platform.tenant.create',             // 创建租户
  PLATFORM_TENANT_EDIT: 'platform.tenant.edit',                 // 编辑租户信息
  PLATFORM_TENANT_DELETE: 'platform.tenant.delete',             // 删除租户
  PLATFORM_TENANT_CONFIG: 'platform.tenant.config',             // 配置租户权限
  PLATFORM_TENANT_SUBSCRIPTION: 'platform.tenant.subscription', // 管理租户订阅

  // AI大模型配置权限
  PLATFORM_AI_CONFIG: 'platform.ai.config',                     // AI模型配置
  PLATFORM_AI_MONITOR: 'platform.ai.monitor',                   // AI使用监控
  PLATFORM_AI_MANAGE: 'platform.ai.manage',                     // AI服务管理

  // 系统设置权限
  PLATFORM_SYSTEM_CONFIG: 'platform.system.config',             // 系统配置
  PLATFORM_SYSTEM_MONITOR: 'platform.system.monitor',           // 系统监控
  PLATFORM_SYSTEM_BACKUP: 'platform.system.backup',             // 系统备份
  PLATFORM_SYSTEM_MAINTENANCE: 'platform.system.maintenance',   // 系统维护

  // ========== 租户级管理权限 ==========
  // 鹅群管理权限
  FLOCK_VIEW_ALL: 'flock.view.all',               // 查看所有鹅群数据
  FLOCK_VIEW_OWN: 'flock.view.own',               // 查看自己的鹅群数据
  FLOCK_CREATE: 'flock.create',                   // 创建鹅群记录
  FLOCK_EDIT: 'flock.edit',                       // 编辑鹅群记录
  FLOCK_DELETE: 'flock.delete',                   // 删除鹅群记录
  FLOCK_STATISTICS: 'flock.statistics',           // 鹅群统计分析

  // 生产物料管理权限
  MATERIAL_VIEW_ALL: 'material.view.all',         // 查看所有物料数据
  MATERIAL_VIEW_OWN: 'material.view.own',         // 查看自己的物料数据
  MATERIAL_CREATE: 'material.create',             // 创建物料记录
  MATERIAL_EDIT: 'material.edit',                 // 编辑物料记录
  MATERIAL_DELETE: 'material.delete',             // 删除物料记录
  MATERIAL_INVENTORY: 'material.inventory',       // 库存管理
  MATERIAL_PURCHASE: 'material.purchase',         // 采购管理

  // 健康记录管理权限
  HEALTH_VIEW_ALL: 'health.view.all',             // 查看所有健康数据
  HEALTH_VIEW_OWN: 'health.view.own',             // 查看自己的健康数据
  HEALTH_CREATE: 'health.create',                 // 创建健康记录
  HEALTH_EDIT: 'health.edit',                     // 编辑健康记录
  HEALTH_DELETE: 'health.delete',                 // 删除健康记录
  HEALTH_DIAGNOSIS: 'health.diagnosis',           // 疾病诊断
  HEALTH_TREATMENT: 'health.treatment',           // 治疗管理

  // 财务管理权限
  FINANCE_VIEW_ALL: 'finance.view.all',           // 查看所有财务数据
  FINANCE_VIEW_OWN: 'finance.view.own',           // 查看自己的财务数据
  FINANCE_CREATE: 'finance.create',               // 创建财务申请
  FINANCE_EDIT: 'finance.edit',                   // 编辑财务申请
  FINANCE_DELETE: 'finance.delete',               // 删除财务申请
  FINANCE_APPROVE: 'finance.approve',             // 审批财务申请
  FINANCE_EXPORT: 'finance.export',               // 导出财务数据
  FINANCE_REPORTS: 'finance.reports',             // 查看财务报表
  FINANCE_BUDGET: 'finance.budget',               // 预算管理
  FINANCE_COST_ANALYSIS: 'finance.cost_analysis', // 成本分析
  FINANCE_PROFIT_ANALYSIS: 'finance.profit_analysis', // 利润核算

  // ========== 通用权限 ==========
  // 用户管理权限
  USER_VIEW: 'user.view',                         // 查看用户信息
  USER_CREATE: 'user.create',                     // 创建用户
  USER_EDIT: 'user.edit',                         // 编辑用户
  USER_DELETE: 'user.delete',                     // 删除用户
  USER_ROLE_ASSIGN: 'user.role.assign',           // 分配角色

  // 生产管理权限
  PRODUCTION_VIEW_ALL: 'production.view.all',     // 查看所有生产数据
  PRODUCTION_VIEW_OWN: 'production.view.own',     // 查看自己的生产数据
  PRODUCTION_CREATE: 'production.create',         // 创建生产记录
  PRODUCTION_EDIT: 'production.edit',             // 编辑生产记录
  PRODUCTION_DELETE: 'production.delete',         // 删除生产记录
  PRODUCTION_STATISTICS: 'production.statistics', // 生产统计分析

  // 数据权限
  DATA_EXPORT: 'data.export',                     // 数据导出
  DATA_IMPORT: 'data.import',                     // 数据导入
  DATA_ANALYTICS: 'data.analytics',               // 数据分析

  // 审批流程权限
  APPROVAL_CREATE: 'approval.create',             // 创建审批
  APPROVAL_PROCESS: 'approval.process',           // 处理审批
  APPROVAL_VIEW_ALL: 'approval.view.all',         // 查看所有审批
  APPROVAL_VIEW_OWN: 'approval.view.own'          // 查看自己的审批
};

/**
 * 角色权限映射（基于最小权限原则）
 */
const ROLE_PERMISSIONS = {
  // 超级管理员：拥有所有权限
  [ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS),

  // 平台管理员：拥有所有平台级权限 + 部分租户级权限
  [ROLES.PLATFORM_ADMIN]: [
    // 平台级权限
    PERMISSIONS.PLATFORM_GOOSE_PRICE_VIEW,
    PERMISSIONS.PLATFORM_GOOSE_PRICE_MANAGE,
    PERMISSIONS.PLATFORM_GOOSE_PRICE_PUBLISH,
    PERMISSIONS.PLATFORM_ANNOUNCEMENT_VIEW,
    PERMISSIONS.PLATFORM_ANNOUNCEMENT_MANAGE,
    PERMISSIONS.PLATFORM_ANNOUNCEMENT_PUBLISH,
    PERMISSIONS.PLATFORM_KNOWLEDGE_VIEW,
    PERMISSIONS.PLATFORM_KNOWLEDGE_MANAGE,
    PERMISSIONS.PLATFORM_KNOWLEDGE_PUBLISH,
    PERMISSIONS.PLATFORM_SHOP_VIEW,
    PERMISSIONS.PLATFORM_SHOP_MANAGE,
    PERMISSIONS.PLATFORM_SHOP_CONFIG,
    PERMISSIONS.PLATFORM_TENANT_VIEW,
    PERMISSIONS.PLATFORM_TENANT_CREATE,
    PERMISSIONS.PLATFORM_TENANT_EDIT,
    PERMISSIONS.PLATFORM_TENANT_DELETE,
    PERMISSIONS.PLATFORM_TENANT_CONFIG,
    PERMISSIONS.PLATFORM_TENANT_SUBSCRIPTION,
    PERMISSIONS.PLATFORM_AI_CONFIG,
    PERMISSIONS.PLATFORM_AI_MONITOR,
    PERMISSIONS.PLATFORM_AI_MANAGE,
    PERMISSIONS.PLATFORM_SYSTEM_CONFIG,
    PERMISSIONS.PLATFORM_SYSTEM_MONITOR,
    PERMISSIONS.PLATFORM_SYSTEM_BACKUP,
    PERMISSIONS.PLATFORM_SYSTEM_MAINTENANCE,
    // 租户级查看权限（用于平台监控）
    PERMISSIONS.FINANCE_VIEW_ALL,
    PERMISSIONS.FINANCE_REPORTS,
    PERMISSIONS.FINANCE_EXPORT,
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_EDIT,
    PERMISSIONS.USER_ROLE_ASSIGN,
    PERMISSIONS.FLOCK_VIEW_ALL,
    PERMISSIONS.HEALTH_VIEW_ALL,
    PERMISSIONS.MATERIAL_VIEW_ALL,
    PERMISSIONS.PRODUCTION_VIEW_ALL,
    PERMISSIONS.DATA_EXPORT,
    PERMISSIONS.DATA_ANALYTICS,
    PERMISSIONS.APPROVAL_VIEW_ALL
  ],

  // 租户拥有者：拥有租户内所有权限
  [ROLES.TENANT_OWNER]: [
    // 鹅群管理权限
    PERMISSIONS.FLOCK_VIEW_ALL,
    PERMISSIONS.FLOCK_CREATE,
    PERMISSIONS.FLOCK_EDIT,
    PERMISSIONS.FLOCK_DELETE,
    PERMISSIONS.FLOCK_STATISTICS,
    // 物料管理权限
    PERMISSIONS.MATERIAL_VIEW_ALL,
    PERMISSIONS.MATERIAL_CREATE,
    PERMISSIONS.MATERIAL_EDIT,
    PERMISSIONS.MATERIAL_DELETE,
    PERMISSIONS.MATERIAL_INVENTORY,
    PERMISSIONS.MATERIAL_PURCHASE,
    // 健康管理权限
    PERMISSIONS.HEALTH_VIEW_ALL,
    PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.HEALTH_EDIT,
    PERMISSIONS.HEALTH_DELETE,
    PERMISSIONS.HEALTH_DIAGNOSIS,
    PERMISSIONS.HEALTH_TREATMENT,
    // 财务管理权限
    PERMISSIONS.FINANCE_VIEW_ALL,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_EDIT,
    PERMISSIONS.FINANCE_DELETE,
    PERMISSIONS.FINANCE_APPROVE,
    PERMISSIONS.FINANCE_REPORTS,
    PERMISSIONS.FINANCE_BUDGET,
    PERMISSIONS.FINANCE_COST_ANALYSIS,
    PERMISSIONS.FINANCE_PROFIT_ANALYSIS,
    // 用户管理权限
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_EDIT,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_ROLE_ASSIGN,
    // 生产管理权限
    PERMISSIONS.PRODUCTION_VIEW_ALL,
    PERMISSIONS.PRODUCTION_CREATE,
    PERMISSIONS.PRODUCTION_EDIT,
    PERMISSIONS.PRODUCTION_DELETE,
    PERMISSIONS.PRODUCTION_STATISTICS,
    // 数据和审批权限
    PERMISSIONS.DATA_EXPORT,
    PERMISSIONS.DATA_IMPORT,
    PERMISSIONS.DATA_ANALYTICS,
    PERMISSIONS.APPROVAL_VIEW_ALL,
    PERMISSIONS.APPROVAL_PROCESS
  ],

  // 租户管理员：拥有大部分租户权限
  [ROLES.ADMIN]: [
    // 鹅群管理权限
    PERMISSIONS.FLOCK_VIEW_ALL,
    PERMISSIONS.FLOCK_CREATE,
    PERMISSIONS.FLOCK_EDIT,
    PERMISSIONS.FLOCK_STATISTICS,
    // 物料管理权限
    PERMISSIONS.MATERIAL_VIEW_ALL,
    PERMISSIONS.MATERIAL_CREATE,
    PERMISSIONS.MATERIAL_EDIT,
    PERMISSIONS.MATERIAL_INVENTORY,
    PERMISSIONS.MATERIAL_PURCHASE,
    // 健康管理权限
    PERMISSIONS.HEALTH_VIEW_ALL,
    PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.HEALTH_EDIT,
    PERMISSIONS.HEALTH_DIAGNOSIS,
    PERMISSIONS.HEALTH_TREATMENT,
    // 财务管理权限
    PERMISSIONS.FINANCE_VIEW_ALL,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_EDIT,
    PERMISSIONS.FINANCE_APPROVE,
    PERMISSIONS.FINANCE_REPORTS,
    PERMISSIONS.FINANCE_COST_ANALYSIS,
    // 用户管理权限
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_EDIT,
    // 生产管理权限
    PERMISSIONS.PRODUCTION_VIEW_ALL,
    PERMISSIONS.PRODUCTION_CREATE,
    PERMISSIONS.PRODUCTION_EDIT,
    PERMISSIONS.PRODUCTION_STATISTICS,
    // 数据和审批权限
    PERMISSIONS.DATA_EXPORT,
    PERMISSIONS.DATA_ANALYTICS,
    PERMISSIONS.APPROVAL_VIEW_ALL,
    PERMISSIONS.APPROVAL_PROCESS
  ],
  
  [ROLES.MANAGER]: [
    PERMISSIONS.FINANCE_VIEW_ALL,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_APPROVE,
    PERMISSIONS.FINANCE_REPORTS,
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.HEALTH_VIEW_ALL,
    PERMISSIONS.PRODUCTION_VIEW_ALL,
    PERMISSIONS.DATA_ANALYTICS,
    PERMISSIONS.APPROVAL_VIEW_ALL,
    PERMISSIONS.APPROVAL_PROCESS
  ],
  
  [ROLES.FINANCE_MANAGER]: [
    PERMISSIONS.FINANCE_VIEW_ALL,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_EDIT,
    PERMISSIONS.FINANCE_DELETE,
    PERMISSIONS.FINANCE_APPROVE,
    PERMISSIONS.FINANCE_EXPORT,
    PERMISSIONS.FINANCE_REPORTS,
    PERMISSIONS.FINANCE_BUDGET,
    PERMISSIONS.APPROVAL_PROCESS
  ],
  
  [ROLES.FINANCE_STAFF]: [
    PERMISSIONS.FINANCE_VIEW_ALL,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_EDIT,
    PERMISSIONS.FINANCE_EXPORT,
    PERMISSIONS.APPROVAL_CREATE
  ],
  
  [ROLES.HR_MANAGER]: [
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_EDIT,
    PERMISSIONS.USER_ROLE_ASSIGN,
    PERMISSIONS.FINANCE_VIEW_ALL,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.APPROVAL_PROCESS
  ],
  
  [ROLES.VETERINARIAN]: [
    PERMISSIONS.HEALTH_VIEW_ALL,
    PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.HEALTH_EDIT,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_VIEW_OWN,
    PERMISSIONS.APPROVAL_CREATE
  ],
  
  [ROLES.EMPLOYEE]: [
    PERMISSIONS.FINANCE_VIEW_OWN,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.HEALTH_VIEW_OWN,
    PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.PRODUCTION_VIEW_OWN,
    PERMISSIONS.PRODUCTION_CREATE,
    PERMISSIONS.APPROVAL_CREATE,
    PERMISSIONS.APPROVAL_VIEW_OWN
  ],
  
  [ROLES.USER]: [
    PERMISSIONS.FINANCE_VIEW_OWN,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.HEALTH_VIEW_OWN,
    PERMISSIONS.PRODUCTION_VIEW_OWN,
    PERMISSIONS.APPROVAL_CREATE,
    PERMISSIONS.APPROVAL_VIEW_OWN
  ],
  
  [ROLES.VIEWER]: [
    PERMISSIONS.FINANCE_VIEW_OWN,
    PERMISSIONS.HEALTH_VIEW_OWN,
    PERMISSIONS.PRODUCTION_VIEW_OWN,
    PERMISSIONS.APPROVAL_VIEW_OWN
  ]
};

/**
 * 基础权限检查函数
 * @param {string} userRole 用户角色
 * @param {string} permission 权限标识
 * @returns {boolean}
 */
function hasPermission(userRole, permission) {
  if (!userRole || !permission) {
    return false;
  }
  
  const rolePermissions = ROLE_PERMISSIONS[userRole] || [];
  return rolePermissions.includes(permission);
}

/**
 * 检查用户是否有任一权限
 * @param {string} userRole 用户角色
 * @param {Array} permissions 权限数组
 * @returns {boolean}
 */
function hasAnyPermission(userRole, permissions) {
  if (!permissions || !Array.isArray(permissions)) {
    return false;
  }
  
  return permissions.some(permission => hasPermission(userRole, permission));
}

/**
 * 检查用户是否有所有权限
 * @param {string} userRole 用户角色
 * @param {Array} permissions 权限数组
 * @returns {boolean}
 */
function hasAllPermissions(userRole, permissions) {
  if (!permissions || !Array.isArray(permissions)) {
    return false;
  }
  
  return permissions.every(permission => hasPermission(userRole, permission));
}

/**
 * 获取角色的所有权限
 * @param {string} userRole 用户角色
 * @returns {Array}
 */
function getRolePermissions(userRole) {
  return ROLE_PERMISSIONS[userRole] || [];
}

/**
 * 检查是否为管理员角色
 * @param {string} userRole 用户角色
 * @returns {boolean}
 */
function isAdmin(userRole) {
  const adminRoles = [
    ROLES.SUPER_ADMIN,
    ROLES.PLATFORM_ADMIN,
    ROLES.TENANT_OWNER,
    ROLES.ADMIN,
    ROLES.MANAGER,
    ROLES.FINANCE_MANAGER,
    ROLES.HR_MANAGER
  ];
  
  return adminRoles.includes(userRole);
}

/**
 * 检查是否为财务相关角色
 * @param {string} userRole 用户角色
 * @returns {boolean}
 */
function isFinanceRole(userRole) {
  const financeRoles = [
    ROLES.FINANCE_MANAGER,
    ROLES.FINANCE_STAFF
  ];
  
  return financeRoles.includes(userRole) || isAdmin(userRole);
}

/**
 * 角色优先级映射（数字越大优先级越高）
 */
const ROLE_PRIORITY = {
  [ROLES.VIEWER]: 1,
  [ROLES.USER]: 2,
  [ROLES.EMPLOYEE]: 3,
  [ROLES.FINANCE_STAFF]: 4,
  [ROLES.VETERINARIAN]: 4,
  [ROLES.HR_MANAGER]: 5,
  [ROLES.FINANCE_MANAGER]: 6,
  [ROLES.MANAGER]: 7,
  [ROLES.ADMIN]: 8,
  [ROLES.TENANT_OWNER]: 9,
  [ROLES.PLATFORM_ADMIN]: 10,
  [ROLES.SUPER_ADMIN]: 11
};

/**
 * 比较角色优先级
 * @param {string} role1 角色1
 * @param {string} role2 角色2
 * @returns {number} -1: role1 < role2, 0: role1 = role2, 1: role1 > role2
 */
function compareRoles(role1, role2) {
  const priority1 = ROLE_PRIORITY[role1] || 0;
  const priority2 = ROLE_PRIORITY[role2] || 0;
  
  if (priority1 < priority2) return -1;
  if (priority1 > priority2) return 1;
  return 0;
}

/**
 * 获取角色显示名称
 * @param {string} role 角色标识
 * @returns {string}
 */
function getRoleName(role) {
  const roleNames = {
    [ROLES.SUPER_ADMIN]: '超级管理员',
    [ROLES.PLATFORM_ADMIN]: '平台管理员',
    [ROLES.TENANT_OWNER]: '租户拥有者',
    [ROLES.ADMIN]: '管理员',
    [ROLES.MANAGER]: '经理',
    [ROLES.EMPLOYEE]: '员工',
    [ROLES.FINANCE_MANAGER]: '财务经理',
    [ROLES.FINANCE_STAFF]: '财务人员',
    [ROLES.HR_MANAGER]: 'HR经理',
    [ROLES.VETERINARIAN]: '兽医',
    [ROLES.USER]: '普通用户',
    [ROLES.VIEWER]: '访客'
  };
  
  return roleNames[role] || role;
}

/**
 * 异步获取当前用户权限信息
 * @returns {Promise<Object>}
 */
async function getCurrentUserPermissions() {
  try {
    const userInfo = await getCurrentUserInfo();
    const role = userInfo?.role || ROLES.USER;
    
    return {
      role,
      roleName: getRoleName(role),
      permissions: getRolePermissions(role),
      isAdmin: isAdmin(role),
      isFinanceRole: isFinanceRole(role)
    };
  } catch (error) {
    console.error('获取用户权限信息失败:', error);
    return {
      role: ROLES.USER,
      roleName: getRoleName(ROLES.USER),
      permissions: getRolePermissions(ROLES.USER),
      isAdmin: false,
      isFinanceRole: false
    };
  }
}

/**
 * 权限验证装饰器
 * @param {string|Array} requiredPermissions 所需权限
 * @param {Function} callback 回调函数
 * @param {Object} options 选项 { requireAll: false, redirectOnFail: null }
 */
function requirePermissions(requiredPermissions, callback, options = {}) {
  return async function(...args) {
    try {
      const userPermissions = await getCurrentUserPermissions();
      const permissions = Array.isArray(requiredPermissions) 
        ? requiredPermissions 
        : [requiredPermissions];
      
      const hasRequiredPermission = options.requireAll
        ? hasAllPermissions(userPermissions.role, permissions)
        : hasAnyPermission(userPermissions.role, permissions);
      
      if (!hasRequiredPermission) {
        if (options.redirectOnFail) {
          wx.navigateTo({ url: options.redirectOnFail });
          return;
        }
        
        // 静默处理，不显示提示
        console.warn('权限不足');
        return;
      }
      
      return callback.apply(this, args);
    } catch (error) {
      console.error('权限验证失败:', error);
      // 静默处理权限验证失败
      console.error('权限验证失败:', error);
    }
  };
}

/**
 * 财务模块权限检查器（增强版）
 */
const FinancePermissionChecker = {
  /**
   * 检查是否可以查看所有申请
   */
  canViewAllApplications(userRole) {
    return hasPermission(userRole, PERMISSIONS.FINANCE_VIEW_ALL);
  },
  
  /**
   * 检查是否只能查看自己的申请
   */
  canViewOwnApplicationsOnly(userRole) {
    return !this.canViewAllApplications(userRole) && 
           hasPermission(userRole, PERMISSIONS.FINANCE_VIEW_OWN);
  },
  
  /**
   * 检查是否可以审批申请
   */
  canApproveApplications(userRole) {
    return hasPermission(userRole, PERMISSIONS.FINANCE_APPROVE);
  },
  
  /**
   * 检查是否可以创建申请
   */
  canCreateApplication(userRole) {
    return hasPermission(userRole, PERMISSIONS.FINANCE_CREATE);
  },
  
  /**
   * 检查是否可以编辑申请
   */
  canEditApplication(userRole, isOwner = false) {
    if (hasPermission(userRole, PERMISSIONS.FINANCE_EDIT)) {
      return true;
    }
    // 普通员工只能编辑自己创建的申请
    return isOwner && hasPermission(userRole, PERMISSIONS.FINANCE_VIEW_OWN);
  },
  
  /**
   * 检查是否可以删除申请
   */
  canDeleteApplication(userRole, isOwner = false) {
    if (hasPermission(userRole, PERMISSIONS.FINANCE_DELETE)) {
      return true;
    }
    // 普通员工只能删除自己创建的草稿申请
    return isOwner && hasPermission(userRole, PERMISSIONS.FINANCE_CREATE);
  },
  
  /**
   * 检查是否可以查看财务报表
   */
  canViewReports(userRole) {
    return hasPermission(userRole, PERMISSIONS.FINANCE_REPORTS);
  },
  
  /**
   * 检查是否可以导出数据
   */
  canExportData(userRole) {
    return hasPermission(userRole, PERMISSIONS.FINANCE_EXPORT);
  },
  
  /**
   * 检查是否可以管理预算
   */
  canManageBudget(userRole) {
    return hasPermission(userRole, PERMISSIONS.FINANCE_BUDGET);
  },
  
  /**
   * 获取用户在财务模块的权限清单
   */
  getFinancePermissions(userRole) {
    const permissions = {
      canViewAll: this.canViewAllApplications(userRole),
      canViewOwn: hasPermission(userRole, PERMISSIONS.FINANCE_VIEW_OWN),
      canCreate: this.canCreateApplication(userRole),
      canEdit: hasPermission(userRole, PERMISSIONS.FINANCE_EDIT),
      canDelete: hasPermission(userRole, PERMISSIONS.FINANCE_DELETE),
      canApprove: this.canApproveApplications(userRole),
      canViewReports: this.canViewReports(userRole),
      canExport: this.canExportData(userRole),
      canManageBudget: this.canManageBudget(userRole),
      isFinanceRole: isFinanceRole(userRole),
      isAdmin: isAdmin(userRole)
    };
    
    return permissions;
  }
};

/**
 * 数据访问权限检查器
 */
const DataAccessChecker = {
  /**
   * 检查是否可以查看指定用户的数据
   */
  canViewUserData(currentUserRole, targetUserId, currentUserId) {
    // 管理员可以查看所有数据
    if (isAdmin(currentUserRole)) {
      return true;
    }
    
    // 用户只能查看自己的数据
    return targetUserId === currentUserId;
  },
  
  /**
   * 根据角色过滤数据查询条件
   */
  getDataFilter(userRole, userId) {
    if (isAdmin(userRole)) {
      return {}; // 无过滤条件，可以查看所有数据
    }
    
    return { userId }; // 只能查看自己的数据
  }
};

module.exports = {
  ROLES,
  PERMISSIONS,
  ROLE_PERMISSIONS,
  ROLE_PRIORITY,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  getRolePermissions,
  isAdmin,
  isFinanceRole,
  compareRoles,
  getRoleName,
  getCurrentUserPermissions,
  requirePermissions,
  FinancePermissionChecker,
  DataAccessChecker
};