/**
 * 精简版常量入口 - 主包专用
 * 只加载核心必需的常量，其他按需加载
 */

const coreConstants = require('./core.constants.js');

// 按需加载函数
const loadConstants = {
  // 加载扩展API常量
  async loadExtendedAPI() {
    try {
      return require('../constants-data/api-extended.js');
    } catch (error) {
      console.warn('扩展API常量加载失败:', error);
      return {};
    }
  },
  
  // 加载业务常量
  async loadBusinessConstants() {
    try {
      return require('./business.constants.js');
    } catch (error) {
      console.warn('业务常量加载失败:', error);
      return {};
    }
  },
  
  // 加载UI常量
  async loadUIConstants() {
    try {
      return require('./ui.constants.js');
    } catch (error) {
      console.warn('UI常量加载失败:', error);
      return {};
    }
  }
};

// 导出核心常量和按需加载函数
module.exports = {
  ...coreConstants,
  loadConstants
};