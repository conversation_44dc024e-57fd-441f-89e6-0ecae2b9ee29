// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { code, userInfo } = event
    
    // 获取微信用户信息
    const { openid, unionid } = wxContext
    
    if (!openid) {
      return {
        success: false,
        message: '获取用户信息失败',
        code: 'AUTH_FAILED'
      }
    }
    
    // 查询用户是否已存在
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    let user = null
    
    if (userQuery.data.length > 0) {
      // 用户已存在，更新登录时间
      user = userQuery.data[0]
      await db.collection('users').doc(user._id).update({
        data: {
          last_login_at: new Date(),
          updated_at: new Date()
        }
      })
    } else {
      // 新用户，创建用户记录
      const createResult = await db.collection('users').add({
        data: {
          openid: openid,
          unionid: unionid || null,
          nickname: userInfo?.nickName || '微信用户',
          avatar: userInfo?.avatarUrl || '',
          phone: '',
          email: '',
          tenant_id: '', // 需要根据业务逻辑设置
          role: 'user',
          status: 'active',
          permissions: ['basic'],
          created_at: new Date(),
          updated_at: new Date(),
          last_login_at: new Date()
        }
      })
      
      // 获取新创建的用户信息
      const newUserQuery = await db.collection('users').doc(createResult._id).get()
      user = newUserQuery.data
    }
    
    // 生成自定义登录态
    const customToken = generateCustomToken(user)
    
    return {
      success: true,
      message: '登录成功',
      data: {
        user: {
          id: user._id,
          openid: user.openid,
          nickname: user.nickname,
          avatar: user.avatar,
          role: user.role,
          tenant_id: user.tenant_id,
          permissions: user.permissions
        },
        token: customToken
      }
    }
    
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      message: '登录失败',
      code: 'LOGIN_ERROR',
      error: error.message
    }
  }
}

// 生成自定义登录态
function generateCustomToken(user) {
  // 这里可以使用JWT或其他方式生成token
  // 简单示例：使用用户ID和时间戳
  const timestamp = Date.now()
  const payload = {
    userId: user._id,
    openid: user.openid,
    role: user.role,
    tenant_id: user.tenant_id,
    timestamp: timestamp,
    expires: timestamp + 7 * 24 * 60 * 60 * 1000 // 7天过期
  }
  
  // 实际项目中应该使用加密算法
  return Buffer.from(JSON.stringify(payload)).toString('base64')
}
