// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { token, userId } = event
    const { openid } = wxContext
    
    if (!openid) {
      return {
        success: false,
        message: '用户未登录',
        code: 'NOT_AUTHENTICATED'
      }
    }
    
    // 验证token（简单验证）
    if (token) {
      try {
        const tokenData = JSON.parse(Buffer.from(token, 'base64').toString())
        if (tokenData.expires < Date.now()) {
          return {
            success: false,
            message: 'Token已过期',
            code: 'TOKEN_EXPIRED'
          }
        }
      } catch (error) {
        return {
          success: false,
          message: 'Token无效',
          code: 'INVALID_TOKEN'
        }
      }
    }
    
    // 查询用户信息
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userQuery.data.length === 0) {
      return {
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND'
      }
    }
    
    const user = userQuery.data[0]
    
    // 如果用户有租户ID，获取租户信息
    let tenant = null
    if (user.tenant_id) {
      const tenantQuery = await db.collection('tenants').doc(user.tenant_id).get()
      if (tenantQuery.data) {
        tenant = {
          id: tenantQuery.data._id,
          tenant_code: tenantQuery.data.tenant_code,
          company_name: tenantQuery.data.company_name,
          subscription_plan: tenantQuery.data.subscription_plan,
          features: tenantQuery.data.features,
          status: tenantQuery.data.status
        }
      }
    }
    
    return {
      success: true,
      message: '获取用户信息成功',
      data: {
        user: {
          id: user._id,
          openid: user.openid,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          email: user.email,
          role: user.role,
          status: user.status,
          tenant_id: user.tenant_id,
          permissions: user.permissions,
          created_at: user.created_at,
          last_login_at: user.last_login_at
        },
        tenant: tenant
      }
    }
    
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return {
      success: false,
      message: '获取用户信息失败',
      code: 'GET_USER_INFO_ERROR',
      error: error.message
    }
  }
}
