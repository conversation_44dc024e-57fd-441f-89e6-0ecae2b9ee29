// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { action, data, productId, orderId, page = 1, limit = 20 } = event
    const { openid } = wxContext
    
    if (!openid) {
      return {
        success: false,
        message: '用户未登录',
        code: 'NOT_AUTHENTICATED'
      }
    }
    
    // 获取用户信息
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userQuery.data.length === 0) {
      return {
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND'
      }
    }
    
    const user = userQuery.data[0]
    
    switch (action) {
      // 商品相关
      case 'productList':
        return await getProductList(user, page, limit, data)
      case 'productDetail':
        return await getProductDetail(user, productId)
      case 'createProduct':
        return await createProduct(user, data)
      case 'updateProduct':
        return await updateProduct(user, productId, data)
      case 'deleteProduct':
        return await deleteProduct(user, productId)
      
      // 订单相关
      case 'orderList':
        return await getOrderList(user, page, limit, data)
      case 'orderDetail':
        return await getOrderDetail(user, orderId)
      case 'createOrder':
        return await createOrder(user, data)
      case 'updateOrderStatus':
        return await updateOrderStatus(user, orderId, data)
      case 'cancelOrder':
        return await cancelOrder(user, orderId)
      
      // 购物车相关
      case 'getCart':
        return await getCart(user)
      case 'addToCart':
        return await addToCart(user, data)
      case 'updateCart':
        return await updateCart(user, data)
      case 'removeFromCart':
        return await removeFromCart(user, data)
      case 'clearCart':
        return await clearCart(user)
      
      default:
        return {
          success: false,
          message: '不支持的操作',
          code: 'UNSUPPORTED_ACTION'
        }
    }
    
  } catch (error) {
    console.error('商城管理操作失败:', error)
    return {
      success: false,
      message: '操作失败',
      code: 'OPERATION_ERROR',
      error: error.message
    }
  }
}

// 获取商品列表
async function getProductList(user, page, limit, filters = {}) {
  const skip = (page - 1) * limit
  
  let query = db.collection('products').where({
    tenant_id: user.tenant_id,
    status: 'active'
  })
  
  // 添加过滤条件
  if (filters.category) {
    query = query.where({
      category: filters.category
    })
  }
  
  if (filters.keyword) {
    query = query.where({
      name: db.RegExp({
        regexp: filters.keyword,
        options: 'i'
      })
    })
  }
  
  const countResult = await query.count()
  const listResult = await query
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(limit)
    .get()
  
  return {
    success: true,
    message: '获取商品列表成功',
    data: {
      list: listResult.data,
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        pages: Math.ceil(countResult.total / limit)
      }
    }
  }
}

// 获取商品详情
async function getProductDetail(user, productId) {
  if (!productId) {
    return {
      success: false,
      message: '商品ID不能为空',
      code: 'PRODUCT_ID_REQUIRED'
    }
  }
  
  const productResult = await db.collection('products').doc(productId).get()
  
  if (!productResult.data) {
    return {
      success: false,
      message: '商品不存在',
      code: 'PRODUCT_NOT_FOUND'
    }
  }
  
  const product = productResult.data
  
  // 检查权限
  if (product.tenant_id !== user.tenant_id) {
    return {
      success: false,
      message: '无权限访问该商品',
      code: 'ACCESS_DENIED'
    }
  }
  
  return {
    success: true,
    message: '获取商品详情成功',
    data: product
  }
}

// 创建订单
async function createOrder(user, data) {
  const { products, shipping_address, payment_method } = data
  
  if (!products || products.length === 0) {
    return {
      success: false,
      message: '商品列表不能为空',
      code: 'PRODUCTS_REQUIRED'
    }
  }
  
  if (!shipping_address) {
    return {
      success: false,
      message: '收货地址不能为空',
      code: 'SHIPPING_ADDRESS_REQUIRED'
    }
  }
  
  // 验证商品并计算总价
  let totalAmount = 0
  const orderProducts = []
  
  for (const item of products) {
    const productResult = await db.collection('products').doc(item.product_id).get()
    
    if (!productResult.data) {
      return {
        success: false,
        message: `商品 ${item.product_id} 不存在`,
        code: 'PRODUCT_NOT_FOUND'
      }
    }
    
    const product = productResult.data
    
    if (product.tenant_id !== user.tenant_id) {
      return {
        success: false,
        message: '商品不属于当前租户',
        code: 'INVALID_PRODUCT'
      }
    }
    
    if (product.stock < item.quantity) {
      return {
        success: false,
        message: `商品 ${product.name} 库存不足`,
        code: 'INSUFFICIENT_STOCK'
      }
    }
    
    const subtotal = product.price * item.quantity
    totalAmount += subtotal
    
    orderProducts.push({
      product_id: item.product_id,
      name: product.name,
      price: product.price,
      quantity: item.quantity,
      subtotal: subtotal
    })
  }
  
  // 生成订单号
  const orderNo = generateOrderNo()
  
  const orderData = {
    tenant_id: user.tenant_id,
    user_id: user._id,
    order_no: orderNo,
    products: orderProducts,
    total_amount: totalAmount,
    discount_amount: 0,
    shipping_fee: 0,
    final_amount: totalAmount,
    status: 'pending',
    payment_status: 'unpaid',
    payment_method: payment_method || 'wechat_pay',
    shipping_address: shipping_address,
    created_at: new Date(),
    updated_at: new Date()
  }
  
  const result = await db.collection('orders').add({
    data: orderData
  })
  
  // 更新商品库存
  for (const item of products) {
    await db.collection('products').doc(item.product_id).update({
      data: {
        stock: _.inc(-item.quantity),
        sales_count: _.inc(item.quantity),
        updated_at: new Date()
      }
    })
  }
  
  return {
    success: true,
    message: '创建订单成功',
    data: {
      id: result._id,
      order_no: orderNo,
      ...orderData
    }
  }
}

// 获取订单列表
async function getOrderList(user, page, limit, filters = {}) {
  const skip = (page - 1) * limit
  
  let query = db.collection('orders').where({
    tenant_id: user.tenant_id,
    user_id: user._id
  })
  
  // 添加过滤条件
  if (filters.status) {
    query = query.where({
      status: filters.status
    })
  }
  
  const countResult = await query.count()
  const listResult = await query
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(limit)
    .get()
  
  return {
    success: true,
    message: '获取订单列表成功',
    data: {
      list: listResult.data,
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        pages: Math.ceil(countResult.total / limit)
      }
    }
  }
}

// 获取购物车
async function getCart(user) {
  // 购物车可以存储在用户集合中，或者单独的购物车集合
  const userResult = await db.collection('users').doc(user._id).get()
  
  if (!userResult.data) {
    return {
      success: false,
      message: '用户不存在',
      code: 'USER_NOT_FOUND'
    }
  }
  
  const cart = userResult.data.cart || []
  
  // 获取购物车中商品的详细信息
  const cartWithDetails = []
  for (const item of cart) {
    const productResult = await db.collection('products').doc(item.product_id).get()
    if (productResult.data) {
      cartWithDetails.push({
        ...item,
        product: productResult.data
      })
    }
  }
  
  return {
    success: true,
    message: '获取购物车成功',
    data: {
      items: cartWithDetails,
      total_items: cartWithDetails.length,
      total_amount: cartWithDetails.reduce((sum, item) => sum + (item.product.price * item.quantity), 0)
    }
  }
}

// 添加到购物车
async function addToCart(user, data) {
  const { product_id, quantity = 1 } = data
  
  if (!product_id) {
    return {
      success: false,
      message: '商品ID不能为空',
      code: 'PRODUCT_ID_REQUIRED'
    }
  }
  
  // 验证商品
  const productResult = await db.collection('products').doc(product_id).get()
  
  if (!productResult.data) {
    return {
      success: false,
      message: '商品不存在',
      code: 'PRODUCT_NOT_FOUND'
    }
  }
  
  const product = productResult.data
  
  if (product.tenant_id !== user.tenant_id) {
    return {
      success: false,
      message: '商品不属于当前租户',
      code: 'INVALID_PRODUCT'
    }
  }
  
  if (product.stock < quantity) {
    return {
      success: false,
      message: '商品库存不足',
      code: 'INSUFFICIENT_STOCK'
    }
  }
  
  // 获取当前购物车
  const userResult = await db.collection('users').doc(user._id).get()
  const currentCart = userResult.data.cart || []
  
  // 检查商品是否已在购物车中
  const existingItemIndex = currentCart.findIndex(item => item.product_id === product_id)
  
  if (existingItemIndex >= 0) {
    // 更新数量
    currentCart[existingItemIndex].quantity += quantity
  } else {
    // 添加新商品
    currentCart.push({
      product_id: product_id,
      quantity: quantity,
      added_at: new Date()
    })
  }
  
  // 更新用户购物车
  await db.collection('users').doc(user._id).update({
    data: {
      cart: currentCart,
      updated_at: new Date()
    }
  })
  
  return {
    success: true,
    message: '添加到购物车成功',
    data: {
      cart_items: currentCart.length
    }
  }
}

// 生成订单号
function generateOrderNo() {
  const now = new Date()
  const year = now.getFullYear().toString().slice(-2)
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hour = now.getHours().toString().padStart(2, '0')
  const minute = now.getMinutes().toString().padStart(2, '0')
  const second = now.getSeconds().toString().padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  
  return `${year}${month}${day}${hour}${minute}${second}${random}`
}
