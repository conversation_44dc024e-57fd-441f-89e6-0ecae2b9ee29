/**
 * 鹅群管理云函数 V2（标准化版本）
 * Flock Management Cloud Function V2 (Standardized Version)
 * 
 * 功能：
 * - 使用统一API标准
 * - 严格的数据隔离机制
 * - 完整的权限控制
 * - 标准化错误处理
 */

const cloud = require('wx-server-sdk');
const { apiHandler, ValidationHelper, PaginationHelper, APIError, ERROR_CODES } = require('../../../utils/api-standard');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 云函数入口函数 - 使用API标准化处理器
 */
exports.main = apiHandler(async (params, context) => {
  const { action, data } = params;
  const { user } = context;

  switch (action) {
    case 'list':
      return await getFlockList(params, context);
    case 'detail':
      return await getFlockDetail(params, context);
    case 'create':
      return await createFlock(params, context);
    case 'update':
      return await updateFlock(params, context);
    case 'delete':
      return await deleteFlock(params, context);
    case 'statistics':
      return await getFlockStatistics(params, context);
    case 'breedAnalysis':
      return await getBreedAnalysis(params, context);
    case 'healthOverview':
      return await getHealthOverview(params, context);
    case 'batchUpdate':
      return await batchUpdateFlocks(params, context);
    default:
      throw new APIError('不支持的操作类型', ERROR_CODES.INVALID_PARAMS);
  }
});

/**
 * 获取鹅群列表
 */
async function getFlockList(params, context) {
  const { page, limit, data } = params;
  const { user, secureQuery, logDataAccess } = context;
  
  // 构建查询条件
  const filters = data.filters || {};
  let baseQuery = {};
  
  // 添加筛选条件
  if (filters.breed) {
    baseQuery.breed = filters.breed;
  }
  
  if (filters.health_status) {
    baseQuery.health_status = filters.health_status;
  }
  
  if (filters.location) {
    baseQuery.location = _.regex({
      regexp: filters.location,
      options: 'i'
    });
  }

  // 应用数据隔离
  const secureQueryCondition = secureQuery('flocks', baseQuery);
  
  // 分页参数处理
  const { skip } = PaginationHelper.validatePaginationParams(page, limit);

  try {
    // 获取总数
    const countResult = await db.collection('flocks')
      .where(secureQueryCondition)
      .count();

    // 获取列表数据
    const listResult = await db.collection('flocks')
      .where(secureQueryCondition)
      .orderBy('created_at', 'desc')
      .skip(skip)
      .limit(limit)
      .get();

    // 记录数据访问日志
    await logDataAccess('flocks', 'read', secureQueryCondition, listResult.data);

    // 返回分页结果
    return PaginationHelper.createPaginationResponse(
      listResult.data,
      page,
      limit,
      countResult.total
    );
  } catch (error) {
    console.error('获取鹅群列表失败:', error);
    throw new APIError('获取鹅群列表失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 获取鹅群详情
 */
async function getFlockDetail(params, context) {
  const { data } = params;
  const { user, secureQuery, logDataAccess } = context;
  
  ValidationHelper.validateRequired(data, ['id']);
  
  const secureQueryCondition = secureQuery('flocks', { _id: data.id });

  try {
    const result = await db.collection('flocks')
      .where(secureQueryCondition)
      .get();

    if (result.data.length === 0) {
      throw new APIError('鹅群不存在或无权限访问', ERROR_CODES.RESOURCE_NOT_FOUND);
    }

    const flock = result.data[0];

    // 获取相关的健康记录
    const healthRecords = await db.collection('health_records')
      .where(secureQuery('health_records', { flock_id: data.id }))
      .orderBy('record_date', 'desc')
      .limit(5)
      .get();

    // 获取相关的生产记录
    const productionRecords = await db.collection('production_records')
      .where(secureQuery('production_records', { flock_id: data.id }))
      .orderBy('record_date', 'desc')
      .limit(5)
      .get();

    const flockDetail = {
      ...flock,
      recent_health_records: healthRecords.data,
      recent_production_records: productionRecords.data
    };

    // 记录数据访问日志
    await logDataAccess('flocks', 'read', secureQueryCondition, [flockDetail]);

    return flockDetail;
  } catch (error) {
    if (error instanceof APIError) {
      throw error;
    }
    console.error('获取鹅群详情失败:', error);
    throw new APIError('获取鹅群详情失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 创建鹅群
 */
async function createFlock(params, context) {
  const { data } = params;
  const { user, validateData, logDataAccess } = context;
  
  // 参数验证
  ValidationHelper.validateRequired(data, ['name', 'breed', 'count', 'location']);
  ValidationHelper.validateLength(data.name, 1, 50, '鹅群名称');
  
  if (data.count <= 0) {
    throw new APIError('鹅群数量必须大于0', ERROR_CODES.INVALID_PARAMS);
  }

  // 数据安全验证和处理
  const flockData = validateData('flocks', {
    name: data.name,
    breed: data.breed,
    count: parseInt(data.count),
    age: data.age || 0,
    location: data.location,
    health_status: data.health_status || 'healthy',
    description: data.description || '',
    purchase_date: data.purchase_date ? new Date(data.purchase_date) : new Date(),
    created_at: new Date(),
    updated_at: new Date()
  });

  try {
    const result = await db.collection('flocks').add({
      data: flockData
    });

    const createdFlock = {
      _id: result._id,
      ...flockData
    };

    // 记录数据访问日志
    await logDataAccess('flocks', 'create', {}, [createdFlock]);

    return createdFlock;
  } catch (error) {
    console.error('创建鹅群失败:', error);
    throw new APIError('创建鹅群失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 更新鹅群
 */
async function updateFlock(params, context) {
  const { data } = params;
  const { user, secureQuery, validateData, logDataAccess } = context;
  
  ValidationHelper.validateRequired(data, ['id']);
  
  // 检查鹅群是否存在且有权限访问
  const existingFlock = await db.collection('flocks')
    .where(secureQuery('flocks', { _id: data.id }))
    .get();

  if (existingFlock.data.length === 0) {
    throw new APIError('鹅群不存在或无权限访问', ERROR_CODES.RESOURCE_NOT_FOUND);
  }

  // 准备更新数据
  const updateData = {
    updated_at: new Date()
  };

  // 只更新提供的字段
  const allowedFields = ['name', 'breed', 'count', 'age', 'location', 'health_status', 'description'];
  allowedFields.forEach(field => {
    if (data[field] !== undefined) {
      updateData[field] = data[field];
    }
  });

  // 数据验证
  if (updateData.name) {
    ValidationHelper.validateLength(updateData.name, 1, 50, '鹅群名称');
  }
  
  if (updateData.count && updateData.count <= 0) {
    throw new APIError('鹅群数量必须大于0', ERROR_CODES.INVALID_PARAMS);
  }

  try {
    await db.collection('flocks').doc(data.id).update({
      data: updateData
    });

    // 获取更新后的数据
    const updatedFlock = await db.collection('flocks').doc(data.id).get();

    // 记录数据访问日志
    await logDataAccess('flocks', 'update', { _id: data.id }, [updatedFlock.data]);

    return updatedFlock.data;
  } catch (error) {
    console.error('更新鹅群失败:', error);
    throw new APIError('更新鹅群失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 删除鹅群
 */
async function deleteFlock(params, context) {
  const { data } = params;
  const { user, secureQuery, logDataAccess } = context;
  
  ValidationHelper.validateRequired(data, ['id']);
  
  // 检查鹅群是否存在且有权限访问
  const existingFlock = await db.collection('flocks')
    .where(secureQuery('flocks', { _id: data.id }))
    .get();

  if (existingFlock.data.length === 0) {
    throw new APIError('鹅群不存在或无权限访问', ERROR_CODES.RESOURCE_NOT_FOUND);
  }

  try {
    // 软删除：更新状态为deleted
    await db.collection('flocks').doc(data.id).update({
      data: {
        status: 'deleted',
        deleted_at: new Date(),
        updated_at: new Date()
      }
    });

    // 记录数据访问日志
    await logDataAccess('flocks', 'delete', { _id: data.id }, [{ _id: data.id }]);

    return { id: data.id, message: '鹅群删除成功' };
  } catch (error) {
    console.error('删除鹅群失败:', error);
    throw new APIError('删除鹅群失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 获取鹅群统计信息
 */
async function getFlockStatistics(params, context) {
  const { user, secureQuery, logDataAccess } = context;
  
  const baseQuery = secureQuery('flocks', { status: _.neq('deleted') });

  try {
    // 总体统计
    const totalFlocks = await db.collection('flocks').where(baseQuery).count();
    
    // 按品种统计
    const breedStats = await db.collection('flocks')
      .aggregate()
      .match(baseQuery)
      .group({
        _id: '$breed',
        count: _.sum(1),
        totalCount: _.sum('$count')
      })
      .end();
    
    // 按健康状态统计
    const healthStats = await db.collection('flocks')
      .aggregate()
      .match(baseQuery)
      .group({
        _id: '$health_status',
        count: _.sum(1),
        totalCount: _.sum('$count')
      })
      .end();

    const statistics = {
      totalFlocks: totalFlocks.total,
      breedStats: breedStats.list,
      healthStats: healthStats.list
    };

    // 记录数据访问日志
    await logDataAccess('flocks', 'read', baseQuery, [statistics]);

    return statistics;
  } catch (error) {
    console.error('获取鹅群统计失败:', error);
    throw new APIError('获取鹅群统计失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 获取品种分析
 */
async function getBreedAnalysis(params, context) {
  const { user, secureQuery, logDataAccess } = context;
  
  const baseQuery = secureQuery('flocks', { status: _.neq('deleted') });

  try {
    const breedAnalysis = await db.collection('flocks')
      .aggregate()
      .match(baseQuery)
      .group({
        _id: '$breed',
        flockCount: _.sum(1),
        totalBirds: _.sum('$count'),
        avgAge: _.avg('$age'),
        healthyCount: _.sum(_.cond([
          _.eq(['$health_status', 'healthy']), 1, 0
        ])),
        sickCount: _.sum(_.cond([
          _.eq(['$health_status', 'sick']), 1, 0
        ]))
      })
      .addFields({
        healthRate: _.divide(['$healthyCount', '$flockCount'])
      })
      .sort({
        totalBirds: -1
      })
      .end();

    // 记录数据访问日志
    await logDataAccess('flocks', 'read', baseQuery, breedAnalysis.list);

    return breedAnalysis.list;
  } catch (error) {
    console.error('获取品种分析失败:', error);
    throw new APIError('获取品种分析失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 获取健康概览
 */
async function getHealthOverview(params, context) {
  const { user, secureQuery, logDataAccess } = context;
  
  const baseQuery = secureQuery('flocks', { status: _.neq('deleted') });

  try {
    const healthOverview = await db.collection('flocks')
      .aggregate()
      .match(baseQuery)
      .group({
        _id: null,
        totalFlocks: _.sum(1),
        totalBirds: _.sum('$count'),
        healthyFlocks: _.sum(_.cond([
          _.eq(['$health_status', 'healthy']), 1, 0
        ])),
        sickFlocks: _.sum(_.cond([
          _.eq(['$health_status', 'sick']), 1, 0
        ])),
        quarantineFlocks: _.sum(_.cond([
          _.eq(['$health_status', 'quarantine']), 1, 0
        ]))
      })
      .addFields({
        healthRate: _.divide(['$healthyFlocks', '$totalFlocks']),
        sickRate: _.divide(['$sickFlocks', '$totalFlocks']),
        quarantineRate: _.divide(['$quarantineFlocks', '$totalFlocks'])
      })
      .end();
    
    const overview = healthOverview.list[0] || {
      totalFlocks: 0,
      totalBirds: 0,
      healthyFlocks: 0,
      sickFlocks: 0,
      quarantineFlocks: 0,
      healthRate: 0,
      sickRate: 0,
      quarantineRate: 0
    };

    // 记录数据访问日志
    await logDataAccess('flocks', 'read', baseQuery, [overview]);

    return overview;
  } catch (error) {
    console.error('获取健康概览失败:', error);
    throw new APIError('获取健康概览失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 批量更新鹅群
 */
async function batchUpdateFlocks(params, context) {
  const { data } = params;
  const { user, secureQuery, logDataAccess } = context;
  
  ValidationHelper.validateRequired(data, ['flockIds', 'updateData']);
  
  const { flockIds, updateData } = data;
  
  if (!Array.isArray(flockIds) || flockIds.length === 0) {
    throw new APIError('鹅群ID列表不能为空', ERROR_CODES.INVALID_PARAMS);
  }

  try {
    const batch = db.batch();
    const results = [];
    
    for (const flockId of flockIds) {
      // 检查鹅群权限
      const flockResult = await db.collection('flocks')
        .where(secureQuery('flocks', { _id: flockId }))
        .get();
      
      if (flockResult.data.length === 0) {
        results.push({
          flockId,
          success: false,
          message: '鹅群不存在或无权限访问'
        });
        continue;
      }
      
      // 准备更新数据
      const safeUpdateData = {
        ...updateData,
        updated_at: new Date()
      };
      
      // 移除不允许更新的字段
      delete safeUpdateData.tenant_id;
      delete safeUpdateData.user_id;
      delete safeUpdateData.created_at;
      
      batch.update(db.collection('flocks').doc(flockId), safeUpdateData);
      
      results.push({
        flockId,
        success: true,
        message: '更新成功'
      });
    }
    
    // 执行批量更新
    await batch.commit();

    // 记录数据访问日志
    await logDataAccess('flocks', 'batch_update', { flockIds }, results);
    
    return {
      results,
      successCount: results.filter(r => r.success).length,
      failCount: results.filter(r => !r.success).length
    };
  } catch (error) {
    console.error('批量更新鹅群失败:', error);
    throw new APIError('批量更新鹅群失败', ERROR_CODES.DATABASE_ERROR);
  }
}
