// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { action, data, recordId, page = 1, limit = 20 } = event
    const { openid } = wxContext
    
    if (!openid) {
      return {
        success: false,
        message: '用户未登录',
        code: 'NOT_AUTHENTICATED'
      }
    }
    
    // 获取用户信息
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userQuery.data.length === 0) {
      return {
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND'
      }
    }
    
    const user = userQuery.data[0]
    
    switch (action) {
      case 'list':
        return await getHealthRecords(user, page, limit, data)
      case 'detail':
        return await getHealthRecordDetail(user, recordId)
      case 'create':
        return await createHealthRecord(user, data)
      case 'update':
        return await updateHealthRecord(user, recordId, data)
      case 'delete':
        return await deleteHealthRecord(user, recordId)
      case 'stats':
        return await getHealthStats(user, data)
      case 'aiDiagnosis':
        return await performAIDiagnosis(user, data)
      default:
        return {
          success: false,
          message: '不支持的操作',
          code: 'UNSUPPORTED_ACTION'
        }
    }
    
  } catch (error) {
    console.error('健康管理操作失败:', error)
    return {
      success: false,
      message: '操作失败',
      code: 'OPERATION_ERROR',
      error: error.message
    }
  }
}

// 获取健康记录列表
async function getHealthRecords(user, page, limit, filters = {}) {
  const skip = (page - 1) * limit
  
  let query = db.collection('health_records').where({
    tenant_id: user.tenant_id
  })
  
  // 添加过滤条件
  if (filters.flock_id) {
    query = query.where({
      flock_id: filters.flock_id
    })
  }
  
  if (filters.record_type) {
    query = query.where({
      record_type: filters.record_type
    })
  }
  
  if (filters.start_date && filters.end_date) {
    query = query.where({
      created_at: _.gte(new Date(filters.start_date)).and(_.lte(new Date(filters.end_date)))
    })
  }
  
  const countResult = await query.count()
  const listResult = await query
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(limit)
    .get()
  
  return {
    success: true,
    message: '获取健康记录列表成功',
    data: {
      list: listResult.data,
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        pages: Math.ceil(countResult.total / limit)
      }
    }
  }
}

// 获取健康记录详情
async function getHealthRecordDetail(user, recordId) {
  if (!recordId) {
    return {
      success: false,
      message: '记录ID不能为空',
      code: 'RECORD_ID_REQUIRED'
    }
  }
  
  const recordResult = await db.collection('health_records').doc(recordId).get()
  
  if (!recordResult.data) {
    return {
      success: false,
      message: '健康记录不存在',
      code: 'RECORD_NOT_FOUND'
    }
  }
  
  const record = recordResult.data
  
  // 检查权限
  if (record.tenant_id !== user.tenant_id) {
    return {
      success: false,
      message: '无权限访问该记录',
      code: 'ACCESS_DENIED'
    }
  }
  
  return {
    success: true,
    message: '获取健康记录详情成功',
    data: record
  }
}

// 创建健康记录
async function createHealthRecord(user, data) {
  const { flock_id, record_type, symptoms, diagnosis, treatment, images, cost, notes } = data
  
  if (!flock_id || !record_type) {
    return {
      success: false,
      message: '鹅群ID和记录类型不能为空',
      code: 'REQUIRED_FIELDS_MISSING'
    }
  }
  
  // 验证鹅群是否存在且属于当前租户
  const flockResult = await db.collection('flocks').doc(flock_id).get()
  if (!flockResult.data || flockResult.data.tenant_id !== user.tenant_id) {
    return {
      success: false,
      message: '鹅群不存在或无权限',
      code: 'FLOCK_NOT_FOUND'
    }
  }
  
  const recordData = {
    tenant_id: user.tenant_id,
    user_id: user._id,
    flock_id: flock_id,
    record_type: record_type,
    symptoms: symptoms || '',
    diagnosis: diagnosis || '',
    treatment: treatment || '',
    images: images || [],
    cost: parseFloat(cost) || 0,
    notes: notes || '',
    created_at: new Date(),
    updated_at: new Date()
  }
  
  const result = await db.collection('health_records').add({
    data: recordData
  })
  
  return {
    success: true,
    message: '创建健康记录成功',
    data: {
      id: result._id,
      ...recordData
    }
  }
}

// 更新健康记录
async function updateHealthRecord(user, recordId, data) {
  if (!recordId) {
    return {
      success: false,
      message: '记录ID不能为空',
      code: 'RECORD_ID_REQUIRED'
    }
  }
  
  // 检查记录是否存在和权限
  const recordResult = await db.collection('health_records').doc(recordId).get()
  
  if (!recordResult.data) {
    return {
      success: false,
      message: '健康记录不存在',
      code: 'RECORD_NOT_FOUND'
    }
  }
  
  const record = recordResult.data
  
  if (record.tenant_id !== user.tenant_id) {
    return {
      success: false,
      message: '无权限修改该记录',
      code: 'ACCESS_DENIED'
    }
  }
  
  const updateData = {
    ...data,
    updated_at: new Date()
  }
  
  // 移除不允许更新的字段
  delete updateData.tenant_id
  delete updateData.user_id
  delete updateData.created_at
  
  await db.collection('health_records').doc(recordId).update({
    data: updateData
  })
  
  return {
    success: true,
    message: '更新健康记录成功',
    data: {
      id: recordId,
      ...updateData
    }
  }
}

// 删除健康记录
async function deleteHealthRecord(user, recordId) {
  if (!recordId) {
    return {
      success: false,
      message: '记录ID不能为空',
      code: 'RECORD_ID_REQUIRED'
    }
  }
  
  // 检查记录是否存在和权限
  const recordResult = await db.collection('health_records').doc(recordId).get()
  
  if (!recordResult.data) {
    return {
      success: false,
      message: '健康记录不存在',
      code: 'RECORD_NOT_FOUND'
    }
  }
  
  const record = recordResult.data
  
  if (record.tenant_id !== user.tenant_id) {
    return {
      success: false,
      message: '无权限删除该记录',
      code: 'ACCESS_DENIED'
    }
  }
  
  await db.collection('health_records').doc(recordId).remove()
  
  return {
    success: true,
    message: '删除健康记录成功',
    data: {
      id: recordId
    }
  }
}

// 获取健康统计数据
async function getHealthStats(user, filters = {}) {
  const query = {
    tenant_id: user.tenant_id
  }
  
  if (filters.flock_id) {
    query.flock_id = filters.flock_id
  }
  
  // 获取总记录数
  const totalRecords = await db.collection('health_records').where(query).count()
  
  // 按记录类型统计
  const typeStats = await db.collection('health_records')
    .aggregate()
    .match(query)
    .group({
      _id: '$record_type',
      count: db.command.aggregate.sum(1)
    })
    .end()
  
  // 最近30天的记录趋势
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
  
  const recentRecords = await db.collection('health_records')
    .where({
      ...query,
      created_at: _.gte(thirtyDaysAgo)
    })
    .count()
  
  return {
    success: true,
    message: '获取健康统计数据成功',
    data: {
      total_records: totalRecords.total,
      recent_records: recentRecords.total,
      type_distribution: typeStats.list,
      period: {
        start_date: thirtyDaysAgo.toISOString(),
        end_date: new Date().toISOString()
      }
    }
  }
}

// AI诊断功能
async function performAIDiagnosis(user, data) {
  const { symptoms, images, flock_id } = data
  
  if (!symptoms && (!images || images.length === 0)) {
    return {
      success: false,
      message: '症状描述或图片不能为空',
      code: 'SYMPTOMS_OR_IMAGES_REQUIRED'
    }
  }
  
  try {
    // 这里应该调用实际的AI诊断服务
    // 目前返回模拟结果
    const mockDiagnosis = {
      result: '根据症状描述，可能是营养不良或环境应激反应',
      confidence: 0.75,
      suggestions: [
        '调整饲料配比，增加维生素含量',
        '改善饲养环境，保持适宜温湿度',
        '建议咨询专业兽医进行进一步检查'
      ],
      severity: 'medium', // low, medium, high
      recommended_actions: [
        '立即隔离观察',
        '调整饲料',
        '监测体温'
      ]
    }
    
    return {
      success: true,
      message: 'AI诊断完成',
      data: {
        diagnosis: mockDiagnosis,
        timestamp: new Date().toISOString(),
        input: {
          symptoms,
          images_count: images ? images.length : 0
        }
      }
    }
    
  } catch (error) {
    console.error('AI诊断失败:', error)
    return {
      success: false,
      message: 'AI诊断服务暂时不可用',
      code: 'AI_SERVICE_ERROR',
      error: error.message
    }
  }
}
