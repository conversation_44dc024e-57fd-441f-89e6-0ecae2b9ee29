/**
 * 租户级物料管理云函数
 * Tenant-Level Material Management Cloud Function
 * 
 * 功能：
 * - 租户专属的物料库存管理
 * - 物料入库出库操作
 * - 库存预警和统计分析
 * - 严格的租户数据隔离
 */

const cloud = require('wx-server-sdk');
const { apiHandler, ValidationHelper, PaginationHelper, APIError, ERROR_CODES } = require('../../../utils/api-standard');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 云函数入口函数
 */
exports.main = apiHandler(async (params, context) => {
  const { action, data } = params;

  switch (action) {
    case 'list':
      return await getMaterialList(params, context);
    case 'detail':
      return await getMaterialDetail(params, context);
    case 'create':
      return await createMaterial(params, context);
    case 'update':
      return await updateMaterial(params, context);
    case 'delete':
      return await deleteMaterial(params, context);
    case 'statistics':
      return await getMaterialStatistics(params, context);
    case 'stock_operation':
      return await handleStockOperation(params, context);
    case 'low_stock_alert':
      return await getLowStockAlert(params, context);
    case 'expiry_alert':
      return await getExpiryAlert(params, context);
    default:
      throw new APIError('不支持的操作类型', ERROR_CODES.INVALID_PARAMS);
  }
});

/**
 * 获取物料列表
 */
async function getMaterialList(params, context) {
  const { page, limit, data } = params;
  const { user, secureQuery, logDataAccess } = context;
  
  // 构建查询条件
  const filters = data.filters || {};
  let baseQuery = {};
  
  // 关键词搜索
  if (filters.keyword) {
    baseQuery.$or = [
      { name: _.regex({ regexp: filters.keyword, options: 'i' }) },
      { brand: _.regex({ regexp: filters.keyword, options: 'i' }) }
    ];
  }
  
  // 物料类型筛选
  if (filters.material_type) {
    baseQuery.material_type = filters.material_type;
  }
  
  // 库存预警筛选
  if (filters.low_stock) {
    baseQuery = _.and([
      baseQuery,
      _.expr(_.lte(['$current_stock', '$min_stock']))
    ]);
  }
  
  // 状态筛选（排除已删除）
  baseQuery.status = _.neq('deleted');

  // 应用数据隔离
  const secureQueryCondition = secureQuery('tenant_materials', baseQuery);
  
  // 分页参数处理
  const { skip } = PaginationHelper.validatePaginationParams(page, limit);

  try {
    // 获取总数
    const countResult = await db.collection('tenant_materials')
      .where(secureQueryCondition)
      .count();

    // 获取列表数据
    const listResult = await db.collection('tenant_materials')
      .where(secureQueryCondition)
      .orderBy('created_at', 'desc')
      .skip(skip)
      .limit(limit)
      .get();

    // 记录数据访问日志
    await logDataAccess('tenant_materials', 'read', secureQueryCondition, listResult.data);

    // 返回分页结果
    return PaginationHelper.createPaginationResponse(
      listResult.data,
      page,
      limit,
      countResult.total
    );
  } catch (error) {
    console.error('获取物料列表失败:', error);
    throw new APIError('获取物料列表失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 获取物料详情
 */
async function getMaterialDetail(params, context) {
  const { data } = params;
  const { user, secureQuery, logDataAccess } = context;
  
  ValidationHelper.validateRequired(data, ['id']);
  
  const secureQueryCondition = secureQuery('tenant_materials', { 
    _id: data.id,
    status: _.neq('deleted')
  });

  try {
    const result = await db.collection('tenant_materials')
      .where(secureQueryCondition)
      .get();

    if (result.data.length === 0) {
      throw new APIError('物料不存在或无权限访问', ERROR_CODES.RESOURCE_NOT_FOUND);
    }

    const material = result.data[0];

    // 获取相关的库存操作记录
    const stockRecords = await db.collection('material_stock_records')
      .where(secureQuery('material_stock_records', { material_id: data.id }))
      .orderBy('created_at', 'desc')
      .limit(10)
      .get();

    const materialDetail = {
      ...material,
      recent_stock_records: stockRecords.data
    };

    // 记录数据访问日志
    await logDataAccess('tenant_materials', 'read', secureQueryCondition, [materialDetail]);

    return materialDetail;
  } catch (error) {
    if (error instanceof APIError) {
      throw error;
    }
    console.error('获取物料详情失败:', error);
    throw new APIError('获取物料详情失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 创建物料
 */
async function createMaterial(params, context) {
  const { data } = params;
  const { user, validateData, logDataAccess } = context;
  
  // 参数验证
  ValidationHelper.validateRequired(data, ['name', 'material_type', 'unit']);
  ValidationHelper.validateLength(data.name, 1, 100, '物料名称');
  
  if (data.current_stock && data.current_stock < 0) {
    throw new APIError('库存数量不能为负数', ERROR_CODES.INVALID_PARAMS);
  }
  
  if (data.unit_price && data.unit_price < 0) {
    throw new APIError('单价不能为负数', ERROR_CODES.INVALID_PARAMS);
  }

  // 数据安全验证和处理
  const materialData = validateData('tenant_materials', {
    name: data.name,
    material_type: data.material_type,
    brand: data.brand || '',
    specification: data.specification || '',
    unit: data.unit,
    current_stock: parseFloat(data.current_stock) || 0,
    min_stock: parseFloat(data.min_stock) || 0,
    max_stock: parseFloat(data.max_stock) || 0,
    unit_price: parseFloat(data.unit_price) || 0,
    supplier: data.supplier || '',
    purchase_date: data.purchase_date ? new Date(data.purchase_date) : new Date(),
    expiry_date: data.expiry_date ? new Date(data.expiry_date) : null,
    storage_location: data.storage_location || '',
    notes: data.notes || '',
    status: 'in_stock',
    created_at: new Date(),
    updated_at: new Date()
  });

  try {
    const result = await db.collection('tenant_materials').add({
      data: materialData
    });

    const createdMaterial = {
      _id: result._id,
      ...materialData
    };

    // 记录初始库存（如果有）
    if (materialData.current_stock > 0) {
      await recordStockOperation(result._id, 'initial', materialData.current_stock, '初始库存', user);
    }

    // 记录数据访问日志
    await logDataAccess('tenant_materials', 'create', {}, [createdMaterial]);

    return createdMaterial;
  } catch (error) {
    console.error('创建物料失败:', error);
    throw new APIError('创建物料失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 更新物料
 */
async function updateMaterial(params, context) {
  const { data } = params;
  const { user, secureQuery, logDataAccess } = context;
  
  ValidationHelper.validateRequired(data, ['id']);
  
  // 检查物料是否存在且有权限访问
  const existingMaterial = await db.collection('tenant_materials')
    .where(secureQuery('tenant_materials', { 
      _id: data.id,
      status: _.neq('deleted')
    }))
    .get();

  if (existingMaterial.data.length === 0) {
    throw new APIError('物料不存在或无权限访问', ERROR_CODES.RESOURCE_NOT_FOUND);
  }

  // 准备更新数据
  const updateData = {
    updated_at: new Date()
  };

  // 只更新提供的字段
  const allowedFields = [
    'name', 'brand', 'specification', 'unit', 'min_stock', 'max_stock', 
    'unit_price', 'supplier', 'expiry_date', 'storage_location', 'notes'
  ];
  
  allowedFields.forEach(field => {
    if (data[field] !== undefined) {
      if (field === 'expiry_date' && data[field]) {
        updateData[field] = new Date(data[field]);
      } else {
        updateData[field] = data[field];
      }
    }
  });

  // 数据验证
  if (updateData.name) {
    ValidationHelper.validateLength(updateData.name, 1, 100, '物料名称');
  }
  
  if (updateData.unit_price && updateData.unit_price < 0) {
    throw new APIError('单价不能为负数', ERROR_CODES.INVALID_PARAMS);
  }

  try {
    await db.collection('tenant_materials').doc(data.id).update({
      data: updateData
    });

    // 获取更新后的数据
    const updatedMaterial = await db.collection('tenant_materials').doc(data.id).get();

    // 记录数据访问日志
    await logDataAccess('tenant_materials', 'update', { _id: data.id }, [updatedMaterial.data]);

    return updatedMaterial.data;
  } catch (error) {
    console.error('更新物料失败:', error);
    throw new APIError('更新物料失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 删除物料
 */
async function deleteMaterial(params, context) {
  const { data } = params;
  const { user, secureQuery, logDataAccess } = context;
  
  ValidationHelper.validateRequired(data, ['id']);
  
  // 检查物料是否存在且有权限访问
  const existingMaterial = await db.collection('tenant_materials')
    .where(secureQuery('tenant_materials', { 
      _id: data.id,
      status: _.neq('deleted')
    }))
    .get();

  if (existingMaterial.data.length === 0) {
    throw new APIError('物料不存在或无权限访问', ERROR_CODES.RESOURCE_NOT_FOUND);
  }

  try {
    // 软删除：更新状态为deleted
    await db.collection('tenant_materials').doc(data.id).update({
      data: {
        status: 'deleted',
        deleted_at: new Date(),
        updated_at: new Date()
      }
    });

    // 记录数据访问日志
    await logDataAccess('tenant_materials', 'delete', { _id: data.id }, [{ _id: data.id }]);

    return { id: data.id, message: '物料删除成功' };
  } catch (error) {
    console.error('删除物料失败:', error);
    throw new APIError('删除物料失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 获取物料统计信息
 */
async function getMaterialStatistics(params, context) {
  const { user, secureQuery, logDataAccess } = context;
  
  const baseQuery = secureQuery('tenant_materials', { status: _.neq('deleted') });

  try {
    // 总体统计
    const totalMaterials = await db.collection('tenant_materials').where(baseQuery).count();
    
    // 库存预警统计
    const lowStockCount = await db.collection('tenant_materials')
      .where(_.and([
        baseQuery,
        _.expr(_.lte(['$current_stock', '$min_stock']))
      ]))
      .count();
    
    // 总价值统计
    const valueStats = await db.collection('tenant_materials')
      .aggregate()
      .match(baseQuery)
      .group({
        _id: null,
        totalValue: _.sum(_.multiply(['$current_stock', '$unit_price']))
      })
      .end();

    const statistics = {
      totalMaterials: totalMaterials.total,
      lowStockCount: lowStockCount.total,
      totalValue: valueStats.list[0]?.totalValue || 0
    };

    // 记录数据访问日志
    await logDataAccess('tenant_materials', 'read', baseQuery, [statistics]);

    return statistics;
  } catch (error) {
    console.error('获取物料统计失败:', error);
    throw new APIError('获取物料统计失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 处理库存操作
 */
async function handleStockOperation(params, context) {
  const { data } = params;
  const { user, secureQuery, logDataAccess } = context;
  
  ValidationHelper.validateRequired(data, ['id', 'operation', 'quantity']);
  
  const { id, operation, quantity, remark } = data;
  
  if (!['in', 'out'].includes(operation)) {
    throw new APIError('无效的库存操作类型', ERROR_CODES.INVALID_PARAMS);
  }
  
  if (quantity <= 0) {
    throw new APIError('操作数量必须大于0', ERROR_CODES.INVALID_PARAMS);
  }

  // 获取当前物料信息
  const materialResult = await db.collection('tenant_materials')
    .where(secureQuery('tenant_materials', { 
      _id: id,
      status: _.neq('deleted')
    }))
    .get();

  if (materialResult.data.length === 0) {
    throw new APIError('物料不存在或无权限访问', ERROR_CODES.RESOURCE_NOT_FOUND);
  }

  const material = materialResult.data[0];
  
  // 检查出库数量
  if (operation === 'out' && quantity > material.current_stock) {
    throw new APIError('出库数量不能超过当前库存', ERROR_CODES.BUSINESS_ERROR);
  }

  try {
    // 计算新库存
    const newStock = operation === 'in' 
      ? material.current_stock + quantity 
      : material.current_stock - quantity;

    // 更新库存
    await db.collection('tenant_materials').doc(id).update({
      data: {
        current_stock: newStock,
        updated_at: new Date()
      }
    });

    // 记录库存操作
    await recordStockOperation(id, operation, quantity, remark, user);

    // 记录数据访问日志
    await logDataAccess('tenant_materials', 'stock_operation', { _id: id }, [{
      operation,
      quantity,
      newStock
    }]);

    return {
      id,
      operation,
      quantity,
      oldStock: material.current_stock,
      newStock,
      message: `${operation === 'in' ? '入库' : '出库'}操作成功`
    };
  } catch (error) {
    console.error('库存操作失败:', error);
    throw new APIError('库存操作失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 记录库存操作
 */
async function recordStockOperation(materialId, operation, quantity, remark, user) {
  try {
    await db.collection('material_stock_records').add({
      data: {
        tenant_id: user.tenant_id,
        user_id: user._id,
        material_id: materialId,
        operation_type: operation,
        quantity: quantity,
        remark: remark || '',
        operator_name: user.nickname || user.name,
        created_at: new Date()
      }
    });
  } catch (error) {
    console.error('记录库存操作失败:', error);
  }
}

/**
 * 获取库存预警
 */
async function getLowStockAlert(params, context) {
  const { user, secureQuery, logDataAccess } = context;
  
  const baseQuery = _.and([
    secureQuery('tenant_materials', { status: _.neq('deleted') }),
    _.expr(_.lte(['$current_stock', '$min_stock']))
  ]);

  try {
    const result = await db.collection('tenant_materials')
      .where(baseQuery)
      .orderBy('current_stock', 'asc')
      .get();

    // 记录数据访问日志
    await logDataAccess('tenant_materials', 'read', baseQuery, result.data);

    return result.data;
  } catch (error) {
    console.error('获取库存预警失败:', error);
    throw new APIError('获取库存预警失败', ERROR_CODES.DATABASE_ERROR);
  }
}

/**
 * 获取过期预警
 */
async function getExpiryAlert(params, context) {
  const { user, secureQuery, logDataAccess } = context;
  
  const thirtyDaysLater = new Date();
  thirtyDaysLater.setDate(thirtyDaysLater.getDate() + 30);
  
  const baseQuery = _.and([
    secureQuery('tenant_materials', { 
      status: _.neq('deleted'),
      expiry_date: _.exists(true)
    }),
    _.or([
      { expiry_date: _.lte(thirtyDaysLater) },
      { expiry_date: _.lte(new Date()) }
    ])
  ]);

  try {
    const result = await db.collection('tenant_materials')
      .where(baseQuery)
      .orderBy('expiry_date', 'asc')
      .get();

    // 记录数据访问日志
    await logDataAccess('tenant_materials', 'read', baseQuery, result.data);

    return result.data;
  } catch (error) {
    console.error('获取过期预警失败:', error);
    throw new APIError('获取过期预警失败', ERROR_CODES.DATABASE_ERROR);
  }
}
