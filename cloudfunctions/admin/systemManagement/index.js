// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { action, data, configId, announcementId, page = 1, limit = 20 } = event
    const { openid } = wxContext
    
    if (!openid) {
      return {
        success: false,
        message: '用户未登录',
        code: 'NOT_AUTHENTICATED'
      }
    }
    
    // 获取用户信息
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userQuery.data.length === 0) {
      return {
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND'
      }
    }
    
    const user = userQuery.data[0]
    
    // 检查管理员权限
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return {
        success: false,
        message: '无管理员权限',
        code: 'INSUFFICIENT_PERMISSIONS'
      }
    }
    
    switch (action) {
      // 系统配置相关
      case 'getConfig':
        return await getSystemConfig(user, data)
      case 'updateConfig':
        return await updateSystemConfig(user, configId, data)
      case 'getConfigList':
        return await getConfigList(user, page, limit, data)
      
      // 公告管理
      case 'getAnnouncements':
        return await getAnnouncements(user, page, limit, data)
      case 'createAnnouncement':
        return await createAnnouncement(user, data)
      case 'updateAnnouncement':
        return await updateAnnouncement(user, announcementId, data)
      case 'deleteAnnouncement':
        return await deleteAnnouncement(user, announcementId)
      case 'publishAnnouncement':
        return await publishAnnouncement(user, announcementId)
      
      // 知识库管理
      case 'getKnowledgeList':
        return await getKnowledgeList(user, page, limit, data)
      case 'createKnowledge':
        return await createKnowledge(user, data)
      case 'updateKnowledge':
        return await updateKnowledge(user, data.id, data)
      case 'deleteKnowledge':
        return await deleteKnowledge(user, data.id)
      
      // 系统统计
      case 'getSystemStats':
        return await getSystemStats(user)
      
      default:
        return {
          success: false,
          message: '不支持的操作',
          code: 'UNSUPPORTED_ACTION'
        }
    }
    
  } catch (error) {
    console.error('系统管理操作失败:', error)
    return {
      success: false,
      message: '操作失败',
      code: 'OPERATION_ERROR',
      error: error.message
    }
  }
}

// 获取系统配置
async function getSystemConfig(user, filters = {}) {
  const query = {
    tenant_id: user.role === 'super_admin' ? null : user.tenant_id
  }
  
  if (filters.category) {
    query.category = filters.category
  }
  
  if (filters.key) {
    query.key = filters.key
  }
  
  const configResult = await db.collection('system_config').where(query).get()
  
  // 如果是普通管理员，过滤掉非公开配置
  let configs = configResult.data
  if (user.role !== 'super_admin') {
    configs = configs.filter(config => config.is_public !== false)
  }
  
  return {
    success: true,
    message: '获取系统配置成功',
    data: configs
  }
}

// 更新系统配置
async function updateSystemConfig(user, configId, data) {
  if (!configId) {
    return {
      success: false,
      message: '配置ID不能为空',
      code: 'CONFIG_ID_REQUIRED'
    }
  }
  
  // 检查配置是否存在
  const configResult = await db.collection('system_config').doc(configId).get()
  
  if (!configResult.data) {
    return {
      success: false,
      message: '配置不存在',
      code: 'CONFIG_NOT_FOUND'
    }
  }
  
  const config = configResult.data
  
  // 权限检查
  if (user.role !== 'super_admin' && config.tenant_id !== user.tenant_id) {
    return {
      success: false,
      message: '无权限修改该配置',
      code: 'ACCESS_DENIED'
    }
  }
  
  const updateData = {
    value: data.value,
    description: data.description,
    updated_at: new Date()
  }
  
  await db.collection('system_config').doc(configId).update({
    data: updateData
  })
  
  return {
    success: true,
    message: '更新系统配置成功',
    data: {
      id: configId,
      ...updateData
    }
  }
}

// 获取公告列表
async function getAnnouncements(user, page, limit, filters = {}) {
  const skip = (page - 1) * limit
  
  let query = {
    tenant_id: user.role === 'super_admin' ? null : user.tenant_id
  }
  
  if (filters.status) {
    query.status = filters.status
  }
  
  if (filters.type) {
    query.type = filters.type
  }
  
  const countResult = await db.collection('announcements').where(query).count()
  const listResult = await db.collection('announcements')
    .where(query)
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(limit)
    .get()
  
  return {
    success: true,
    message: '获取公告列表成功',
    data: {
      list: listResult.data,
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        pages: Math.ceil(countResult.total / limit)
      }
    }
  }
}

// 创建公告
async function createAnnouncement(user, data) {
  const { title, content, type, priority, target_users, expire_at } = data
  
  if (!title || !content) {
    return {
      success: false,
      message: '标题和内容不能为空',
      code: 'REQUIRED_FIELDS_MISSING'
    }
  }
  
  const announcementData = {
    tenant_id: user.role === 'super_admin' ? null : user.tenant_id,
    title: title,
    content: content,
    type: type || 'system',
    priority: priority || 'normal',
    target_users: target_users || [],
    status: 'draft',
    publish_at: null,
    expire_at: expire_at ? new Date(expire_at) : null,
    read_count: 0,
    created_at: new Date(),
    updated_at: new Date()
  }
  
  const result = await db.collection('announcements').add({
    data: announcementData
  })
  
  return {
    success: true,
    message: '创建公告成功',
    data: {
      id: result._id,
      ...announcementData
    }
  }
}

// 发布公告
async function publishAnnouncement(user, announcementId) {
  if (!announcementId) {
    return {
      success: false,
      message: '公告ID不能为空',
      code: 'ANNOUNCEMENT_ID_REQUIRED'
    }
  }
  
  // 检查公告是否存在
  const announcementResult = await db.collection('announcements').doc(announcementId).get()
  
  if (!announcementResult.data) {
    return {
      success: false,
      message: '公告不存在',
      code: 'ANNOUNCEMENT_NOT_FOUND'
    }
  }
  
  const announcement = announcementResult.data
  
  // 权限检查
  if (user.role !== 'super_admin' && announcement.tenant_id !== user.tenant_id) {
    return {
      success: false,
      message: '无权限发布该公告',
      code: 'ACCESS_DENIED'
    }
  }
  
  await db.collection('announcements').doc(announcementId).update({
    data: {
      status: 'published',
      publish_at: new Date(),
      updated_at: new Date()
    }
  })
  
  return {
    success: true,
    message: '发布公告成功',
    data: {
      id: announcementId,
      status: 'published',
      publish_at: new Date()
    }
  }
}

// 获取知识库列表
async function getKnowledgeList(user, page, limit, filters = {}) {
  const skip = (page - 1) * limit
  
  let query = {
    tenant_id: user.role === 'super_admin' ? null : user.tenant_id,
    status: 'published'
  }
  
  if (filters.category) {
    query.category = filters.category
  }
  
  if (filters.keyword) {
    query.title = db.RegExp({
      regexp: filters.keyword,
      options: 'i'
    })
  }
  
  const countResult = await db.collection('knowledge_base').where(query).count()
  const listResult = await db.collection('knowledge_base')
    .where(query)
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(limit)
    .get()
  
  return {
    success: true,
    message: '获取知识库列表成功',
    data: {
      list: listResult.data,
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        pages: Math.ceil(countResult.total / limit)
      }
    }
  }
}

// 获取系统统计数据
async function getSystemStats(user) {
  try {
    const tenantQuery = user.role === 'super_admin' ? {} : { tenant_id: user.tenant_id }
    
    // 用户统计
    const userCount = await db.collection('users').where(tenantQuery).count()
    
    // 鹅群统计
    const flockCount = await db.collection('flocks').where(tenantQuery).count()
    
    // 健康记录统计
    const healthRecordCount = await db.collection('health_records').where(tenantQuery).count()
    
    // 订单统计
    const orderCount = await db.collection('orders').where(tenantQuery).count()
    
    // 最近30天活跃用户
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const activeUserCount = await db.collection('users').where({
      ...tenantQuery,
      last_login_at: _.gte(thirtyDaysAgo)
    }).count()
    
    return {
      success: true,
      message: '获取系统统计数据成功',
      data: {
        user_count: userCount.total,
        flock_count: flockCount.total,
        health_record_count: healthRecordCount.total,
        order_count: orderCount.total,
        active_user_count: activeUserCount.total,
        stats_date: new Date().toISOString()
      }
    }
    
  } catch (error) {
    console.error('获取系统统计失败:', error)
    return {
      success: false,
      message: '获取系统统计失败',
      code: 'STATS_ERROR',
      error: error.message
    }
  }
}
