/**
 * 今日鹅价管理云函数
 * Platform Goose Price Management Cloud Function
 * 
 * 功能：
 * - 发布全平台统一的鹅价信息
 * - 管理鹅价历史记录
 * - 提供鹅价趋势分析
 * - 支持多地区鹅价管理
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  const { OPENID } = cloud.getWXContext();

  try {
    // 权限验证：只有平台管理员可以操作
    const userInfo = await getUserInfo(OPENID);
    if (!hasPermission(userInfo.role, 'PLATFORM_GOOSE_PRICE_MANAGE')) {
      return {
        success: false,
        error: '权限不足：需要平台鹅价管理权限'
      };
    }

    switch (action) {
      case 'publishPrice':
        return await publishGoosePrice(data, userInfo);
      case 'getPriceList':
        return await getPriceList(data);
      case 'getPriceHistory':
        return await getPriceHistory(data);
      case 'updatePrice':
        return await updateGoosePrice(data, userInfo);
      case 'deletePrice':
        return await deleteGoosePrice(data, userInfo);
      case 'getPriceTrend':
        return await getPriceTrend(data);
      case 'getRegionPrices':
        return await getRegionPrices(data);
      default:
        return {
          success: false,
          error: '不支持的操作类型'
        };
    }
  } catch (error) {
    console.error('今日鹅价管理云函数执行失败:', error);
    return {
      success: false,
      error: error.message || '服务器内部错误'
    };
  }
};

/**
 * 发布鹅价信息
 */
async function publishGoosePrice(data, userInfo) {
  const { region, breed, price, unit, market, description } = data;
  
  // 数据验证
  if (!region || !breed || !price) {
    return {
      success: false,
      error: '缺少必要参数：地区、品种、价格'
    };
  }

  const priceData = {
    region,
    breed,
    price: parseFloat(price),
    unit: unit || '元/斤',
    market: market || '综合市场',
    description: description || '',
    publish_date: new Date(),
    publisher_id: userInfo._id,
    publisher_name: userInfo.nickname,
    status: 'active',
    view_count: 0,
    created_at: new Date(),
    updated_at: new Date()
  };

  try {
    const result = await db.collection('goose_prices').add({
      data: priceData
    });

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'publish_goose_price',
      target: 'goose_prices',
      target_id: result._id,
      details: `发布鹅价：${region} ${breed} ${price}${unit}`
    });

    return {
      success: true,
      data: {
        id: result._id,
        ...priceData
      }
    };
  } catch (error) {
    console.error('发布鹅价失败:', error);
    return {
      success: false,
      error: '发布鹅价失败'
    };
  }
}

/**
 * 获取鹅价列表
 */
async function getPriceList(data) {
  const { page = 1, limit = 20, region, breed, sortBy = 'publish_date' } = data;
  
  let query = db.collection('goose_prices').where({
    status: 'active'
  });

  // 添加筛选条件
  if (region) {
    query = query.where({
      region: region
    });
  }
  
  if (breed) {
    query = query.where({
      breed: breed
    });
  }

  try {
    const result = await query
      .orderBy(sortBy, 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();

    // 获取总数
    const countResult = await query.count();

    return {
      success: true,
      data: {
        list: result.data,
        total: countResult.total,
        page,
        limit,
        totalPages: Math.ceil(countResult.total / limit)
      }
    };
  } catch (error) {
    console.error('获取鹅价列表失败:', error);
    return {
      success: false,
      error: '获取鹅价列表失败'
    };
  }
}

/**
 * 获取鹅价历史记录
 */
async function getPriceHistory(data) {
  const { region, breed, days = 30 } = data;
  
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  let query = db.collection('goose_prices').where({
    status: 'active',
    publish_date: _.gte(startDate)
  });

  if (region) {
    query = query.where({ region });
  }
  
  if (breed) {
    query = query.where({ breed });
  }

  try {
    const result = await query
      .orderBy('publish_date', 'desc')
      .get();

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('获取鹅价历史失败:', error);
    return {
      success: false,
      error: '获取鹅价历史失败'
    };
  }
}

/**
 * 更新鹅价信息
 */
async function updateGoosePrice(data, userInfo) {
  const { id, ...updateData } = data;
  
  if (!id) {
    return {
      success: false,
      error: '缺少价格记录ID'
    };
  }

  try {
    const result = await db.collection('goose_prices').doc(id).update({
      data: {
        ...updateData,
        updated_at: new Date(),
        updater_id: userInfo._id,
        updater_name: userInfo.nickname
      }
    });

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'update_goose_price',
      target: 'goose_prices',
      target_id: id,
      details: `更新鹅价信息`
    });

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('更新鹅价失败:', error);
    return {
      success: false,
      error: '更新鹅价失败'
    };
  }
}

/**
 * 删除鹅价信息
 */
async function deleteGoosePrice(data, userInfo) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      error: '缺少价格记录ID'
    };
  }

  try {
    // 软删除：更新状态为deleted
    const result = await db.collection('goose_prices').doc(id).update({
      data: {
        status: 'deleted',
        deleted_at: new Date(),
        deleter_id: userInfo._id,
        deleter_name: userInfo.nickname
      }
    });

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'delete_goose_price',
      target: 'goose_prices',
      target_id: id,
      details: `删除鹅价信息`
    });

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('删除鹅价失败:', error);
    return {
      success: false,
      error: '删除鹅价失败'
    };
  }
}

/**
 * 获取鹅价趋势分析
 */
async function getPriceTrend(data) {
  const { region, breed, days = 30 } = data;
  
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  try {
    // 使用聚合查询获取价格趋势
    const result = await db.collection('goose_prices')
      .aggregate()
      .match({
        status: 'active',
        publish_date: _.gte(startDate),
        ...(region && { region }),
        ...(breed && { breed })
      })
      .group({
        _id: {
          date: '$publish_date',
          region: '$region',
          breed: '$breed'
        },
        avgPrice: _.avg('$price'),
        maxPrice: _.max('$price'),
        minPrice: _.min('$price'),
        count: _.sum(1)
      })
      .sort({
        '_id.date': 1
      })
      .end();

    return {
      success: true,
      data: result.list
    };
  } catch (error) {
    console.error('获取鹅价趋势失败:', error);
    return {
      success: false,
      error: '获取鹅价趋势失败'
    };
  }
}

/**
 * 获取各地区鹅价
 */
async function getRegionPrices(data) {
  const { breed } = data;
  
  try {
    let query = db.collection('goose_prices')
      .aggregate()
      .match({
        status: 'active',
        ...(breed && { breed })
      });

    const result = await query
      .group({
        _id: '$region',
        avgPrice: _.avg('$price'),
        latestPrice: _.last('$price'),
        latestDate: _.last('$publish_date'),
        count: _.sum(1)
      })
      .sort({
        avgPrice: -1
      })
      .end();

    return {
      success: true,
      data: result.list
    };
  } catch (error) {
    console.error('获取地区鹅价失败:', error);
    return {
      success: false,
      error: '获取地区鹅价失败'
    };
  }
}

/**
 * 获取用户信息
 */
async function getUserInfo(openid) {
  try {
    const result = await db.collection('users').where({
      openid: openid
    }).get();
    
    return result.data[0] || null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
}

/**
 * 权限检查
 */
function hasPermission(userRole, permission) {
  // 这里应该调用权限系统进行检查
  // 简化实现：超级管理员和平台管理员有权限
  return ['super_admin', 'platform_admin'].includes(userRole);
}

/**
 * 记录平台操作日志
 */
async function logPlatformOperation(logData) {
  try {
    await db.collection('platform_operation_logs').add({
      data: {
        ...logData,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('记录操作日志失败:', error);
  }
}
