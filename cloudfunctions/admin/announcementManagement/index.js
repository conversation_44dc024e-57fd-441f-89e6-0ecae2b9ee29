/**
 * 平台公告管理云函数
 * Platform Announcement Management Cloud Function
 * 
 * 功能：
 * - 发布面向所有租户的系统通知
 * - 管理公告的生命周期
 * - 支持定时发布和过期管理
 * - 提供公告阅读统计
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  const { OPENID } = cloud.getWXContext();

  try {
    // 权限验证：只有平台管理员可以操作
    const userInfo = await getUserInfo(OPENID);
    if (!hasPermission(userInfo.role, 'PLATFORM_ANNOUNCEMENT_MANAGE')) {
      return {
        success: false,
        error: '权限不足：需要平台公告管理权限'
      };
    }

    switch (action) {
      case 'createAnnouncement':
        return await createAnnouncement(data, userInfo);
      case 'getAnnouncementList':
        return await getAnnouncementList(data);
      case 'getAnnouncementDetail':
        return await getAnnouncementDetail(data);
      case 'updateAnnouncement':
        return await updateAnnouncement(data, userInfo);
      case 'publishAnnouncement':
        return await publishAnnouncement(data, userInfo);
      case 'deleteAnnouncement':
        return await deleteAnnouncement(data, userInfo);
      case 'getAnnouncementStats':
        return await getAnnouncementStats(data);
      case 'getPublicAnnouncements':
        return await getPublicAnnouncements(data);
      default:
        return {
          success: false,
          error: '不支持的操作类型'
        };
    }
  } catch (error) {
    console.error('平台公告管理云函数执行失败:', error);
    return {
      success: false,
      error: error.message || '服务器内部错误'
    };
  }
};

/**
 * 创建公告
 */
async function createAnnouncement(data, userInfo) {
  const { 
    title, 
    content, 
    type = 'system', 
    priority = 'normal',
    target_tenants = [], // 空数组表示所有租户
    publish_at,
    expire_at
  } = data;
  
  // 数据验证
  if (!title || !content) {
    return {
      success: false,
      error: '缺少必要参数：标题、内容'
    };
  }

  const announcementData = {
    tenant_id: null, // null表示平台级公告
    title,
    content,
    type, // system, maintenance, feature, promotion
    priority, // low, normal, high, urgent
    target_tenants, // 目标租户列表，空数组表示所有租户
    status: 'draft', // draft, published, archived
    publish_at: publish_at ? new Date(publish_at) : null,
    expire_at: expire_at ? new Date(expire_at) : null,
    read_count: 0,
    author_id: userInfo._id,
    author_name: userInfo.nickname,
    created_at: new Date(),
    updated_at: new Date()
  };

  try {
    const result = await db.collection('announcements').add({
      data: announcementData
    });

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'create_announcement',
      target: 'announcements',
      target_id: result._id,
      details: `创建公告：${title}`
    });

    return {
      success: true,
      data: {
        id: result._id,
        ...announcementData
      }
    };
  } catch (error) {
    console.error('创建公告失败:', error);
    return {
      success: false,
      error: '创建公告失败'
    };
  }
}

/**
 * 获取公告列表
 */
async function getAnnouncementList(data) {
  const { 
    page = 1, 
    limit = 20, 
    status, 
    type, 
    priority,
    sortBy = 'created_at' 
  } = data;
  
  let whereCondition = {
    tenant_id: null // 只获取平台级公告
  };

  // 添加筛选条件
  if (status) {
    whereCondition.status = status;
  }
  
  if (type) {
    whereCondition.type = type;
  }
  
  if (priority) {
    whereCondition.priority = priority;
  }

  try {
    const result = await db.collection('announcements')
      .where(whereCondition)
      .orderBy(sortBy, 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();

    // 获取总数
    const countResult = await db.collection('announcements')
      .where(whereCondition)
      .count();

    return {
      success: true,
      data: {
        list: result.data,
        total: countResult.total,
        page,
        limit,
        totalPages: Math.ceil(countResult.total / limit)
      }
    };
  } catch (error) {
    console.error('获取公告列表失败:', error);
    return {
      success: false,
      error: '获取公告列表失败'
    };
  }
}

/**
 * 获取公告详情
 */
async function getAnnouncementDetail(data) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      error: '缺少公告ID'
    };
  }

  try {
    const result = await db.collection('announcements').doc(id).get();
    
    if (!result.data) {
      return {
        success: false,
        error: '公告不存在'
      };
    }

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('获取公告详情失败:', error);
    return {
      success: false,
      error: '获取公告详情失败'
    };
  }
}

/**
 * 更新公告
 */
async function updateAnnouncement(data, userInfo) {
  const { id, ...updateData } = data;
  
  if (!id) {
    return {
      success: false,
      error: '缺少公告ID'
    };
  }

  try {
    const result = await db.collection('announcements').doc(id).update({
      data: {
        ...updateData,
        updated_at: new Date(),
        updater_id: userInfo._id,
        updater_name: userInfo.nickname
      }
    });

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'update_announcement',
      target: 'announcements',
      target_id: id,
      details: `更新公告`
    });

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('更新公告失败:', error);
    return {
      success: false,
      error: '更新公告失败'
    };
  }
}

/**
 * 发布公告
 */
async function publishAnnouncement(data, userInfo) {
  const { id, publish_at } = data;
  
  if (!id) {
    return {
      success: false,
      error: '缺少公告ID'
    };
  }

  try {
    const updateData = {
      status: 'published',
      publish_at: publish_at ? new Date(publish_at) : new Date(),
      updated_at: new Date(),
      publisher_id: userInfo._id,
      publisher_name: userInfo.nickname
    };

    const result = await db.collection('announcements').doc(id).update({
      data: updateData
    });

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'publish_announcement',
      target: 'announcements',
      target_id: id,
      details: `发布公告`
    });

    // 如果是紧急公告，可以考虑发送推送通知
    const announcementDetail = await db.collection('announcements').doc(id).get();
    if (announcementDetail.data.priority === 'urgent') {
      // 这里可以调用推送服务
      console.log('发送紧急公告推送通知');
    }

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('发布公告失败:', error);
    return {
      success: false,
      error: '发布公告失败'
    };
  }
}

/**
 * 删除公告
 */
async function deleteAnnouncement(data, userInfo) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      error: '缺少公告ID'
    };
  }

  try {
    // 软删除：更新状态为archived
    const result = await db.collection('announcements').doc(id).update({
      data: {
        status: 'archived',
        archived_at: new Date(),
        archiver_id: userInfo._id,
        archiver_name: userInfo.nickname
      }
    });

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'delete_announcement',
      target: 'announcements',
      target_id: id,
      details: `删除公告`
    });

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('删除公告失败:', error);
    return {
      success: false,
      error: '删除公告失败'
    };
  }
}

/**
 * 获取公告统计信息
 */
async function getAnnouncementStats(data) {
  try {
    // 获取各状态公告数量
    const statsResult = await db.collection('announcements')
      .aggregate()
      .match({
        tenant_id: null
      })
      .group({
        _id: '$status',
        count: _.sum(1)
      })
      .end();

    // 获取各类型公告数量
    const typeStatsResult = await db.collection('announcements')
      .aggregate()
      .match({
        tenant_id: null,
        status: 'published'
      })
      .group({
        _id: '$type',
        count: _.sum(1),
        totalReads: _.sum('$read_count')
      })
      .end();

    return {
      success: true,
      data: {
        statusStats: statsResult.list,
        typeStats: typeStatsResult.list
      }
    };
  } catch (error) {
    console.error('获取公告统计失败:', error);
    return {
      success: false,
      error: '获取公告统计失败'
    };
  }
}

/**
 * 获取公开公告（供小程序端调用）
 */
async function getPublicAnnouncements(data) {
  const { page = 1, limit = 10, type } = data;
  
  let whereCondition = {
    tenant_id: null,
    status: 'published',
    publish_at: _.lte(new Date())
  };

  // 检查是否过期
  whereCondition = _.and([
    whereCondition,
    _.or([
      { expire_at: null },
      { expire_at: _.gte(new Date()) }
    ])
  ]);

  if (type) {
    whereCondition.type = type;
  }

  try {
    const result = await db.collection('announcements')
      .where(whereCondition)
      .field({
        title: true,
        content: true,
        type: true,
        priority: true,
        publish_at: true,
        read_count: true
      })
      .orderBy('priority', 'desc')
      .orderBy('publish_at', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('获取公开公告失败:', error);
    return {
      success: false,
      error: '获取公开公告失败'
    };
  }
}

/**
 * 获取用户信息
 */
async function getUserInfo(openid) {
  try {
    const result = await db.collection('users').where({
      openid: openid
    }).get();
    
    return result.data[0] || null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
}

/**
 * 权限检查
 */
function hasPermission(userRole, permission) {
  // 这里应该调用权限系统进行检查
  // 简化实现：超级管理员和平台管理员有权限
  return ['super_admin', 'platform_admin'].includes(userRole);
}

/**
 * 记录平台操作日志
 */
async function logPlatformOperation(logData) {
  try {
    await db.collection('platform_operation_logs').add({
      data: {
        ...logData,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('记录操作日志失败:', error);
  }
}
