// pages/shop/shop.js
const request = require('../../utils/request');
const { API } = require('../../constants/index.js');
const { shouldUseMockData, smartAPICall, showConfigStatus } = require('../../utils/api-config-switcher.js');

Page({
  data: {
    goods: [],
    originalGoods: [], // 原始商品数据
    categories: [
      { id: 0, name: '全部', active: true },
      { id: 'feed', name: '饵料', active: false },
      { id: 'medicine', name: '药品', active: false },
      { id: 'equipment', name: '设备', active: false },
      { id: 'supplement', name: '营养品', active: false },
      { id: 'cleaning', name: '清洁用品', active: false },
      { id: 'other', name: '其他', active: false }
    ],
    cartCount: 0,
    filteredGoods: [],
    searchKeyword: '',
    isLoading: true,
    hasError: false,
    pagination: {
      current: 1,
      total: 0,
      pageSize: 20
    },
    // 购物车拖拽相关数据
    cartPosition: {
      x: 30,
      y: 200
    },
    isDragging: false,
    startX: 0,
    startY: 0,
    startCartX: 0,
    startCartY: 0
  },

  onLoad: function (options) {
    // 页面加载时的初始化逻辑
    this.loadCartCount();
    this.loadProducts();
    this.loadCategories();
    // this.loadMockData();
    // 初始化拖拽功能
    this.initCartDrag();
  },

  onShow: function () {
    // 页面显示时重新加载购物车数量
    this.loadCartCount();
  },

  loadCategories: function() {
    // 加载商品分类
    // 实际项目中应该从 API 获取
  },

  onCategorySelect: function(e) {
    const categoryId = e.currentTarget.dataset.id;
    
    // 更新分类选中状态
    const categories = this.data.categories.map(category => ({
      ...category,
      active: category.id == categoryId
    }));
    
    this.setData({ categories });
    
    // 筛选商品
    this.filterGoods();
  },



  filterGoods: function() {
    const { originalGoods, categories, searchKeyword } = this.data;
    const activeCategory = categories.find(cat => cat.active);
    
    let filteredGoods = [...originalGoods];
    
    // 按分类筛选
    if (activeCategory && activeCategory.id !== 0) {
      filteredGoods = filteredGoods.filter(item => item.category === activeCategory.id);
    }
    
    // 按搜索关键词筛选
    if (searchKeyword) {
      filteredGoods = filteredGoods.filter(item => 
        item.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        item.description.toLowerCase().includes(searchKeyword.toLowerCase())
      );
    }
    
    this.setData({ filteredGoods });
  },
  // 获取模拟商品数据（仅返回数据，不设置状态）
  getMockProductsData: function() {
    return [
      {
        id: 1,
        name: '优质鹅饵料',
        description: '营养丰富，促进鹅类健康成长',
        price: 99.99,
        originalPrice: 129.99,
        category: '饲料',
        image: '/images/icons/goods1.png',
        stock: 1200,
        sales: 856
      },
      {
        id: 2,
        name: '智能养鹅生蛋',
        description: '新鲜鹅蛋，营养价值高',
        price: 45.6,
        originalPrice: 56.0,
        category: '蛋类',
        image: '/images/icons/goods2.png',
        stock: 856,
        sales: 234
      },
      {
        id: 3,
        name: '鹅舍清洁剂',
        description: '专业清洁，保持鹅舍卫生',
        price: 15.9,
        originalPrice: 19.9,
        category: '清洁用品',
        image: '/images/icons/goods3.png',
        stock: 445,
        sales: 123
      },
      {
        id: 4,
        name: '鹅用病毒治疗药',
        description: '专业治疗鹅类常见疾病',
        price: 68,
        originalPrice: 88,
        category: '医疗用品',
        image: '/images/icons/goods4.png',
        stock: 150,
        sales: 678
      }
    ];
  },

  loadMockData: function() {
    // 使用getMockProductsData获取模拟数据
    const mockGoods = this.getMockProductsData();

    this.setData({
      goods: mockGoods,
      originalGoods: mockGoods, // 保存原始数据用于筛选
      filteredGoods: mockGoods,
      isLoading: false
    });

    return mockGoods; // 同时返回数据，供其他方法使用
  },

  loadProducts: function() {
    this.setData({ isLoading: true });

    // 使用智能API调用，自动处理模拟数据回退
    smartAPICall('/shop/products', () => this.getMockProductsData(), {
      method: 'GET',
      data: {
        page: this.data.pagination.current,
        limit: this.data.pagination.pageSize
      },
      mockDelay: 500 // 模拟网络延迟
    }).then(result => {
      if (result.success) {
        let products;

        if (result.source === 'api') {
          // 来自真实API的数据，需要映射格式
          products = result.data.products.map(product => ({
            id: product.id,
            name: product.name,
            description: product.description,
            price: product.price,
            category: product.category,
            image: product.image,
            stock: product.stock,
            status: product.status,
            sales: product.salesCount || 0,
            tags: product.tags,
            specifications: product.specifications
          }));
        } else {
          // 来自模拟数据，直接使用
          products = result.data;
        }

        this.setData({
          goods: products,
          originalGoods: products,
          filteredGoods: products,
          pagination: {
            ...this.data.pagination,
            total: result.data.pagination?.total || products.length
          },
          isLoading: false,
          hasError: false
        });

        // 显示数据来源提示
        if (result.source === 'mock' || result.source === 'mock_fallback') {
          console.log('商城数据来源: 模拟数据');
        }
      } else {
        this.handleLoadError('数据加载失败');
      }
    }).catch(error => {
      console.error('商品数据加载失败:', error);
      this.handleLoadError(error.message);
    });
  },

  // 检查API可用性
  checkAPIAvailability: function() {
    return new Promise((resolve) => {
      // 简单的健康检查请求
      wx.request({
        url: API.ENDPOINTS.SHOP.PRODUCTS,
        method: 'GET',
        timeout: 3000, // 3秒超时
        success: (res) => {
          resolve(res.statusCode === 200 || res.statusCode === 404); // 404也表示服务器可达
        },
        fail: (error) => {
          console.log('API健康检查失败:', error);
          resolve(false);
        }
      });
    });
  },

  // 从API加载商品数据
  loadProductsFromAPI: function() {
    request.get(API.ENDPOINTS.SHOP.PRODUCTS, {
      page: this.data.pagination.current,
      limit: this.data.pagination.pageSize
    }).then(result => {
      if (result.success) {
        // 映射后端数据结构到前端格式
        const products = result.data.products.map(product => ({
          id: product.id,
          name: product.name,
          description: product.description,
          price: product.price,
          category: product.category,
          image: product.image,
          stock: product.stock,
          status: product.status,
          sales: product.salesCount || 0, // 后端salesCount映射到前端sales
          tags: product.tags,
          specifications: product.specifications
        }));

        this.setData({
          goods: products,
          originalGoods: products,
          filteredGoods: products,
          pagination: {
            ...this.data.pagination,
            total: result.data.pagination.total
          },
          isLoading: false,
          hasError: false
        });
      } else {
        this.handleLoadError(result.message);
      }
    }).catch(error => {
      console.error('API请求失败，使用模拟数据', error);
      this.loadMockData(); // fallback到模拟数据
      this.setData({ isLoading: false });
    });
  },

  loadCartCount: function() {
    try {
      const cart = wx.getStorageSync('cart') || [];
      const count = cart.reduce((total, item) => total + item.quantity, 0);
      this.setData({ cartCount: count });
    } catch (e) {
      try { logger.error && logger.error('获取购物车数据失败', e); } catch(_) {}
      this.setData({ cartCount: 0 });
    }
  },

  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    
    // 实时搜索（可以加防抖动优化）
    this.filterGoods();
  },

  onSearch: function() {
    // 执行搜索逻辑
    this.filterGoods();
    wx.showToast({
      title: `搜索: ${this.data.searchKeyword}`,
      icon: 'none',
      duration: 1500
    });
  },

  onGoodsTap: function(e) {
    const goodsId = e.currentTarget.dataset.id;
    if (goodsId) {
      wx.navigateTo({
        url: `/pages/shop/goods-detail?id=${goodsId}`
      });
    }
  },

  onImageError: function(e) {
    try { logger.warn && logger.warn('商品图片加载失败', e.detail.errMsg); } catch(_) {}
    
    // 设置默认图片 - 使用已有的包装图标作为商品默认图片
    const dataset = e.currentTarget.dataset;
    if (dataset && dataset.index !== undefined) {
      const goods = this.data.goods;
      goods[dataset.index].image = '/images/icons/package.png'; // 使用包装图标作为默认图片
      this.setData({
        goods: goods
      });
    }
  },

  handleLoadError: function(message) {
    this.setData({
      isLoading: false,
      hasError: true
    });
    
    wx.showToast({
      title: message || '加载失败',
      icon: 'none',
      duration: 2000
    });
  },

  // 初始化购物车拖拽功能
  initCartDrag: function() {
    const { getWindowInfo } = require('../../utils/system-info-helper');
    
    // 获取窗口信息来计算默认位置
    try {
      const windowInfo = getWindowInfo();
      const defaultX = windowInfo.windowWidth - 80; // 距离右边缘80px
      const defaultY = windowInfo.windowHeight * 0.6; // 屏幕高度的60%位置
      
      this.setData({
        cartPosition: {
          x: defaultX,
          y: defaultY
        }
      });
    } catch (error) {
      // 如果获取窗口信息失败，使用默认值
      this.setData({
        cartPosition: {
          x: 300,
          y: 400
        }
      });
    }
  },

  // 触摸开始
  onTouchStart: function(e) {
    const touch = e.touches[0];
    this.setData({
      isDragging: true,
      startX: touch.clientX,
      startY: touch.clientY,
      startCartX: this.data.cartPosition.x,
      startCartY: this.data.cartPosition.y
    });
  },

  // 触摸移动
  onTouchMove: function(e) {
    if (!this.data.isDragging) return;
    
    const touch = e.touches[0];
    const deltaX = touch.clientX - this.data.startX;
    const deltaY = touch.clientY - this.data.startY;
    
    const { getWindowInfo } = require('../../utils/system-info-helper');
    
    let newX = this.data.startCartX + deltaX;
    let newY = this.data.startCartY + deltaY;
    
    // 获取窗口信息来计算边界
    try {
      const windowInfo = getWindowInfo();
      const maxX = windowInfo.windowWidth - 60; // 购物车宽度60px
      const maxY = windowInfo.windowHeight - 60; // 购物车高度60px
      
      // 边界限制
      newX = Math.max(0, Math.min(newX, maxX));
      newY = Math.max(0, Math.min(newY, maxY));
    } catch (error) {
      // 如果获取窗口信息失败，使用默认边界
      newX = Math.max(0, Math.min(newX, 300));
      newY = Math.max(0, Math.min(newY, 600));
    }
    
    this.setData({
      cartPosition: {
        x: newX,
        y: newY
      }
    });
  },

  // 触摸结束
  onTouchEnd: function(e) {
    this.setData({
      isDragging: false
    });
    
    // 如果移动距离很小，认为是点击事件
    const touch = e.changedTouches[0];
    const deltaX = Math.abs(touch.clientX - this.data.startX);
    const deltaY = Math.abs(touch.clientY - this.data.startY);
    
    if (deltaX < 10 && deltaY < 10) {
      this.onCartTap();
    }
  },

  // 购物车点击
  onCartTap: function() {
    wx.navigateTo({
      url: '/pages/shop/cart'
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadProducts();
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom: function() {
    // 加载更多商品
    // 加载更多商品
  },

  // 分享功能
  onShareAppMessage: function() {
    return {
      title: '智慧养鹅 - 商城',
      path: '/pages/shop/shop'
    };
  }
});