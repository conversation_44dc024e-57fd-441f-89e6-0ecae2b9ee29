// pages/shop/checkout.js
const addressService = require('../../utils/helpers/address-service.js');
const OrderDataManager = require('../../utils/order-data-manager.js');

Page({
  data: {
    cartItems: [],
    selectedItems: [],
    totalPrice: 0,
    discountAmount: 0,
    actualAmount: 0,
    address: null, // 改为null，表示未选择地址
    note: ''
  },

  onLoad: function (options) {
    // 页面加载
    this.loadCheckoutData(options);
    this.loadDefaultAddress();
  },

  loadCheckoutData: function(options) {
    // 如果是从商品详情页直接购买
    if (options.goodsId && options.quantity) {
      // 模拟获取商品信息
      const goodsData = [
        {
          id: 1,
          name: '优质鹅饲料',
          price: 99.99,
          image: '/images/icons/goods1.png',
          quantity: parseInt(options.quantity)
        },
        {
          id: 2,
          name: '疫苗套装',
          price: 199.99,
          image: '/images/icons/goods2.png',
          quantity: parseInt(options.quantity)
        },
        {
          id: 3,
          name: '养殖设备',
          price: 299.99,
          image: '/images/icons/goods3.png',
          quantity: parseInt(options.quantity)
        }
      ];
      
      const goods = goodsData.find(item => item.id == options.goodsId);
      if (goods) {
        // 计算单项商品总价
        const itemsWithTotal = [{
          ...goods,
          total: (goods.price * goods.quantity).toFixed(2)
        }];
        
        const totalPrice = (goods.price * goods.quantity).toFixed(2);
        this.setData({
          cartItems: itemsWithTotal,
          totalPrice: totalPrice,
          actualAmount: totalPrice
        });
      }
      return;
    }
    
    // 如果是从购物车进入
    const cart = wx.getStorageSync('cart') || [];
    const selectedItems = cart.filter(item => item.selected !== false);
    
    if (selectedItems.length > 0) {
      // 为每个商品项添加总价字段
      const itemsWithTotal = selectedItems.map(item => {
        return {
          ...item,
          total: (item.price * item.quantity).toFixed(2)
        };
      });
      
      this.setData({
        cartItems: itemsWithTotal
      });
      
      this.calculateTotal();
    } else {
      // 如果没有选中的商品，返回购物车
      wx.showToast({
        title: '请选择商品',
        icon: 'none'
      });
      wx.navigateBack();
    }
  },

  calculateTotal: function() {
    let totalPrice = 0;
    
    this.data.cartItems.forEach(item => {
      totalPrice += item.price * item.quantity;
    });
    
    const finalTotalPrice = totalPrice.toFixed(2);
    const actualAmount = (totalPrice - this.data.discountAmount).toFixed(2);
    
    this.setData({
      totalPrice: finalTotalPrice,
      actualAmount: actualAmount
    });
  },

  onNoteInput: function(e) {
    this.setData({
      note: e.detail.value
    });
  },

  // 加载默认地址
  loadDefaultAddress: function() {
    // 初始化示例数据（仅用于开发测试）
    addressService.initSampleData();
    
    // 获取默认地址
    const defaultAddress = addressService.getDefaultAddress();
    
    if (defaultAddress) {
      this.setData({
        address: defaultAddress
      });
    }
  },

  // 选择收货地址
  onAddressSelect: function() {
    wx.navigateTo({
      url: '/pages/address/address?select=true'
    });
  },

  // 地址选择回调（从地址页面返回时调用）
  onAddressSelected: function(selectedAddress) {
    this.setData({
      address: selectedAddress
    });
    
    wx.showToast({
      title: '地址已选择',
      icon: 'success'
    });
  },

  onSubmitOrder: function() {
    // 检查是否有商品
    if (this.data.cartItems.length === 0) {
      wx.showToast({
        title: '没有可结算的商品',
        icon: 'none'
      });
      return;
    }
    
    // 检查是否选择了收货地址
    if (!this.data.address) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      });
      return;
    }
    
    // 模拟提交订单
    wx.showLoading({
      title: '提交订单中...'
    });
    
    // 模拟网络请求
    setTimeout(() => {
      wx.hideLoading();

      try {
        // 使用订单数据管理器创建订单
        const order = OrderDataManager.createOrderFromGoods(
          this.data.cartItems,
          1,
          this.data.address,
          {
            type: 'checkout',
            paymentMethod: 'wechat',
            remark: this.data.remark || ''
          }
        );

        // 清除购物车中已购买的商品
        const cart = wx.getStorageSync('cart') || [];
        const purchasedItemIds = this.data.cartItems.map(item => item.id);
        const remainingCart = cart.filter(item => !purchasedItemIds.includes(item.id));
        wx.setStorageSync('cart', remainingCart);

        wx.showToast({
          title: '订单提交成功',
          icon: 'success'
        });

        // 跳转到订单详情页面
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/order-detail/order-detail?id=${order.id}`
          });
        }, 1500);

      } catch (error) {
        console.error('订单创建失败:', error);
        wx.showToast({
          title: '订单提交失败，请重试',
          icon: 'none'
        });
      }
    }, 1500);
  }
});