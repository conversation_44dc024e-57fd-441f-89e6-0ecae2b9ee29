/**
 * 权限性能测试页面
 * Permission Performance Test Page
 * 
 * 用于测试和展示权限缓存优化效果
 */

const { can, canAny, finance, health, production, getCacheStats, clearCache } = require('../../../utils/permission-helper-optimized');
const performanceMonitor = require('../../../utils/permission-performance-monitor');
const { PERMISSIONS } = require('../../../utils/role-permission');

Page({
  data: {
    // 测试状态
    isTestRunning: false,
    testResults: null,
    
    // 缓存统计
    cacheStats: null,
    
    // 性能监控
    performanceReport: null,
    
    // 测试配置
    testConfig: {
      iterations: 100,
      concurrency: 10,
      includeResourceChecks: true
    },
    
    // 权限测试结果
    permissionResults: {
      finance: null,
      health: null,
      production: null
    }
  },

  onLoad() {
    this.initializeTest();
  },

  /**
   * 初始化测试
   */
  async initializeTest() {
    try {
      // 获取初始缓存统计
      const stats = getCacheStats();
      this.setData({ cacheStats: stats });
      
      // 设置性能阈值
      performanceMonitor.setPerformanceThresholds({
        maxAvgResponseTime: 50, // 50ms
        minCacheHitRate: 70,    // 70%
        maxErrorRate: 2         // 2%
      });
      
      console.log('权限性能测试页面初始化完成');
    } catch (error) {
      console.error('初始化失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'error'
      });
    }
  },

  /**
   * 开始性能测试
   */
  async startPerformanceTest() {
    if (this.data.isTestRunning) return;
    
    this.setData({ isTestRunning: true });
    
    wx.showLoading({
      title: '性能测试中...',
      mask: true
    });
    
    try {
      // 重置监控数据
      performanceMonitor.reset();
      performanceMonitor.startMonitoring();
      
      // 清除缓存以获得准确的测试结果
      clearCache();
      
      const testResults = await this.runPerformanceTest();
      
      // 停止监控
      performanceMonitor.stopMonitoring();
      
      // 获取性能报告
      const performanceReport = performanceMonitor.getPerformanceReport();
      const cacheStats = getCacheStats();
      
      this.setData({
        testResults,
        performanceReport,
        cacheStats,
        isTestRunning: false
      });
      
      // 打印详细报告
      performanceMonitor.printPerformanceReport();
      
      wx.hideLoading();
      wx.showToast({
        title: '测试完成',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('性能测试失败:', error);
      this.setData({ isTestRunning: false });
      wx.hideLoading();
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      });
    }
  },

  /**
   * 执行性能测试
   */
  async runPerformanceTest() {
    const { iterations, concurrency } = this.data.testConfig;
    const results = {
      totalTests: 0,
      successfulTests: 0,
      failedTests: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      testDetails: []
    };

    // 测试用例
    const testCases = [
      // 单个权限检查
      () => can(PERMISSIONS.FINANCE_VIEW_ALL),
      () => can(PERMISSIONS.HEALTH_CREATE),
      () => can(PERMISSIONS.PRODUCTION_VIEW_OWN),
      
      // 多个权限检查
      () => canAny([PERMISSIONS.FINANCE_VIEW_ALL, PERMISSIONS.FINANCE_VIEW_OWN]),
      
      // 模块权限检查
      () => finance(),
      () => health(),
      () => production(),
      
      // 资源级权限检查
      () => can(PERMISSIONS.FINANCE_EDIT, { 
        resourceId: 'test-resource-1', 
        resourceOwnerId: wx.getStorageSync('userId') 
      })
    ];

    // 并发测试
    const promises = [];
    for (let i = 0; i < concurrency; i++) {
      promises.push(this.runTestBatch(testCases, iterations / concurrency));
    }

    const batchResults = await Promise.all(promises);
    
    // 合并结果
    batchResults.forEach(batchResult => {
      results.totalTests += batchResult.totalTests;
      results.successfulTests += batchResult.successfulTests;
      results.failedTests += batchResult.failedTests;
      results.testDetails.push(...batchResult.testDetails);
    });

    return results;
  },

  /**
   * 运行测试批次
   */
  async runTestBatch(testCases, iterations) {
    const results = {
      totalTests: 0,
      successfulTests: 0,
      failedTests: 0,
      testDetails: []
    };

    for (let i = 0; i < iterations; i++) {
      for (const testCase of testCases) {
        const startTime = Date.now();
        let success = false;
        let error = null;

        try {
          await testCase();
          success = true;
          results.successfulTests++;
        } catch (err) {
          error = err.message;
          results.failedTests++;
        }

        const endTime = Date.now();
        const responseTime = endTime - startTime;

        results.totalTests++;
        results.testDetails.push({
          testCase: testCase.name || 'anonymous',
          responseTime,
          success,
          error,
          timestamp: new Date().toISOString()
        });
      }
    }

    return results;
  },

  /**
   * 测试模块权限
   */
  async testModulePermissions() {
    wx.showLoading({
      title: '测试模块权限...',
      mask: true
    });

    try {
      const startTime = Date.now();
      
      // 并行测试所有模块权限
      const [financePerms, healthPerms, productionPerms] = await Promise.all([
        finance(),
        health(),
        production()
      ]);
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;

      this.setData({
        'permissionResults.finance': financePerms,
        'permissionResults.health': healthPerms,
        'permissionResults.production': productionPerms
      });

      wx.hideLoading();
      wx.showToast({
        title: `测试完成 (${totalTime}ms)`,
        icon: 'success'
      });

    } catch (error) {
      console.error('模块权限测试失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      });
    }
  },

  /**
   * 清除缓存并刷新统计
   */
  async clearCacheAndRefresh() {
    try {
      clearCache();
      
      // 等待一下让缓存清除生效
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const stats = getCacheStats();
      this.setData({ cacheStats: stats });
      
      wx.showToast({
        title: '缓存已清除',
        icon: 'success'
      });
    } catch (error) {
      console.error('清除缓存失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    }
  },

  /**
   * 刷新缓存统计
   */
  refreshCacheStats() {
    const stats = getCacheStats();
    this.setData({ cacheStats: stats });
  },

  /**
   * 导出测试结果
   */
  exportTestResults() {
    const exportData = {
      testResults: this.data.testResults,
      performanceReport: this.data.performanceReport,
      cacheStats: this.data.cacheStats,
      permissionResults: this.data.permissionResults,
      exportTime: new Date().toISOString()
    };

    // 在实际应用中，这里可以保存到文件或发送到服务器
    console.log('测试结果导出:', exportData);
    
    wx.showToast({
      title: '结果已导出到控制台',
      icon: 'success'
    });
  },

  /**
   * 更新测试配置
   */
  onTestConfigChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`testConfig.${field}`]: field === 'includeResourceChecks' ? value : parseInt(value)
    });
  },

  /**
   * 显示性能报告详情
   */
  showPerformanceDetails() {
    if (!this.data.performanceReport) {
      wx.showToast({
        title: '请先运行测试',
        icon: 'none'
      });
      return;
    }

    const report = this.data.performanceReport;
    const content = `
缓存命中率: ${report.cacheHitRate}
平均响应时间: ${report.avgResponseTime}
性能提升: ${report.performanceImprovement}
总检查次数: ${report.totalChecks}
错误率: ${report.errorRate}
    `.trim();

    wx.showModal({
      title: '性能报告详情',
      content,
      showCancel: false
    });
  }
});
