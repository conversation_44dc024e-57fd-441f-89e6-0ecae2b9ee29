<!--权限性能测试页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">权限缓存性能测试</text>
    <text class="subtitle">验证权限检查优化效果</text>
  </view>

  <!-- 缓存统计卡片 -->
  <view class="card">
    <view class="card-header">
      <text class="card-title">缓存统计</text>
      <button class="refresh-btn" size="mini" bindtap="refreshCacheStats">刷新</button>
    </view>
    <view class="stats-grid" wx:if="{{cacheStats}}">
      <view class="stat-item">
        <text class="stat-label">总请求数</text>
        <text class="stat-value">{{cacheStats.totalRequests}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">内存命中</text>
        <text class="stat-value">{{cacheStats.memoryHits}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">本地命中</text>
        <text class="stat-value">{{cacheStats.localHits}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">缓存未命中</text>
        <text class="stat-value">{{cacheStats.misses}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">命中率</text>
        <text class="stat-value highlight">{{cacheStats.hitRate}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">内存缓存大小</text>
        <text class="stat-value">{{cacheStats.memoryCacheSize}}</text>
      </view>
    </view>
  </view>

  <!-- 测试配置 -->
  <view class="card">
    <view class="card-header">
      <text class="card-title">测试配置</text>
    </view>
    <view class="config-section">
      <view class="config-item">
        <text class="config-label">测试次数</text>
        <input class="config-input" type="number" value="{{testConfig.iterations}}" 
               data-field="iterations" bindinput="onTestConfigChange" />
      </view>
      <view class="config-item">
        <text class="config-label">并发数</text>
        <input class="config-input" type="number" value="{{testConfig.concurrency}}" 
               data-field="concurrency" bindinput="onTestConfigChange" />
      </view>
      <view class="config-item">
        <text class="config-label">包含资源检查</text>
        <switch checked="{{testConfig.includeResourceChecks}}" 
                data-field="includeResourceChecks" bindchange="onTestConfigChange" />
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="action-btn primary" 
            disabled="{{isTestRunning}}" 
            bindtap="startPerformanceTest">
      {{isTestRunning ? '测试中...' : '开始性能测试'}}
    </button>
    
    <button class="action-btn secondary" bindtap="testModulePermissions">
      测试模块权限
    </button>
    
    <button class="action-btn warning" bindtap="clearCacheAndRefresh">
      清除缓存
    </button>
  </view>

  <!-- 性能测试结果 -->
  <view class="card" wx:if="{{testResults}}">
    <view class="card-header">
      <text class="card-title">性能测试结果</text>
      <button class="detail-btn" size="mini" bindtap="showPerformanceDetails">详情</button>
    </view>
    <view class="result-grid">
      <view class="result-item">
        <text class="result-label">总测试数</text>
        <text class="result-value">{{testResults.totalTests}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">成功数</text>
        <text class="result-value success">{{testResults.successfulTests}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">失败数</text>
        <text class="result-value error">{{testResults.failedTests}}</text>
      </view>
    </view>
  </view>

  <!-- 性能报告 -->
  <view class="card" wx:if="{{performanceReport}}">
    <view class="card-header">
      <text class="card-title">性能报告</text>
      <button class="export-btn" size="mini" bindtap="exportTestResults">导出</button>
    </view>
    <view class="performance-grid">
      <view class="perf-item">
        <text class="perf-label">缓存命中率</text>
        <text class="perf-value {{performanceReport.cacheHitRate >= '80%' ? 'good' : 'warning'}}">
          {{performanceReport.cacheHitRate}}
        </text>
      </view>
      <view class="perf-item">
        <text class="perf-label">平均响应时间</text>
        <text class="perf-value">{{performanceReport.avgResponseTime}}</text>
      </view>
      <view class="perf-item">
        <text class="perf-label">缓存命中时间</text>
        <text class="perf-value good">{{performanceReport.avgCacheHitTime}}</text>
      </view>
      <view class="perf-item">
        <text class="perf-label">缓存未命中时间</text>
        <text class="perf-value warning">{{performanceReport.avgCacheMissTime}}</text>
      </view>
      <view class="perf-item">
        <text class="perf-label">性能提升</text>
        <text class="perf-value highlight">{{performanceReport.performanceImprovement}}</text>
      </view>
      <view class="perf-item">
        <text class="perf-label">错误率</text>
        <text class="perf-value {{performanceReport.errorRate <= '1%' ? 'good' : 'error'}}">
          {{performanceReport.errorRate}}
        </text>
      </view>
    </view>
  </view>

  <!-- 模块权限测试结果 -->
  <view class="card" wx:if="{{permissionResults.finance}}">
    <view class="card-header">
      <text class="card-title">模块权限测试</text>
    </view>
    
    <!-- 财务权限 -->
    <view class="module-section">
      <text class="module-title">财务模块</text>
      <view class="permission-grid">
        <view class="perm-item {{permissionResults.finance.canViewAll ? 'granted' : 'denied'}}">
          <text class="perm-name">查看所有</text>
          <text class="perm-status">{{permissionResults.finance.canViewAll ? '✓' : '✗'}}</text>
        </view>
        <view class="perm-item {{permissionResults.finance.canCreate ? 'granted' : 'denied'}}">
          <text class="perm-name">创建申请</text>
          <text class="perm-status">{{permissionResults.finance.canCreate ? '✓' : '✗'}}</text>
        </view>
        <view class="perm-item {{permissionResults.finance.canApprove ? 'granted' : 'denied'}}">
          <text class="perm-name">审批申请</text>
          <text class="perm-status">{{permissionResults.finance.canApprove ? '✓' : '✗'}}</text>
        </view>
        <view class="perm-item {{permissionResults.finance.canViewReports ? 'granted' : 'denied'}}">
          <text class="perm-name">查看报表</text>
          <text class="perm-status">{{permissionResults.finance.canViewReports ? '✓' : '✗'}}</text>
        </view>
      </view>
    </view>

    <!-- 健康权限 -->
    <view class="module-section" wx:if="{{permissionResults.health}}">
      <text class="module-title">健康模块</text>
      <view class="permission-grid">
        <view class="perm-item {{permissionResults.health.canViewAll ? 'granted' : 'denied'}}">
          <text class="perm-name">查看所有</text>
          <text class="perm-status">{{permissionResults.health.canViewAll ? '✓' : '✗'}}</text>
        </view>
        <view class="perm-item {{permissionResults.health.canCreate ? 'granted' : 'denied'}}">
          <text class="perm-name">创建记录</text>
          <text class="perm-status">{{permissionResults.health.canCreate ? '✓' : '✗'}}</text>
        </view>
        <view class="perm-item {{permissionResults.health.canDiagnose ? 'granted' : 'denied'}}">
          <text class="perm-name">AI诊断</text>
          <text class="perm-status">{{permissionResults.health.canDiagnose ? '✓' : '✗'}}</text>
        </view>
      </view>
    </view>

    <!-- 生产权限 -->
    <view class="module-section" wx:if="{{permissionResults.production}}">
      <text class="module-title">生产模块</text>
      <view class="permission-grid">
        <view class="perm-item {{permissionResults.production.canViewAll ? 'granted' : 'denied'}}">
          <text class="perm-name">查看所有</text>
          <text class="perm-status">{{permissionResults.production.canViewAll ? '✓' : '✗'}}</text>
        </view>
        <view class="perm-item {{permissionResults.production.canCreate ? 'granted' : 'denied'}}">
          <text class="perm-name">创建记录</text>
          <text class="perm-status">{{permissionResults.production.canCreate ? '✓' : '✗'}}</text>
        </view>
        <view class="perm-item {{permissionResults.production.canViewReports ? 'granted' : 'denied'}}">
          <text class="perm-name">查看报表</text>
          <text class="perm-status">{{permissionResults.production.canViewReports ? '✓' : '✗'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 优化建议 -->
  <view class="card tips-card">
    <view class="card-header">
      <text class="card-title">优化效果说明</text>
    </view>
    <view class="tips-content">
      <view class="tip-item">
        <text class="tip-icon">🚀</text>
        <text class="tip-text">缓存命中率 > 80% 表示优化效果显著</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">⚡</text>
        <text class="tip-text">缓存命中响应时间通常 < 10ms</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">📈</text>
        <text class="tip-text">性能提升 > 60% 达到预期目标</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">🎯</text>
        <text class="tip-text">错误率应保持在 1% 以下</text>
      </view>
    </view>
  </view>
</view>
