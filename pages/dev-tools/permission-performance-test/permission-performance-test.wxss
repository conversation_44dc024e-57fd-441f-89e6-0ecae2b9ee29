/* 权限性能测试页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 按钮样式 */
.refresh-btn, .detail-btn, .export-btn {
  background-color: #0066CC;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.stat-value.highlight {
  color: #0066CC;
  font-size: 36rpx;
}

/* 配置区域 */
.config-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.config-label {
  font-size: 28rpx;
  color: #333;
}

.config-input {
  width: 200rpx;
  height: 60rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 15rpx;
  font-size: 28rpx;
  text-align: center;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.action-btn {
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.action-btn.primary {
  background-color: #0066CC;
  color: white;
}

.action-btn.secondary {
  background-color: #28A745;
  color: white;
}

.action-btn.warning {
  background-color: #FFC107;
  color: #333;
}

.action-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

/* 结果网格 */
.result-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
}

.result-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.result-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.result-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.result-value.success {
  color: #28A745;
}

.result-value.error {
  color: #DC3545;
}

/* 性能报告网格 */
.performance-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.perf-item {
  display: flex;
  flex-direction: column;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.perf-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.perf-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.perf-value.good {
  color: #28A745;
}

.perf-value.warning {
  color: #FFC107;
}

.perf-value.error {
  color: #DC3545;
}

.perf-value.highlight {
  color: #0066CC;
  font-size: 32rpx;
}

/* 模块权限区域 */
.module-section {
  margin-bottom: 25rpx;
}

.module-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #e0e0e0;
}

.permission-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10rpx;
}

.perm-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 15rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e0e0e0;
}

.perm-item.granted {
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.perm-item.denied {
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.perm-name {
  font-size: 26rpx;
  color: #333;
}

.perm-status {
  font-size: 28rpx;
  font-weight: bold;
}

.perm-item.granted .perm-status {
  color: #28A745;
}

.perm-item.denied .perm-status {
  color: #DC3545;
}

/* 提示卡片 */
.tips-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tips-card .card-title {
  color: white;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.tip-icon {
  font-size: 32rpx;
}

.tip-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .stats-grid,
  .performance-grid {
    grid-template-columns: 1fr;
  }
  
  .permission-grid {
    grid-template-columns: 1fr;
  }
  
  .result-grid {
    grid-template-columns: 1fr;
  }
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 动画效果 */
.card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 高亮效果 */
.highlight-animation {
  animation: highlight 1s ease-in-out;
}

@keyframes highlight {
  0%, 100% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(0, 102, 204, 0.1);
  }
}
