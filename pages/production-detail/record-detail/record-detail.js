// pages/production-detail/record-detail/record-detail.js
const { apiClient, get, post, put } = require('../../../utils/api-client-final.js');
const { BatchManagement } = require('../../../utils/business/batch-management.js');
const { FinanceService } = require('../../../utils/business/finance-service.js');

Page({
  data: {
    id: null,
    record: {},
    isEditing: false,
    loading: true,
    
    // 批次管理相关
    batchOptions: [],
    activeBatches: [],
    selectedBatchInfo: null,

    // 死亡原因选项
    deathCauseOptions: [
      { label: '疾病死亡', value: 'disease' },
      { label: '意外死亡', value: 'accident' },
      { label: '老龄死亡', value: 'aging' },
      { label: '营养不良', value: 'malnutrition' },
      { label: '环境应激', value: 'stress' },
      { label: '其他原因', value: 'other' }
    ],
    
    // 传染性选项
    contagiousOptions: [
      { label: '高度传染', value: 'high' },
      { label: '中度传染', value: 'medium' },
      { label: '低度传染', value: 'low' },
      { label: '不传染', value: 'none' }
    ],
    
    // 隔离措施选项
    isolationOptions: [
      { label: '完全隔离', value: 'complete' },
      { label: '部分隔离', value: 'partial' },
      { label: '观察隔离', value: 'observation' },
      { label: '无需隔离', value: 'none' }
    ],
    // 疾病类型选项
    diseaseTypeOptions: [
      { label: '病毒感染', value: 'virus' },
      { label: '细菌感染', value: 'bacteria' },
      { label: '寄生虫病', value: 'parasite' },
      { label: '营养缺乏', value: 'nutrition' },
      { label: '消化系统疾病', value: 'digestive' },
      { label: '呼吸系统疾病', value: 'respiratory' },
      { label: '其他疾病', value: 'other' }
    ],
    // 严重程度选项
    severityOptions: [
      { label: '轻度', value: 'mild' },
      { label: '中度', value: 'moderate' },
      { label: '重度', value: 'severe' },
      { label: '危重', value: 'critical' }
    ],
    // 治疗方案选项
    treatmentOptions: [
      { label: '药物治疗', value: 'medication' },
      { label: '手术治疗', value: 'surgery' },
      { label: '隔离治疗', value: 'isolation' },
      { label: '营养调理', value: 'nutrition' },
      { label: '环境改善', value: 'environment' },
      { label: '疫苗接种', value: 'vaccination' },
      { label: '综合治疗', value: 'comprehensive' }
    ],
    // 治疗效果选项
    treatmentEffectOptions: [
      { label: '完全治愈', value: 'cured' },
      { label: '明显好转', value: 'improved' },
      { label: '略有好转', value: 'slightly_improved' },
      { label: '无变化', value: 'no_change' },
      { label: '病情加重', value: 'worsened' }
    ],
    // 疫苗类型选项
    vaccineTypeOptions: [
      { label: '小鹅瘟疫苗', value: 'gosling_plague' },
      { label: '禽流感疫苗', value: 'bird_flu' },
      { label: '新城疫疫苗', value: 'newcastle' },
      { label: '禽霍乱疫苗', value: 'fowl_cholera' },
      { label: '大肠杆菌疫苗', value: 'ecoli' },
      { label: '传染性法氏囊病疫苗', value: 'ibdv' },
      { label: '鹅副粘病毒疫苗', value: 'gpv' },
      { label: '其他疫苗', value: 'other' }
    ],
    // 接种方式选项
    vaccinationMethodOptions: [
      { label: '肌肉注射', value: 'intramuscular' },
      { label: '皮下注射', value: 'subcutaneous' },
      { label: '滴鼻滴眼', value: 'drop' },
      { label: '饮水免疫', value: 'drinking_water' },
      { label: '喷雾免疫', value: 'spray' },
      { label: '涂肛免疫', value: 'cloacal' },
      { label: '其他方式', value: 'other' }
    ],
    // 治疗状态选项
    treatmentStatusOptions: [
      { label: '未治疗', value: 'untreated' },
      { label: '治疗中', value: 'treating' },
      { label: '已治愈', value: 'cured' },
      { label: '好转中', value: 'improving' },
      { label: '病情稳定', value: 'stable' },
      { label: '病情恶化', value: 'worsening' }
    ],
    // 鹅群来源选项
    sourceOptions: [
      { label: '自繁自养', value: 'self_breeding' },
      { label: '外购雏鹅', value: 'purchased_gosling' },
      { label: '合作养殖', value: 'cooperative' },
      { label: '种鹅场', value: 'breeding_farm' },
      { label: '其他来源', value: 'other' }
    ],
    // 品种类型选项
    breedOptions: [
      { label: '太湖鹅', value: 'taihu' },
      { label: '皖西白鹅', value: 'wanxi_white' },
      { label: '四川白鹅', value: 'sichuan_white' },
      { label: '豁眼鹅', value: 'huoyan' },
      { label: '狮头鹅', value: 'shitou' },
      { label: '朗德鹅', value: 'lande' },
      { label: '溆浦鹅', value: 'xupu' },
      { label: '其他品种', value: 'other' }
    ],
    // 健康状况选项
    healthStatusOptions: [
      { label: '健康', value: 'healthy' },
      { label: '亚健康', value: 'sub_healthy' },
      { label: '轻微异常', value: 'slight_abnormal' },
      { label: '需要观察', value: 'need_observation' }
    ]
  },

  onLoad: function (options) {
    // 加载活跃批次数据
    this.loadActiveBatches();
    
    // 获取记录ID
    if (options.id) {
      this.setData({
        id: options.id
      });
      this.loadRecordDetail(options.id);
    } else {
      // 新建记录
      this.setData({
        isEditing: true,
        loading: false,
        record: {
          date: this.getCurrentDate(),
          status: 'vaccination',
          description: '',
          images: [],
          author: '当前用户',
          
          // 基础字段
          batchNumber: '',
          recordType: 'vaccine',
          affectedCount: '',
          
          // 防疫记录相关字段
          vaccineTypeIndex: undefined,
          vaccineBatch: '',
          vaccinationMethodIndex: undefined,
          vaccineDosage: '',
          vaccinatedCount: '',
          nextVaccinationDate: '',
          vaccinationReaction: '',
          
          // 疾病相关字段
          diseaseTypeIndex: undefined,
          diseaseSymptoms: '',
          severityIndex: undefined,
          onsetDate: '',
          diagnosisDate: '',
          sickCount: '',
          contagiousIndex: undefined,
          isolationIndex: undefined,
          
          // 治疗相关字段
          treatmentIndex: undefined,
          medicationName: '',
          treatmentDosage: '',
          treatmentDuration: '',
          treatmentCount: '',
          treatmentEffectIndex: undefined,
          treatmentCost: '',
          followUpDate: '',
          curedCount: '',
          treatmentStatusIndex: undefined,
          
          // 死亡相关字段
          deathCauseIndex: undefined,
          deathSymptoms: '',
          deathTime: '',
          deathCount: '',
          
          // 其他字段
          notes: '',
          location: '',
          weather: '',
          temperature: '',
          humidity: ''
        }
      });
    }
  },

  onShow: function () {
    // 页面显示
  },

  // 获取当前日期
  getCurrentDate: function () {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 加载记录详情
  loadRecordDetail: function (id) {
    this.setData({
      loading: true
    });

    apiClient.get('/api/v2/health/records/' + id).then(res => {
      if (res && res.success && res.data) {
        this.setData({
          record: res.data,
          loading: false
        });
      } else {
        // API失败时使用模拟数据
        this.loadMockRecordDetail(id);
      }
    }).catch(err => {
      console.log('API请求失败，使用模拟数据:', err);
      // 网络请求失败时使用模拟数据
      this.loadMockRecordDetail(id);
    });
  },

  // 加载模拟记录详情
  loadMockRecordDetail: function (id) {
    const mockRecords = {
      1: {
        id: 1,
        userId: 1,
        batchNumber: '批次001',
        status: 'vaccination',
        date: '2023-06-15',
        author: '张兽医',
        description: '完成小鹅瘟疫苗接种，接种数量100只',
        symptoms: '',
        diagnosis: '疫苗接种完成',
        treatment: '预防性接种',
        medication: '小鹅瘟疫苗',
        dosage: '1ml/只',
        affectedCount: 100,
        images: [],
        notes: '接种数量100只，无不良反应',
        createdAt: '2023-06-15T00:00:00.000Z',
        updatedAt: '2023-06-15T00:00:00.000Z'
      },
      2: {
        id: 2,
        userId: 1,
        batchNumber: '批次001',
        status: 'sick',
        date: '2023-06-20',
        author: '李兽医',
        description: '发现鹅群呼吸道感染症状，患病5只',
        symptoms: '发现鹅群呼吸道感染症状，患病5只',
        diagnosis: '呼吸道感染',
        treatment: '隔离治疗，给予抗生素',
        medication: '阿莫西林',
        dosage: '0.5g/只',
        affectedCount: 5,
        images: [],
        notes: '患病5只，已隔离观察',
        createdAt: '2023-06-20T00:00:00.000Z',
        updatedAt: '2023-06-20T00:00:00.000Z'
      },
      3: {
        id: 3,
        userId: 1,
        batchNumber: '批次001',
        status: 'treatment',
        date: '2023-06-21',
        author: '李兽医',
        description: '针对呼吸道感染进行药物治疗',
        symptoms: '针对呼吸道感染进行药物治疗',
        diagnosis: '呼吸道感染治疗中',
        treatment: '继续抗生素治疗',
        medication: '阿莫西林',
        dosage: '0.5g/只',
        affectedCount: 5,
        images: [],
        notes: '治疗效果良好，症状减轻',
        createdAt: '2023-06-21T00:00:00.000Z',
        updatedAt: '2023-06-21T00:00:00.000Z'
      },
      4: {
        id: 4,
        userId: 1,
        batchNumber: '批次001',
        status: 'death',
        date: '2023-06-25',
        author: '王兽医',
        description: '发现1只成年鹅死亡，疑似疾病导致',
        symptoms: '发现1只成年鹅死亡，疑似疾病导致',
        diagnosis: '死亡原因待查',
        treatment: '尸体处理，环境消毒',
        medication: '',
        dosage: '',
        affectedCount: 1,
        images: [],
        notes: '疑似疾病导致死亡，需要加强防疫',
        createdAt: '2023-06-25T00:00:00.000Z',
        updatedAt: '2023-06-25T00:00:00.000Z'
      }
    };

    const record = mockRecords[id];
    if (record) {
      this.setData({
        record: record,
        loading: false
      });
    } else {
      this.setData({
        loading: false
      });
      wx.showToast({
        title: '记录不存在',
        icon: 'none'
      });
    }
  },

  // 编辑记录
  onEdit: function () {
    this.setData({
      isEditing: true
    });
  },

  // 取消编辑
  onCancelEdit: function () {
    if (!this.data.id) {
      // 新建记录时取消编辑，返回上一页
      wx.navigateBack();
      return;
    }

    this.setData({
      isEditing: false
    });

    // 重新加载数据以恢复原始状态
    this.loadRecordDetail(this.data.id);
  },

  // 加载活跃批次（用于批次选择）
  loadActiveBatches: async function() {
    try {
      const activeBatches = await BatchManagement.getActiveBatches();
      
      const batchOptions = activeBatches.map(batch => ({
        value: batch.batchNumber,
        label: `${batch.batchNumber} (${batch.breed}, ${batch.currentCount}只)`,
        ...batch
      }));
      
      this.setData({
        batchOptions: batchOptions,
        activeBatches: activeBatches
      });
      
    } catch (error) {
      console.error('加载活跃批次失败:', error);
    }
  },
  
  // 批次选择处理
  onBatchChange: function(e) {
    const index = e.detail.value;
    const selectedBatch = this.data.batchOptions[index];
    
    if (selectedBatch) {
      this.setData({
        'record.batchNumber': selectedBatch.value,
        selectedBatchInfo: selectedBatch,
        'record.affectedCount': selectedBatch.currentCount // 默认影响数量为批次当前数量
      });
    }
  },

  // 保存记录
  onSave: async function () {
    const { id, record } = this.data;

    // 验证必填字段
    if (!record.date) {
      wx.showToast({
        title: '请选择日期',
        icon: 'none'
      });
      return;
    }

    if (!record.batchNumber) {
      wx.showToast({
        title: '请选择批次',
        icon: 'none'
      });
      return;
    }

    if (!record.description) {
      wx.showToast({
        title: '请输入描述',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({ title: '保存中...' });
      
      // 1. 保存健康记录
      let savedRecord;
      if (id) {
        // 更新记录
        const res = await apiClient.put('/api/v2/health/records/' + id, record);
        if (res && res.success) {
          savedRecord = res.data;
        } else {
          throw new Error(res.message || '保存失败');
        }
      } else {
        // 创建记录
        const res = await apiClient.post('/api/v2/health/records', record);
        if (res && res.success) {
          savedRecord = res.data;
        } else {
          throw new Error(res.message || '创建失败');
        }
      }
      
      // 2. 集成批次管理和财务服务
      const integrationResult = await this.integrateHealthRecord(savedRecord);
      
      wx.hideLoading();
      
      // 3. 显示保存结果
      let successMessage = id ? '健康记录更新成功' : '健康记录创建成功';
      if (integrationResult.batchResult && integrationResult.batchResult.success) {
        successMessage += '\n' + integrationResult.batchResult.message;
      }
      if (integrationResult.financeResult && integrationResult.financeResult.success) {
        successMessage += '\n' + integrationResult.financeResult.message;
      }
      
      wx.showModal({
        title: '操作成功',
        content: successMessage,
        showCancel: false,
        confirmText: '确定'
      });

      // 4. 更新页面数据
      this.setData({
        id: savedRecord.id,
        record: savedRecord,
        isEditing: false
      });

      // 5. 保存数据到全局用于关联
      this.saveHealthRecordToGlobal(savedRecord);

      // 6. 通知健康管理页面刷新数据
      const pages = getCurrentPages();
      const productionPage = pages.find(page => page.route === 'pages/production/production');
      if (productionPage) {
        productionPage.loadHealthRecords();
        productionPage.loadReportData();
      }
      
    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '保存失败',
        content: error.message || '保存健康记录时发生错误',
        showCancel: false
      });
    }
  },
  
  // 集成健康记录到批次管理和财务服务
  integrateHealthRecord: async function(healthRecord) {
    try {
      let batchResult = { success: true, message: '批次管理操作完成' };
      let financeResult = { success: true, message: '无需创建财务记录' };
      
      // 1. 添加健康记录到批次管理
      batchResult = await BatchManagement.addBatchHealthRecord(healthRecord.batchNumber, {
        date: healthRecord.date,
        recordType: healthRecord.recordType || 'other',
        description: healthRecord.description,
        medicine: healthRecord.medicine || '',
        dosage: healthRecord.dosage || '',
        cost: parseFloat(healthRecord.cost) || 0,
        affectedCount: parseInt(healthRecord.affectedCount) || 0,
        notes: healthRecord.notes || ''
      });
      
      // 2. 如果产生费用，创建财务记录
      const cost = parseFloat(healthRecord.cost) || 0;
      if (cost > 0) {
        financeResult = await FinanceService.createHealthExpense({
          id: healthRecord.id,
          batch: healthRecord.batchNumber,
          date: healthRecord.date,
          recordType: healthRecord.recordType || 'other',
          description: healthRecord.description,
          medicine: healthRecord.medicine || '',
          dosage: healthRecord.dosage || '',
          cost: cost,
          supplier: healthRecord.supplier || '',
          notes: healthRecord.notes || ''
        });
      }
      
      return {
        success: batchResult.success && financeResult.success,
        batchResult: batchResult,
        financeResult: financeResult
      };
      
    } catch (error) {
      console.error('集成健康记录失败:', error);
      return {
        success: false,
        error: error,
        message: '集成健康记录失败，但记录已保存'
      };
    }
  },

  // 删除记录
  onDelete: function () {
    const { id } = this.data;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条健康记录吗？',
      success: (res) => {
        if (res.confirm) {
          apiClient.delete('/api/v2/health/records/' + id).then(res => {
            if (res && res.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });

              // 返回上一页
              setTimeout(() => {
                wx.navigateBack();
              }, 1000);
            } else {
              wx.showToast({
                title: res.message || '删除失败',
                icon: 'none'
              });
            }
          }).catch(err => {
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 选择日期
  onDateChange: function (e) {
    this.setData({
      'record.date': e.detail.value
    });
  },

  // 选择状态
  onStatusChange: function (e) {
    const status = e.currentTarget.dataset.status;
    
    // 切换状态时，清空状态特定字段
    const updatedRecord = {
      ...this.data.record,
      status: status,
      // 清空状态特定字段
      // 入栏记录相关字段
      batchNumber: '',
      sourceIndex: undefined,
      breedIndex: undefined,
      initialCount: '',
      dayAge: '',
      shedNumber: '',
      averageWeight: '',
      healthStatusIndex: undefined,
      // 死亡相关字段
      deathCauseIndex: undefined,
      deathSymptoms: '',
      deathTime: '',
      affectedCount: '',
      // 疾病相关字段
      relatedBatchNumber: '',
      diseaseTypeIndex: undefined,
      diseaseSymptoms: '',
      severityIndex: undefined,
      onsetDate: '',
      diagnosisDate: '',
      sickCount: '',
      contagiousIndex: undefined,
      isolationIndex: undefined,
      // 治疗相关字段
      relatedSickRecordId: '',
      treatmentIndex: undefined,
      medicationName: '',
      dosage: '',
      treatmentDuration: '',
      treatmentCount: '',
      treatmentEffectIndex: undefined,
      treatmentCost: '',
      followUpDate: '',
      curedCount: '',
      treatmentStatusIndex: undefined,
      // 防疫记录相关字段
      vaccineTypeIndex: undefined,
      vaccineBatch: '',
      vaccinationMethodIndex: undefined,
      vaccineDosage: '',
      vaccinatedCount: '',
      nextVaccinationDate: '',
      vaccinationReaction: ''
    };
    
    this.setData({
      record: updatedRecord
    });
  },

  // 输入描述
  onDescriptionInput: function (e) {
    this.setData({
      'record.description': e.detail.value
    });
  },

  // 通用输入处理
  onInputChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`record.${field}`]: value
    });
  },

  // 选择器变化处理
  onPickerChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = parseInt(e.detail.value);
    this.setData({
      [`record.${field}`]: value
    });
  },

  // 选择图片
  onChooseImage: function () {
    const that = this;
    wx.chooseImage({
      count: 9,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function (res) {
        const tempFiles = res.tempFiles;
        const images = that.data.record.images || [];

        // 添加新选择的图片
        tempFiles.forEach(file => {
          images.push({
            url: file.path,
            size: file.size
          });
        });

        that.setData({
          'record.images': images
        });
      }
    });
  },

  // 删除图片
  onDeleteImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.record.images || [];
    images.splice(index, 1);

    this.setData({
      'record.images': images
    });
  },

  // 选择时间
  onTimeChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`record.${field}`]: value
    });
  },

  // 选择日期字段
  onDateFieldChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`record.${field}`]: value
    });
  },

  // 保存健康记录数据到全局用于关联
  saveHealthRecordToGlobal: function (record) {
    try {
      // 获取当前全局健康数据
      const app = getApp();
      let globalHealthData = app.globalData.healthRecords || [];
      
      // 将记录转换为健康报告格式的数据
      const healthData = this.convertRecordToHealthData(record);
      
      // 更新或添加记录
      const existingIndex = globalHealthData.findIndex(item => item.id === record.id);
      if (existingIndex >= 0) {
        globalHealthData[existingIndex] = healthData;
      } else {
        globalHealthData.push(healthData);
      }
      
      // 保存到全局数据
      app.globalData.healthRecords = globalHealthData;
      
      // 同时保存到本地存储
      wx.setStorageSync('healthRecords', globalHealthData);
      
    } catch (error) {
      try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('保存健康记录数据失败', error); } catch(_) {}
    }
  },

  // 文本域变化
  onTextareaChange: function (e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`record.${field}`]: e.detail.value
    });
  },

  // 图片选择
  onChooseImage: function () {
    const that = this;
    wx.chooseImage({
      count: 9 - this.data.record.images.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function (res) {
        const tempFiles = res.tempFiles;
        const images = that.data.record.images || [];
        
        tempFiles.forEach(file => {
          images.push({
            url: file.path,
            size: file.size
          });
        });
        
        that.setData({
          'record.images': images
        });
      }
    });
  },

  // 删除图片
  onDeleteImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.record.images;
    images.splice(index, 1);
    this.setData({
      'record.images': images
    });
  },

  // 预览图片
  onPreviewImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.record.images;
    const urls = images.map(img => img.url);
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },

  // 创建记录
  createRecord: function (record) {
    apiClient.post('/api/v2/health/records', record).then(res => {
      if (res && res.success) {
        wx.showToast({
          title: '创建成功',
          icon: 'success'
        });

        // 返回上一页并刷新
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(res.message || '创建失败');
      }
    }).catch(error => {
      console.error('创建记录失败:', error);
      wx.showToast({
        title: '创建失败',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({ loading: false });
    });
  },

  // 更新记录
  updateRecord: function (record) {
    apiClient.put('/api/v2/health/records/' + this.data.id, record).then(res => {
      if (res && res.success) {
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });

        // 返回上一页并刷新
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(res.message || '更新失败');
      }
    }).catch(error => {
      console.error('更新记录失败:', error);
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({ loading: false });
    });
  },

  // 删除记录
  deleteRecord: function () {
    this.setData({ loading: true });

    apiClient.delete('/api/v2/health/records/' + this.data.id).then(res => {
      if (res && res.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });

        // 返回上一页并刷新
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(res.message || '删除失败');
      }
    }).catch(error => {
      console.error('删除记录失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({ loading: false });
    });
  },

  // 取消编辑
  onCancel: function () {
    if (this.data.id) {
      // 编辑现有记录，恢复原始数据
      this.loadRecordDetail(this.data.id);
    } else {
      // 新建记录，返回上一页
      wx.navigateBack();
    }
  },

  // 将健康记录转换为健康报告格式
  convertRecordToHealthData: function (record) {
    const now = new Date();
    const timestamp = now.getTime();
    
    // 根据状态生成默认标题
    let title = '';
    switch (record.status) {
    case 'entry':
      title = '入栏记录';
      break;
    case 'vaccination':
      title = '防疫记录';
      break;
    case 'sick':
      title = '生病记录';
      break;
    case 'treatment':
      title = '治疗记录';
      break;
    case 'death':
      title = '死亡记录';
      break;
    default:
      title = '健康记录';
    }
    
    return {
      id: record.id || `health_${timestamp}`,
      date: record.date,
      timestamp: timestamp,
      status: record.status,
      title: title,
      
      // 状态特定数据
      deathCause: record.deathCauseIndex !== undefined ? this.data.deathCauseOptions[record.deathCauseIndex]?.value : null,
      deathSymptoms: record.deathSymptoms || '',
      deathTime: record.deathTime || '',
      affectedCount: parseInt(record.affectedCount) || 0,
      
      diseaseType: record.diseaseTypeIndex !== undefined ? this.data.diseaseTypeOptions[record.diseaseTypeIndex]?.value : null,
      diseaseSymptoms: record.diseaseSymptoms || '',
      severity: record.severityIndex !== undefined ? this.data.severityOptions[record.severityIndex]?.value : null,
      onsetDate: record.onsetDate || '',
      diagnosisDate: record.diagnosisDate || '',
      
      treatment: record.treatmentIndex !== undefined ? this.data.treatmentOptions[record.treatmentIndex]?.value : null,
      medicationName: record.medicationName || '',
      dosage: record.dosage || '',
      treatmentDuration: record.treatmentDuration || '',
      treatmentEffect: record.treatmentEffectIndex !== undefined ? this.data.treatmentEffectOptions[record.treatmentEffectIndex]?.value : null,
      treatmentCost: parseFloat(record.treatmentCost) || 0,
      followUpDate: record.followUpDate || '',
      treatmentStatus: record.treatmentStatusIndex !== undefined ? this.data.treatmentStatusOptions[record.treatmentStatusIndex]?.value : null,
      
      // 入栏记录相关数据
      batchNumber: record.batchNumber || '',
      source: record.sourceIndex !== undefined ? this.data.sourceOptions[record.sourceIndex]?.value : null,
      breed: record.breedIndex !== undefined ? this.data.breedOptions[record.breedIndex]?.value : null,
      initialCount: parseInt(record.initialCount) || 0,
      dayAge: parseInt(record.dayAge) || 0,
      shedNumber: record.shedNumber || '',
      averageWeight: parseFloat(record.averageWeight) || 0,
      healthStatus: record.healthStatusIndex !== undefined ? this.data.healthStatusOptions[record.healthStatusIndex]?.value : null,
      
      // 疾病记录增强数据
      relatedBatchNumber: record.relatedBatchNumber || '',
      sickCount: parseInt(record.sickCount) || 0,
      contagious: record.contagiousIndex !== undefined ? this.data.contagiousOptions[record.contagiousIndex]?.value : null,
      isolation: record.isolationIndex !== undefined ? this.data.isolationOptions[record.isolationIndex]?.value : null,
      
      // 治疗记录增强数据
      relatedSickRecordId: record.relatedSickRecordId || '',
      treatmentCount: parseInt(record.treatmentCount) || 0,
      curedCount: parseInt(record.curedCount) || 0,
      
      // 防疫记录相关数据
      vaccineType: record.vaccineTypeIndex !== undefined ? this.data.vaccineTypeOptions[record.vaccineTypeIndex]?.value : null,
      vaccineBatch: record.vaccineBatch || '',
      vaccinationMethod: record.vaccinationMethodIndex !== undefined ? this.data.vaccinationMethodOptions[record.vaccinationMethodIndex]?.value : null,
      vaccineDosage: record.vaccineDosage || '',
      vaccinatedCount: parseInt(record.vaccinatedCount) || 0,
      nextVaccinationDate: record.nextVaccinationDate || '',
      vaccinationReaction: record.vaccinationReaction || '',
      
      description: record.description || '',
      images: record.images || []
    };
  }
});