// pages/production/production.js - 增强模块化版本
const { IMAGES, UI, BUSINESS, API } = require('../../constants/index.js');
const { ultimateAPIClient, businessAPI } = require('../../utils/ultimate-api-client.js');
const { configManager } = require('../../utils/unified-config-manager.js');
const { permissionManager } = require('../../utils/enhanced-permission-manager.js');
const { RealtimeSyncMixin, QuickSetup } = require('../../utils/websocket/index.js');

// 导入模块化组件
const EnhancedHealthModule = require('./modules/enhanced-health-module.js');
const EnhancedMaterialModule = require('./modules/enhanced-material-module.js');

Page({
  // 合并 RealtimeSyncMixin 的数据
  data: {
    ...RealtimeSyncMixin.data,
    activeTab: 0,
    tabs: [
      { id: 0, name: '生产记录' },
      { id: 1, name: '健康监测' },
      { id: 2, name: '物料管理' },
      { id: 3, name: '知识库' }
    ],

    // 健康记录数据和二级标签页
    healthRecords: [],
    loading: true,
    activeHealthTab: 0, // 健康页面内的二级标签页
    healthTabs: [
      { id: 0, name: '健康记录' },
      { id: 1, name: '健康报告' },
      { id: 2, name: 'AI诊断' }
    ],

    // AI诊断数据
    symptoms: '',
    uploadedImages: [],
    diagnosisResult: null,
    isDiagnosing: false,

    // 知识库数据
    categories: [],
    articles: [],
    activeCategory: 'all',
    searchKeyword: '',

    // 生产记录详情弹窗
    showRecordDetail: false,
    currentRecord: null,

    // 健康报告数据（合并到健康记录标签页）
    reportTypes: [
      { id: 'week', name: '周报' },
      { id: 'month', name: '月报' },
      { id: 'quarter', name: '季报' },
      { id: 'year', name: '年报' }
    ],
    activeReportType: 'week',
    reportData: {
      overview: {
        totalGeese: 0,
        healthyCount: 0,
        sickCount: 0,
        deathCount: 0,
        healthyRate: '0%'
      },
      diseaseStats: [],
      treatmentStats: [],
      trendData: [],
      updateTime: ''
    },

    // 物料管理相关数据
    materialStats: {
      feedCount: 12,
      medicineCount: 8,
      lowStockItems: 3
    },
    activeMaterialTab: 'all',
    materialTabs: [
      { id: 'all', name: '全部' },
      { id: 'feed', name: '饲料' },
      { id: 'medicine', name: '药品' },
      { id: 'other', name: '其他' }
    ],
    materialList: [
      {
        id: 1,
        name: '雏鹅专用饲料',
        spec: '25kg/袋',
        stock: 120,
        status: 'normal',
        category: 'feed'
      },
      {
        id: 2,
        name: '育肥期饲料',
        spec: '25kg/袋',
        stock: 45,
        status: 'warning',
        category: 'feed'
      },
      {
        id: 3,
        name: '成鹅饲料',
        spec: '25kg/袋',
        stock: 80,
        status: 'normal',
        category: 'feed'
      },
      {
        id: 4,
        name: '抗生素',
        spec: '100ml/瓶',
        stock: 30,
        status: 'danger',
        category: 'medicine'
      },
      {
        id: 5,
        name: '维生素',
        spec: '500g/瓶',
        stock: 25,
        status: 'warning',
        category: 'medicine'
      },
      {
        id: 6,
        name: '消毒液',
        spec: '5L/桶',
        stock: 15,
        status: 'normal',
        category: 'other'
      },
      {
        id: 7,
        name: '饮水器',
        spec: '个',
        stock: 8,
        status: 'danger',
        category: 'other'
      }
    ],
    filteredMaterialList: [],

    // 生产记录相关数据
    activeRecordTab: 'all',
    recordTabs: [
      { id: 'all', name: '全部记录' },
      { id: 'entry', name: '入栏记录' },
      { id: 'weight', name: '称重记录' },
      { id: 'sale', name: '出栏记录' }
    ],
    productionRecords: [],
    filteredProductionRecords: []


  },

  onLoad: function (options) {
    console.log('[Production] onLoad 开始，options:', options);

    // 调用 RealtimeSyncMixin 的 onLoad 方法
    if (RealtimeSyncMixin.onLoad) {
      RealtimeSyncMixin.onLoad.call(this);
    }

    // 获取传入的tab参数
    let activeTab = 0; // 默认值
    if (options.tab !== undefined) {
      activeTab = parseInt(options.tab);
    }
    
    console.log('[Production] 设置 activeTab:', activeTab);
    
    this.setData({
      activeTab: activeTab
    });

    // 初始化实时同步
    this.initRealtimeSync();

    // 页面加载时初始化所有数据
    this.loadHealthRecords();
    this.loadReportData();
    this.initializeMaterialData();
    this.initializeProductionData();
    
    // 先加载分类数据
    console.log('[Production] 开始加载分类数据...');
    this.loadCategories(() => {
      console.log('[Production] 分类数据加载完成，当前 activeTab:', activeTab);
      // 只在知识库标签页时加载文章，避免不必要的资源浪费
      if (activeTab === 3) {
        console.log('[Production] 检测到知识库标签页，开始加载文章...');
        this.loadArticles();
      } else {
        console.log('[Production] 当前不是知识库标签页，不预加载文章');
      }
    });
  },

  onShow: function () {
    // 检查是否有全局变量指定的目标标签页
    const app = getApp();
    if (app.globalData && app.globalData.targetTab !== undefined) {
      console.log('[Production] 检测到全局目标标签页:', app.globalData.targetTab);
      this.setData({
        activeTab: app.globalData.targetTab
      });
      
      // 清除全局变量
      app.globalData.targetTab = undefined;
      
      // 加载对应标签页的数据
      this.loadDataForTab(this.data.activeTab);
    } else {
      // 页面显示时刷新当前Tab的数据
      switch (this.data.activeTab) {
      case 0:
        // 生产管理页面不需要额外加载
        break;
      case 1:
        this.loadHealthRecords();
        break;
      case 2:
        // 物料管理页面不需要额外加载
        break;
      case 3:
        // 知识库页面 - 只在需要时加载
        if (!this.data.articles || this.data.articles.length === 0) {
          this.loadArticles();
        }
        break;
      }
    }
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadHealthRecords(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 切换Tab
  onTabChange: function (e) {
    const index = e.detail.index;
    // 确保index是有效的数字
    if (typeof index === 'number' && index >= 0) {
      this.setData({
        activeTab: index
      });
      
      // 按需加载对应标签页的数据
      this.loadDataForTab(index);
    } else {
      try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('[Production] Invalid tab index', index); } catch(_) {}
      return;
    }
  },
  
  // 根据标签页加载对应数据
  loadDataForTab: function (tabIndex) {
    console.log('[Production] 切换到标签页:', tabIndex, '开始加载对应数据');
    
    switch (tabIndex) {
    case 0:
      // 生产管理页面 - 不需要额外加载
      console.log('[Production] 生产管理页面，无需额外加载');
      break;
    case 1:
      // 健康管理页面 - 加载健康记录
      console.log('[Production] 健康管理页面，加载健康记录');
      this.loadHealthRecords();
      break;
    case 2:
      // 物料管理页面 - 不需要额外加载
      console.log('[Production] 物料管理页面，无需额外加载');
      break;
    case 3:
      // 知识库页面 - 加载文章
      console.log('[Production] 知识库页面，加载文章');
      this.loadArticles();
      break;
    default:
      console.log('[Production] 未知标签页:', tabIndex);
      break;
    }
  },

  // 健康页面二级标签页切换
  onHealthTabChange: function(e) {
    const tabId = parseInt(e.currentTarget.dataset.tab);
    this.setData({
      activeHealthTab: tabId
    });
    
    // 如果切换到健康报告标签页，重新生成报告数据
    if (tabId === 1) {
      this.generateHealthReportFromRecords();
    }
    
    // 如果切换到AI诊断标签页，可以清空之前的诊断结果
    if (tabId === 2) {
      // 可以选择清空诊断结果，让用户重新诊断
      // this.setData({
      //   diagnosisResult: null
      // });
    }
  },

  // 加载健康记录
  loadHealthRecords: function (callback) {
    this.setData({
      loading: true
    });

    // 使用新的统一API客户端获取生产记录
    console.log('使用统一API客户端加载健康记录');

    apiClient.get('/api/v2/production/records', {
      data: { page: 1, size: 20 }
    }).then(res => {
      if (res && res.success && res.data) {
        // 转换API数据格式为前端期望的格式
        const records = (res.data.records || res.data || []).map(record => ({
          id: record.id,
          title: record.symptoms || record.diagnosis || record.treatment || '健康记录',
          date: record.checkDate || record.created_at,
          status: this.mapStatusToDisplay(record.healthStatus, record.checkType),
          description: record.symptoms || record.diagnosis || record.treatment || record.notes || ''
        }));

        this.setData({
          healthRecords: records,
          loading: false
        });

        // 记录数据更新后，重新生成报告数据
        this.generateHealthReportFromRecords();

        callback && callback();
      } else {
        // API失败时使用模拟数据
        console.log('API返回数据格式异常，使用模拟数据');
        this.loadMockHealthRecords(callback);
      }
    }).catch(err => {
      console.log('API请求失败，使用模拟数据:', err);
      // 网络请求失败时使用模拟数据
      this.loadMockHealthRecords(callback);
    });
  },

  // 加载模拟健康记录数据
  loadMockHealthRecords: function (callback) {
    setTimeout(() => {
      const records = [
        {
          id: 1,
          title: '小鹅瘟疫苗接种',
          date: '2023-06-15',
          status: '防疫记录',
          description: '完成小鹅瘟疫苗接种，接种数量100只'
        },
        {
          id: 2,
          title: '呼吸道疾病发现',
          date: '2023-06-20',
          status: '生病记录',
          description: '发现鹅群呼吸道感染症状，患病5只'
        },
        {
          id: 3,
          title: '抗生素治疗',
          date: '2023-06-21',
          status: '治疗记录',
          description: '针对呼吸道感染进行药物治疗'
        },
        {
          id: 4,
          title: '意外死亡记录',
          date: '2023-06-25',
          status: '死亡记录',
          description: '发现1只成年鹅死亡，疑似疾病导致'
        }
      ];

      this.setData({
        healthRecords: records,
        loading: false
      });

      // 记录数据更新后，重新生成报告数据
      this.generateHealthReportFromRecords();

      callback && callback();
    }, 500);
  },

  // 状态映射方法
  mapStatusToDisplay: function(healthStatus, checkType) {
    if (checkType === 'vaccination') {
      return '防疫记录';
    } else if (healthStatus === 'sick' || healthStatus === 'critical') {
      return '生病记录';
    } else if (checkType === 'treatment') {
      return '治疗记录';
    } else if (healthStatus === 'critical' && checkType === 'emergency') {
      return '死亡记录';
    } else {
      return '健康记录';
    }
  },

  // 添加健康记录
  onAddRecord: function () {
    wx.navigateTo({
      url: '/pages/production-detail/record-detail/record-detail'
    });
  },

  // 查看记录详情
  onViewRecord: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/production-detail/record-detail/record-detail?id=${id}`
    });
  },

  // ==================== AI诊断相关方法 ====================

  // 输入症状描述
  onSymptomInput: function (e) {
    this.setData({
      symptoms: e.detail.value
    });
  },

  // 选择图片
  onChooseImage: function () {
    const that = this;
    wx.chooseImage({
      count: 3,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function (res) {
        const tempFiles = res.tempFiles;
        const uploadedImages = that.data.uploadedImages;

        tempFiles.forEach(file => {
          if (uploadedImages.length < 3) {
            uploadedImages.push({
              url: file.path,
              size: file.size
            });
          }
        });

        that.setData({
          uploadedImages: uploadedImages
        });
      }
    });
  },

  // 删除图片
  onDeleteImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const uploadedImages = this.data.uploadedImages;
    uploadedImages.splice(index, 1);
    this.setData({
      uploadedImages: uploadedImages
    });
  },

  // 开始诊断
  onStartDiagnosis: function () {
    const { symptoms, uploadedImages } = this.data;

    if (!symptoms && uploadedImages.length === 0) {
      wx.showToast({
        title: '请填写症状或上传图片',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isDiagnosing: true,
      diagnosisResult: null
    });

    // 调用AI诊断服务
    this.callAIDiagnosis();
  },

  // 调用AI诊断服务
  async callAIDiagnosis() {
    try {
      // 引入AI服务
      const { callAIService } = require('../../utils/ai-service.js');

      // 构建诊断提示
      const userMessage = `
症状描述：${this.data.symptoms || '无明显症状描述'}

${this.data.uploadedImages.length > 0 ? '已上传图片：' + this.data.uploadedImages.length + '张' : ''}

请根据以上症状描述，对鹅的健康状况进行专业诊断分析。
      `.trim();

      // 调用AI诊断
      const result = await callAIService('HEALTH_DIAGNOSIS', userMessage);

      if (result.success) {
        // 解析AI返回的诊断结果
        const diagnosisContent = result.data.content;

        // 尝试解析结构化的诊断结果
        const parsedResult = this.parseAIDiagnosisResult(diagnosisContent);

        this.setData({
          diagnosisResult: parsedResult,
          isDiagnosing: false
        });

        wx.showToast({
          title: 'AI诊断完成',
          icon: 'success'
        });

      } else {
        // AI诊断失败，使用备用诊断
        this.fallbackDiagnosis();
      }

    } catch (error) {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('AI诊断错误', error); } catch(_) {}
      // 网络错误，使用备用诊断
      this.fallbackDiagnosis();
    }
  },

  // 解析AI诊断结果
  parseAIDiagnosisResult(content) {
    try {
      // 尝试从AI返回的文本中提取结构化信息
      const lines = content.split('\n').filter(line => line.trim());

      let disease = '未知疾病';
      let confidence = '待评估';
      let description = '';
      let suggestions = [];
      let medications = [];

      // 简单的文本解析逻辑
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        if (line.includes('可能疾病') || line.includes('诊断结果')) {
          const match = line.match(/[:：](.+)/);
          if (match) disease = match[1].trim();
        }

        if (line.includes('概率') || line.includes('置信度')) {
          const match = line.match(/(\d+%)/);
          if (match) confidence = match[1];
        }

        if (line.includes('症状分析') || line.includes('病情描述')) {
          const match = line.match(/[:：](.+)/);
          if (match) description = match[1].trim();
        }

        if (line.includes('治疗建议') || line.includes('处理建议')) {
          // 收集后续的建议行
          for (let j = i + 1; j < lines.length && j < i + 5; j++) {
            const suggestionLine = lines[j].trim();
            if (suggestionLine && !suggestionLine.includes(':') && !suggestionLine.includes('：')) {
              suggestions.push(suggestionLine.replace(/^[-•\d\.]\s*/, ''));
            }
          }
        }
      }

      // 如果解析失败，使用原始内容作为描述
      if (!description && content.length > 0) {
        description = content.substring(0, 200) + (content.length > 200 ? '...' : '');
      }

      return {
        disease: disease,
        confidence: confidence,
        description: description,
        suggestions: suggestions.length > 0 ? suggestions : [
          '建议咨询专业兽医',
          '密切观察鹅群状况',
          '加强饲养管理'
        ],
        medications: medications.length > 0 ? medications : [
          { name: '请咨询兽医', dosage: '根据专业建议使用' }
        ],
        aiGenerated: true,
        rawContent: content
      };

    } catch (error) {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('解析AI诊断结果失败', error); } catch(_) {}
      return this.getDefaultDiagnosisResult();
    }
  },

  // 备用诊断（当AI服务不可用时）
  fallbackDiagnosis() {
    wx.showModal({
      title: 'AI诊断服务连接失败',
      content: '将为您提供基础诊断建议，建议咨询专业兽医获取准确诊断',
      showCancel: false,
      success: () => {
        const result = this.getDefaultDiagnosisResult();
        this.setData({
          diagnosisResult: result,
          isDiagnosing: false
        });
      }
    });
  },

  // 获取默认诊断结果
  getDefaultDiagnosisResult() {
    return {
      disease: '需要进一步检查',
      confidence: '建议专业诊断',
      description: '根据您提供的症状描述，建议联系专业兽医进行详细检查以获得准确诊断。',
      suggestions: [
        '立即隔离疑似病鹅',
        '联系专业兽医进行检查',
        '保持鹅舍清洁卫生',
        '密切观察鹅群健康状况'
      ],
      medications: [
        { name: '请咨询兽医', dosage: '根据专业建议使用药物' }
      ],
      aiGenerated: false
    };
  },

  // 清空诊断结果
  onClearResult: function () {
    this.setData({
      diagnosisResult: null,
      symptoms: '',
      uploadedImages: []
    });
  },

  // ==================== 知识库相关方法 ====================

  // 加载分类 - 使用统一数据源
  loadCategories: function (callback) {
    console.log('[Production] loadCategories 开始执行');
    
    try {
      // 引入统一的知识库数据
      const { getCategories } = require('../../utils/knowledge-data.js');
      console.log('[Production] 成功引入知识库数据模块');
      
      const categories = getCategories();
      console.log('[Production] 获取到分类数据:', categories);
      
      this.setData({
        categories: categories
      });
      console.log('[Production] 分类数据设置完成');
      
      callback && callback();
    } catch (error) {
      console.error('[Production] 加载分类失败:', error);
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Production] 加载分类失败', error); } catch(_) {}
      // 使用备用数据
      const categories = [
        { id: 'all', name: '全部' },
        { id: 'feed', name: '饲料营养' },
        { id: 'breed', name: '品种繁育' },
        { id: 'manage', name: '饲养管理' }
      ];
      console.log('[Production] 使用备用分类数据:', categories);
      this.setData({ categories: categories });
      
      callback && callback();
    }
  },

  // 加载文章列表 - 使用统一数据源
  loadArticles: function (callback) {
    console.log('[Production] 开始加载文章，当前分类:', this.data.activeCategory);
    
    this.setData({
      loading: true
    });

    setTimeout(() => {
      try {
        // 引入统一的知识库数据
        const { getArticlesByCategory, searchArticles } = require('../../utils/knowledge-data.js');
        
        let filteredArticles;

        // 如果有搜索关键词，使用搜索功能
        const searchKeyword = this.data.searchKeyword;
        const activeCategory = this.data.activeCategory;
        console.log('[Production] 搜索关键词:', searchKeyword, '当前分类:', activeCategory);
        
        if (searchKeyword && searchKeyword.trim()) {
          filteredArticles = searchArticles(searchKeyword.trim(), activeCategory);
        } else {
          // 否则按分类获取文章
          filteredArticles = getArticlesByCategory(activeCategory);
        }

        console.log('[Production] 获取到文章数量:', filteredArticles.length);

        this.setData({
          articles: filteredArticles,
          loading: false
        });

        callback && callback();
      } catch (error) {
        console.error('[Production] 加载文章失败:', error);
        try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Production] 加载文章失败', error); } catch(_) {}
        this.setData({
          articles: [],
          loading: false
        });
        callback && callback();
      }
    }, 500);
  },

  // 搜索输入
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch: function () {
    const searchKeyword = this.data.searchKeyword.trim();

    if (searchKeyword) {
      // 如果有搜索关键词，先进行AI智能搜索
      this.performAISearch(searchKeyword);
    } else {
      // 没有关键词，正常加载文章
      this.loadArticles();
    }
  },

  // AI智能搜索
  async performAISearch(keyword) {
    try {
      this.setData({ loading: true });

      // 引入AI服务
      const { callAIService } = require('../../utils/ai-service.js');

      // 构建搜索提示
      const userMessage = `
用户搜索关键词：${keyword}

请根据这个关键词，推荐相关的鹅类养殖知识内容，包括：
1. 相关的疾病防治知识
2. 饲料营养相关内容
3. 品种繁育相关信息
4. 饲养管理技巧

请提供3-5个具体的知识点标题和简要说明。
      `.trim();

      // 调用AI知识问答服务
      const result = await callAIService('KNOWLEDGE_QA', userMessage);

      if (result.success) {
        // 解析AI推荐的内容
        const recommendations = this.parseAIRecommendations(result.data.content);

        // 合并AI推荐和现有文章
        const filteredArticles = this.filterArticlesByKeyword(keyword);
        const enhancedArticles = [...recommendations, ...filteredArticles];

        this.setData({
          articles: enhancedArticles,
          loading: false,
          aiSearchUsed: true
        });

        if (recommendations.length > 0) {
          wx.showToast({
            title: `AI为您推荐了${recommendations.length}个相关内容`,
            icon: 'none',
            duration: 2000
          });
        }

      } else {
        // AI搜索失败，使用普通搜索
        this.loadArticles();
      }

    } catch (error) {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('AI搜索失败', error); } catch(_) {}
      // 出错时使用普通搜索
      this.loadArticles();
    }
  },

  // 解析AI推荐内容
  parseAIRecommendations(content) {
    try {
      const lines = content.split('\n').filter(line => line.trim());
      const recommendations = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // 查找标题行（通常包含数字序号或特殊标记）
        if (line.match(/^\d+[\.、]/) || line.includes('：') || line.includes(':')) {
          let title = line.replace(/^\d+[\.、]\s*/, '').split(/[：:]/)[0].trim();
          let summary = '';

          // 查找描述内容
          if (line.includes('：') || line.includes(':')) {
            summary = line.split(/[：:]/)[1]?.trim() || '';
          }

          // 如果当前行没有描述，查看下一行
          if (!summary && i + 1 < lines.length) {
            const nextLine = lines[i + 1].trim();
            if (nextLine && !nextLine.match(/^\d+[\.、]/)) {
              summary = nextLine;
            }
          }

          if (title && title.length > 3) {
            recommendations.push({
              id: `ai_rec_${recommendations.length + 1}`,
              title: title,
              summary: summary || '详细内容请咨询专业兽医',
              category: this.getCategoryByTitle(title),
              isAIRecommendation: true,
              readCount: Math.floor(Math.random() * 1000) + 100,
              publishTime: '刚刚'
            });
          }
        }
      }

      return recommendations.slice(0, 5); // 最多返回5个推荐

    } catch (error) {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('解析AI推荐失败', error); } catch(_) {}
      return [];
    }
  },

  // 根据标题判断分类
  getCategoryByTitle(title) {
    if (title.includes('病') || title.includes('疾') || title.includes('治') || title.includes('防')) {
      return 'disease';
    } else if (title.includes('饲料') || title.includes('营养') || title.includes('喂')) {
      return 'nutrition';
    } else if (title.includes('繁殖') || title.includes('育种') || title.includes('品种')) {
      return 'breeding';
    } else {
      return 'management';
    }
  },

  // 按关键词筛选现有文章
  filterArticlesByKeyword(keyword) {
    const allArticles = this.getAllArticles();
    return allArticles.filter(article =>
      article.title.includes(keyword) ||
      article.summary.includes(keyword) ||
      article.content?.includes(keyword)
    );
  },

  // 获取所有文章（用于搜索） - 使用统一数据源
  getAllArticles() {
    try {
      const { getAllArticles } = require('../../utils/knowledge-data.js');
      
      const articles = getAllArticles();
      this.setData({
        allArticles: articles
      });
      return articles;
    } catch (error) {
      console.error('获取所有文章失败:', error);
      return [];
    }
  },

  // 分类变化
  onCategoryChange: function (e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      activeCategory: category,
      aiSearchUsed: false // 重置AI搜索标记
    });
    this.loadArticles();
  },

  // 查看文章
  onViewArticle: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/production-detail/knowledge/detail/detail?id=${id}`
    });
  },

  // ==================== 健康报告相关方法 ====================

  // 切换报告类型
  onReportTypeChange: function (e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      activeReportType: type
    });
    this.loadReportData();
  },

  // 加载报告数据
  loadReportData: function (callback) {
    // 直接从健康记录生成报告数据
    this.generateHealthReportFromRecords();
    callback && callback();
  },

  // 从健康记录生成报告数据
  generateHealthReportFromRecords: function() {
    const records = this.data.healthRecords || [];
    
    // 统计总体数据
    const overview = this.calculateOverviewStats(records);
    
    // 统计疾病数据
    const diseaseStats = this.calculateDiseaseStats(records);
    
    // 统计治疗效果
    const treatmentStats = this.calculateTreatmentStats(records);
    
    // 生成趋势数据
    const trendData = this.calculateTrendData(records);
    
    const reportData = {
      overview,
      diseaseStats,
      treatmentStats,
      trendData,
      updateTime: new Date().toLocaleString()
    };

    this.setData({
      reportData: reportData
    });
  },

  // 计算总体统计数据
  calculateOverviewStats: function(records) {
    const sickRecords = records.filter(r => r.status === '生病记录');
    const treatmentRecords = records.filter(r => r.status === '治疗记录');
    const deathRecords = records.filter(r => r.status === '死亡记录');
    
    // 从生产记录中获取总数量（这里使用默认值，实际应该从生产记录API获取）
    const totalGeese = 1200; // 默认值，实际应该从生产记录获取
    
    // 计算死亡数量（来自健康记录）
    const deathCount = deathRecords.length;
    
    // 计算患病数量（来自健康记录）
    const sickCount = sickRecords.length;
    
    // 计算健康数量（动态计算：总数量 - 患病 - 死亡）
    const healthyCount = Math.max(0, totalGeese - sickCount - deathCount);
    
    // 计算总损耗
    const totalLoss = sickCount + deathCount;
    
    // 计算损耗率
    const lossRate = totalGeese > 0 ? 
      ((totalLoss / totalGeese) * 100).toFixed(1) + '%' : '0%';
    
    // 计算健康率
    const healthyRate = totalGeese > 0 ? 
      ((healthyCount / totalGeese) * 100).toFixed(1) + '%' : '0%';
    
    return {
      totalGeese: totalGeese,
      healthyCount: healthyCount,
      sickCount: sickCount,
      deathCount: deathCount,
      totalLoss: totalLoss,
      lossRate: lossRate,
      healthyRate: healthyRate
    };
  },

  // 计算疾病统计数据
  calculateDiseaseStats: function(records) {
    const sickRecords = records.filter(r => r.status === '生病记录');
    const diseaseMap = {};
    
    if (sickRecords.length === 0) {
      // 如果没有生病记录，返回默认数据
      return [
        { name: '小鹅瘟', value: 25, rate: '38.5%' },
        { name: '禽流感', value: 18, rate: '27.7%' },
        { name: '大肠杆菌病', value: 12, rate: '18.5%' },
        { name: '寄生虫病', value: 7, rate: '10.8%' },
        { name: '人畜共患病', value: 3, rate: '4.6%' }
      ];
    }
    
    let totalSick = 0;
    sickRecords.forEach(record => {
      const diseaseType = record.title || record.description || '未知疾病';
      const count = 1;
      
      diseaseMap[diseaseType] = (diseaseMap[diseaseType] || 0) + count;
      totalSick += count;
    });
    
    // 转换为数组并计算百分比
    const diseaseStats = Object.entries(diseaseMap)
      .map(([name, value]) => ({
        name,
        value,
        rate: totalSick > 0 ? ((value / totalSick) * 100).toFixed(1) + '%' : '0%'
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 5); // 只显示前5种疾病
    
    return diseaseStats;
  },

  // 计算治疗效果统计
  calculateTreatmentStats: function(records) {
    const treatmentRecords = records.filter(r => r.status === '治疗记录');
    
    if (treatmentRecords.length === 0) {
      // 如果没有治疗记录，返回默认数据
      return [
        { name: '治愈', value: 52, rate: '80.0%' },
        { name: '好转', value: 9, rate: '15.0%' },
        { name: '死亡', value: 3, rate: '5.0%' }
      ];
    }
    
    const effectMap = { '治愈': 0, '好转': 0, '死亡': 0 };
    let totalTreatment = treatmentRecords.length;
    
    treatmentRecords.forEach(record => {
      // 根据记录内容判断治疗效果
      const description = (record.description || '').toLowerCase();
      if (description.includes('治愈') || description.includes('康复')) {
        effectMap['治愈']++;
      } else if (description.includes('好转') || description.includes('改善')) {
        effectMap['好转']++;
      } else if (description.includes('死亡') || description.includes('不治')) {
        effectMap['死亡']++;
      } else {
        effectMap['治愈']++; // 默认归为治愈
      }
    });
    
    // 转换为数组并计算百分比
    const treatmentStats = Object.entries(effectMap)
      .map(([name, value]) => ({
        name,
        value,
        rate: totalTreatment > 0 ? ((value / totalTreatment) * 100).toFixed(1) + '%' : '0%'
      }))
      .filter(item => item.value > 0)
      .sort((a, b) => b.value - a.value);
    
    return treatmentStats;
  },

  // 计算趋势数据
  calculateTrendData: function(records) {
    const monthlyData = {};
    
    if (records.length === 0) {
      // 如果没有记录，返回默认趋势数据
      return [
        { month: '2023-06', 入栏记录: 2, 防疫记录: 3, 生病记录: 1, 治疗记录: 1, 死亡记录: 0 }
      ];
    }
    
    records.forEach(record => {
      const month = record.date ? record.date.substring(0, 7) : '2023-06';
      
      if (!monthlyData[month]) {
        monthlyData[month] = {
          month,
          入栏记录: 0,
          防疫记录: 0,
          生病记录: 0,
          治疗记录: 0,
          死亡记录: 0
        };
      }
      
      if (monthlyData[month][record.status] !== undefined) {
        monthlyData[month][record.status]++;
      }
    });
    
    // 转换为数组并排序
    const trendData = Object.values(monthlyData)
      .sort((a, b) => a.month.localeCompare(b.month))
      .slice(-6); // 只显示最近6个月
    
    return trendData;
  },

  // 加载模拟报告数据
  loadMockReportData: function (callback) {
    setTimeout(() => {
      const { activeReportType } = this.data;

      // 根据报告类型生成不同的数据
      let overview, diseaseStats, treatmentStats, trendData;

      switch (activeReportType) {
      case 'week':
        overview = {
          totalGeese: 1200,
          healthyCount: 1120,
          sickCount: 65,
          deathCount: 15,
          healthyRate: '93.3%'
        };
        diseaseStats = [
          { name: '小鹅瘟', value: 25, rate: '38.5%' },
          { name: '禽流感', value: 18, rate: '27.7%' },
          { name: '大肠杆菌病', value: 12, rate: '18.5%' },
          { name: '寄生虫病', value: 7, rate: '10.8%' }
        ];
        trendData = [
          { date: '06-09', healthy: 1150, sick: 35, death: 15 },
          { date: '06-11', healthy: 1140, sick: 45, death: 15 },
          { date: '06-13', healthy: 1130, sick: 55, death: 15 },
          { date: '06-15', healthy: 1120, sick: 65, death: 15 }
        ];
        break;

      case 'month':
        overview = {
          totalGeese: 1200,
          healthyCount: 1050,
          sickCount: 120,
          deathCount: 30,
          healthyRate: '87.5%'
        };
        diseaseStats = [
          { name: '小鹅瘟', value: 45, rate: '37.5%' },
          { name: '禽流感', value: 32, rate: '26.7%' },
          { name: '大肠杆菌病', value: 25, rate: '20.8%' },
          { name: '寄生虫病', value: 18, rate: '15.0%' }
        ];
        trendData = [
          { date: '05-15', healthy: 1180, sick: 15, death: 5 },
          { date: '05-22', healthy: 1160, sick: 25, death: 15 },
          { date: '05-29', healthy: 1140, sick: 40, death: 20 },
          { date: '06-05', healthy: 1120, sick: 60, death: 20 },
          { date: '06-12', healthy: 1080, sick: 90, death: 30 },
          { date: '06-15', healthy: 1050, sick: 120, death: 30 }
        ];
        break;

      case 'quarter':
        overview = {
          totalGeese: 1200,
          healthyCount: 980,
          sickCount: 150,
          deathCount: 70,
          healthyRate: '81.7%'
        };
        diseaseStats = [
          { name: '小鹅瘟', value: 58, rate: '38.7%' },
          { name: '禽流感', value: 42, rate: '28.0%' },
          { name: '大肠杆菌病', value: 30, rate: '20.0%' },
          { name: '寄生虫病', value: 20, rate: '13.3%' }
        ];
        trendData = [
          { date: '04月', healthy: 1150, sick: 30, death: 20 },
          { date: '05月', healthy: 1100, sick: 70, death: 30 },
          { date: '06月', healthy: 980, sick: 150, death: 70 }
        ];
        break;

      case 'year':
        overview = {
          totalGeese: 1200,
          healthyCount: 900,
          sickCount: 200,
          deathCount: 100,
          healthyRate: '75.0%'
        };
        diseaseStats = [
          { name: '小鹅瘟', value: 75, rate: '37.5%' },
          { name: '禽流感', value: 55, rate: '27.5%' },
          { name: '大肠杆菌病', value: 40, rate: '20.0%' },
          { name: '寄生虫病', value: 30, rate: '15.0%' }
        ];
        trendData = [
          { date: '1季度', healthy: 1100, sick: 50, death: 50 },
          { date: '2季度', healthy: 1000, sick: 100, death: 100 },
          { date: '3季度', healthy: 950, sick: 150, death: 100 },
          { date: '4季度', healthy: 900, sick: 200, death: 100 }
        ];
        break;
      }

      // 治疗效果数据（相对稳定）
      treatmentStats = [
        { name: '治愈', value: Math.floor(overview.sickCount * 0.8), rate: '80.0%' },
        { name: '好转', value: Math.floor(overview.sickCount * 0.15), rate: '15.0%' },
        { name: '死亡', value: Math.floor(overview.sickCount * 0.05), rate: '5.0%' }
      ];

      // 生成趋势数据
      trendData = this.generateTrendData(activeReportType, overview);

      this.setData({
        'reportData.overview': overview,
        'reportData.diseaseStats': diseaseStats,
        'reportData.treatmentStats': treatmentStats,
        'reportData.trendData': trendData,
        'reportData.updateTime': new Date().toLocaleString(),
        loading: false
      });

      // 保存健康数据到全局状态，供其他模块使用
      this.saveHealthReportDataForSharing(overview, trendData);

      callback && callback();
    }, 500);
  },

  // 生成趋势数据
  generateTrendData: function(reportType, overview) {
    const now = new Date();
    let trendData = [];

    switch (reportType) {
    case 'week':
      // 生成7天的数据
      for (let i = 6; i >= 0; i--) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        const dateStr = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

        // 模拟数据变化趋势
        const progress = (6 - i) / 6;
        const healthyVariation = Math.floor(Math.random() * 40 - 20);
        const sickVariation = Math.floor(Math.random() * 20 - 10);
        const deathVariation = Math.floor(Math.random() * 10 - 5);

        trendData.push({
          date: dateStr,
          healthy: Math.max(0, overview.healthyCount + healthyVariation),
          sick: Math.max(0, overview.sickCount + sickVariation),
          death: Math.max(0, overview.deathCount + deathVariation)
        });
      }
      break;

    case 'month':
      // 生成30天的数据，每5天一个点
      for (let i = 25; i >= 0; i -= 5) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        const dateStr = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

        const progress = (25 - i) / 25;
        const healthyBase = 1200 - Math.floor(progress * 150);
        const sickBase = Math.floor(progress * 120);
        const deathBase = Math.floor(progress * 30);

        trendData.push({
          date: dateStr,
          healthy: healthyBase + Math.floor(Math.random() * 40 - 20),
          sick: sickBase + Math.floor(Math.random() * 20 - 10),
          death: deathBase + Math.floor(Math.random() * 10 - 5)
        });
      }
      break;

    case 'quarter':
      // 生成3个月的数据
      const months = ['04月', '05月', '06月'];
      months.forEach((month, index) => {
        const progress = index / 2;
        const healthyBase = 1200 - Math.floor(progress * 220);
        const sickBase = Math.floor(progress * 150);
        const deathBase = Math.floor(progress * 70);

        trendData.push({
          date: month,
          healthy: healthyBase + Math.floor(Math.random() * 50 - 25),
          sick: sickBase + Math.floor(Math.random() * 30 - 15),
          death: deathBase + Math.floor(Math.random() * 15 - 7)
        });
      });
      break;

    case 'year':
      // 生成4个季度的数据
      const quarters = ['1季度', '2季度', '3季度', '4季度'];
      quarters.forEach((quarter, index) => {
        const progress = index / 3;
        const healthyBase = 1200 - Math.floor(progress * 300);
        const sickBase = Math.floor(progress * 200);
        const deathBase = Math.floor(progress * 100);

        trendData.push({
          date: quarter,
          healthy: healthyBase + Math.floor(Math.random() * 60 - 30),
          sick: sickBase + Math.floor(Math.random() * 40 - 20),
          death: deathBase + Math.floor(Math.random() * 20 - 10)
        });
      });
      break;
    }

    return trendData;
  },



  // 保存健康报告数据供其他模块使用
  saveHealthReportDataForSharing: function(overview, trendData) {
    try {
      // 将健康数据保存到全局应用数据中
      const app = getApp();
      if (app.globalData) {
        app.globalData.healthReportData = {
          overview: overview,
          trendData: trendData,
          lastUpdateTime: new Date().getTime()
        };
      }
      
      // 也可以保存到本地存储
      wx.setStorageSync('healthReportData', {
        overview: overview,
        trendData: trendData,
        lastUpdateTime: new Date().getTime()
      });
      
    } catch (error) {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('保存健康报告数据失败', error); } catch(_) {}
    }
  },

  // 导出报告
  onExportReport: function () {
    wx.showActionSheet({
      itemList: ['导出健康统计表', '导出AI诊断报告', '导出健康趋势图', '导出详细记录'],
      success: (res) => {
        const exportTypes = ['statistics', 'ai_diagnosis', 'trends', 'records'];
        const selectedType = exportTypes[res.tapIndex];
        this.executeHealthExport(selectedType);
      }
    });
  },

  // 执行健康数据导出
  executeHealthExport: function(type) {
    wx.showLoading({
      title: '生成报告中...'
    });

    // 准备导出数据
    const exportData = this.prepareHealthExportData(type);
    
    setTimeout(() => {
      wx.hideLoading();
      this.showExportResult(type, exportData);
    }, 2000);
  },

  // 准备健康导出数据
  prepareHealthExportData: function(type) {
    const healthRecords = this.data.healthRecords || [];
    const healthStats = this.data.healthStats || {};
    const aiDiagnosis = this.data.aiDiagnosis || {};
    
    const baseData = {
      exportTime: new Date().toISOString(),
      totalRecords: healthRecords.length,
      flockInfo: {
        totalFlocks: this.data.flocks?.length || 0,
        healthyFlocks: healthRecords.filter(r => r.status === 'healthy').length,
        sicksFlocks: healthRecords.filter(r => r.status === 'sick').length
      }
    };

    switch(type) {
    case 'statistics':
      return {
        ...baseData,
        type: '健康统计表',
        data: {
          healthRate: healthStats.healthRate || '85%',
          treatmentSuccess: healthStats.treatmentSuccess || '92%',
          preventionCoverage: healthStats.preventionCoverage || '98%',
          categoryStats: this.groupHealthByCategory(healthRecords),
          monthlyTrend: this.calculateHealthTrend(healthRecords)
        }
      };
      
    case 'ai_diagnosis':
      return {
        ...baseData,
        type: 'AI诊断报告',
        data: {
          diagnosisCount: aiDiagnosis.totalDiagnosis || 0,
          accuracy: aiDiagnosis.accuracy || '94%',
          commonDiseases: aiDiagnosis.commonDiseases || [],
          recommendations: aiDiagnosis.recommendations || [],
          aiInsights: this.generateAIInsights(healthRecords)
        }
      };
      
    case 'trends':
      return {
        ...baseData,
        type: '健康趋势图',
        data: {
          trendData: this.calculateHealthTrend(healthRecords),
          seasonalAnalysis: this.analyzeSeasonalHealth(healthRecords),
          riskPrediction: this.predictHealthRisks(healthRecords)
        }
      };
      
    case 'records':
      return {
        ...baseData,
        type: '详细记录',
        data: {
          records: healthRecords.map(record => ({
            id: record.id,
            date: record.date,
            flockName: record.flockName,
            healthStatus: record.status,
            symptoms: record.symptoms,
            treatment: record.treatment,
            outcome: record.outcome
          })),
          summary: {
            byStatus: this.groupRecordsByStatus(healthRecords),
            byMonth: this.groupRecordsByMonth(healthRecords)
          }
        }
      };
      
    default:
      return baseData;
    }
  },

  // 按类别分组健康数据
  groupHealthByCategory: function(records) {
    const categories = {};
    records.forEach(record => {
      const category = record.category || '其他';
      if (!categories[category]) {
        categories[category] = {
          total: 0,
          healthy: 0,
          sick: 0,
          rate: 0
        };
      }
      categories[category].total++;
      if (record.status === 'healthy') {
        categories[category].healthy++;
      } else {
        categories[category].sick++;
      }
      categories[category].rate = categories[category].total > 0 ? 
        (categories[category].healthy / categories[category].total * 100) : 0;
    });
    return categories;
  },

  // 计算健康趋势
  calculateHealthTrend: function(records) {
    const trend = {};
    records.forEach(record => {
      const month = record.date?.substring(0, 7) || new Date().toISOString().substring(0, 7);
      if (!trend[month]) {
        trend[month] = { total: 0, healthy: 0, rate: 0 };
      }
      trend[month].total++;
      if (record.status === 'healthy') {
        trend[month].healthy++;
      }
      trend[month].rate = trend[month].total > 0 ? 
        (trend[month].healthy / trend[month].total * 100) : 0;
    });
    return trend;
  },

  // 生成AI洞察
  generateAIInsights: function(records) {
    return [
      '春季呼吸道疾病发病率较高，建议加强通风',
      '饲料质量与肠道健康呈正相关',
      '定期疫苗接种可有效降低传染病风险',
      '环境温度控制对雏鹅成活率影响显著'
    ];
  },

  // 分析季节性健康状况
  analyzeSeasonalHealth: function(records) {
    const seasons = { spring: 0, summer: 0, autumn: 0, winter: 0 };
    records.forEach(record => {
      if (record.date) {
        const month = new Date(record.date).getMonth() + 1;
        if (month >= 3 && month <= 5) seasons.spring++;
        else if (month >= 6 && month <= 8) seasons.summer++;
        else if (month >= 9 && month <= 11) seasons.autumn++;
        else seasons.winter++;
      }
    });
    return seasons;
  },

  // 预测健康风险
  predictHealthRisks: function(records) {
    return [
      { risk: '呼吸道疾病', probability: '15%', season: '春季' },
      { risk: '肠道感染', probability: '12%', season: '夏季' },
      { risk: '营养不良', probability: '8%', season: '冬季' }
    ];
  },

  // 按状态分组记录
  groupRecordsByStatus: function(records) {
    const groups = {};
    records.forEach(record => {
      const status = record.status || 'unknown';
      groups[status] = (groups[status] || 0) + 1;
    });
    return groups;
  },

  // 按月份分组记录
  groupRecordsByMonth: function(records) {
    const groups = {};
    records.forEach(record => {
      const month = record.date?.substring(0, 7) || 'unknown';
      groups[month] = (groups[month] || 0) + 1;
    });
    return groups;
  },

  // 显示导出结果
  showExportResult: function(type, data) {
    const typeNames = {
      'statistics': '健康统计表',
      'ai_diagnosis': 'AI诊断报告',
      'trends': '健康趋势图',
      'records': '详细记录'
    };

    wx.showModal({
      title: '导出完成',
      content: `${typeNames[type]}已生成\n\n包含数据：\n• 记录总数：${data.totalRecords}条\n• 健康鹅群：${data.flockInfo.healthyFlocks}个\n• 导出时间：${new Date().toLocaleString()}\n\n文件已保存到本地`,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // ==================== 物料管理相关方法 ====================

  // 初始化物料数据
  initializeMaterialData: function() {
    this.calculateMaterialStats();
    this.filterMaterialList();
  },

  // 计算物料统计数据
  calculateMaterialStats: function() {
    const materialList = this.data.materialList || [];
    
    const feedCount = materialList.filter(item => item.category === 'feed').length;
    const medicineCount = materialList.filter(item => item.category === 'medicine').length;
    const lowStockItems = materialList.filter(item => item.status === 'danger').length;
    
    this.setData({
      materialStats: {
        feedCount,
        medicineCount,
        lowStockItems
      }
    });
  },

  // 过滤物料列表
  filterMaterialList: function() {
    const { materialList, activeMaterialTab } = this.data;
    
    let filteredList = materialList;
    if (activeMaterialTab !== 'all') {
      filteredList = materialList.filter(item => item.category === activeMaterialTab);
    }
    
    this.setData({
      filteredMaterialList: filteredList
    });
  },

  // 物料标签页切换
  onMaterialTabChange: function(e) {
    const tabId = e.currentTarget.dataset.tab;
    this.setData({
      activeMaterialTab: tabId
    });
    this.filterMaterialList();
  },

  // 添加物料
  onAddMaterial: function() {
    wx.navigateTo({
      url: '/pages/production-detail/material-detail/material-detail'
    });
  },



  // 物料统计点击事件
  onMaterialStatsTap: function(e) {
    const type = e.currentTarget.dataset.type;
    let tabId = 'all';
    
    switch(type) {
    case 'feed':
      tabId = 'feed';
      break;
    case 'medicine':
      tabId = 'medicine';
      break;
    case 'lowstock':
      tabId = 'all';
      break;
    }
    
    this.setData({
      activeMaterialTab: tabId
    });
    this.filterMaterialList();
  },

  // ==================== 生产管理相关方法 ====================

  // 初始化生产数据
  initializeProductionData: function() {
    // 初始化生产记录数据
    const records = [
      {
        id: 1,
        type: 'entry',
        batch: 'QY-20230616',
        date: '2023-06-16',
        status: 'active',
        statusText: '饲养中',
        relatedFinance: true,
        details: {
          count: 1000,
          weight: 850,
          source: '江苏优质鹅苗场',
          breed: '太湖鹅',
          cost: 28500
        }
      },
      {
        id: 2,
        type: 'sale',
        batch: 'QY-20230616',
        date: '2023-06-15',
        status: 'partial_sold',
        statusText: '部分出栏',
        relatedFinance: true,
        details: {
          count: 200,
          weight: 3.2,
          price: 28,
          buyer: '华联超市',
          totalIncome: 17920
        }
      },
      {
        id: 3,
        type: 'weight',
        batch: 'QY-20230605',
        date: '2023-06-14',
        status: 'active',
        statusText: '饲养中',
        relatedFinance: false,
        details: {
          count: 500,
          weight: 2.3,
          growthRate: 75
        }
      },
      {
        id: 4,
        type: 'entry',
        batch: 'QY-20230605',
        date: '2023-06-05',
        status: 'active',
        statusText: '饲养中',
        relatedFinance: true,
        details: {
          count: 800,
          weight: 680,
          source: '本地养殖合作社',
          breed: '四川白鹅',
          cost: 22400
        }
      },
      {
        id: 5,
        type: 'weight',
        batch: 'QY-20230616',
        date: '2023-06-20',
        status: 'active',
        statusText: '饲养中',
        relatedFinance: false,
        details: {
          count: 800,
          weight: 2.8,
          growthRate: 85
        }
      },
      {
        id: 6,
        type: 'sale',
        batch: 'QY-20230605',
        date: '2023-07-01',
        status: 'completed',
        statusText: '已完成',
        relatedFinance: true,
        details: {
          count: 300,
          weight: 3.5,
          price: 32,
          buyer: '农贸市场',
          totalIncome: 33600
        }
      },
      {
        id: 7,
        type: 'entry',
        batch: 'QY-20230701',
        date: '2023-07-01',
        status: 'active',
        statusText: '饲养中',
        relatedFinance: true,
        details: {
          count: 1200,
          weight: 960,
          source: '专业种鹅场',
          breed: '皖西白鹅',
          cost: 36000
        }
      },
      {
        id: 8,
        type: 'weight',
        batch: 'QY-20230701',
        date: '2023-07-15',
        status: 'active',
        statusText: '饲养中',
        relatedFinance: false,
        details: {
          count: 1200,
          weight: 1.8,
          growthRate: 45
        }
      }
    ];

    // 为每条记录计算批次颜色索引
    const recordsWithColorIndex = records.map(record => {
      const colorIndex = this.calculateBatchColorIndex(record.batch);
      return {
        ...record,
        batchColorIndex: colorIndex
      };
    });

    this.setData({
      productionRecords: recordsWithColorIndex
    });

    this.filterRecordList();
  },

  // 计算批次颜色索引
  calculateBatchColorIndex: function(batchNumber) {
    if (!batchNumber) return 0;
    
    // 使用批次号字符串计算hash值
    let hash = 0;
    for (let i = 0; i < batchNumber.length; i++) {
      const char = batchNumber.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    // 取绝对值并对颜色数量取模
    return Math.abs(hash) % 8;
  },

  // 过滤生产记录列表
  filterRecordList: function() {
    const { productionRecords, activeRecordTab } = this.data;
    
    let filteredList = productionRecords;
    if (activeRecordTab !== 'all') {
      filteredList = productionRecords.filter(item => item.type === activeRecordTab);
    }
    
    this.setData({
      filteredProductionRecords: filteredList
    });
  },

  // 生产记录标签页切换
  onRecordTabChange: function(e) {
    const tabId = e.currentTarget.dataset.tab;
    this.setData({
      activeRecordTab: tabId
    });
    this.filterRecordList();
  },

  // 添加生产记录
  onAddProductionRecord: function() {
    // 直接跳转到生产记录表单页面，使用标签页选择类型
    wx.navigateTo({
      url: '/pages/production-modules/record-add/record-add'
    });
  },

  // 查看生产记录详情
  onViewProductionRecord: function(e) {
    const recordId = e.currentTarget.dataset.id;
    const record = this.data.productionRecords.find(r => r.id === recordId);
    
    if (record) {
      // 准备弹窗数据
      const modalData = {
        id: record.id,
        type: record.type,
        batch: record.batch,
        date: record.date,
        count: record.details.count,
        notes: record.notes || ''
      };
      
      // 根据记录类型添加特定信息
      switch(record.type) {
      case 'entry':
        modalData.source = record.details.source || '未知来源';
        modalData.cost = record.details.cost || 0;
        break;
      case 'weight':
        modalData.weight = record.details.weight || 0;
        modalData.growthStage = record.details.growthStage || '生长期';
        break;
      case 'sale':
        modalData.weight = record.details.weight || 0;
        modalData.price = record.details.price || 0;
        modalData.totalIncome = record.details.totalIncome || 0;
        break;
      }
      
      this.setData({
        currentRecord: modalData,
        showRecordDetail: true
      });
    }
  },

  // 关闭生产记录详情弹窗
  onCloseRecordDetail: function() {
    this.setData({
      showRecordDetail: false,
      currentRecord: null
    });
  },

  // ==================== 实时同步相关方法 ====================

  // 初始化实时同步
  initRealtimeSync: function() {
    try {
      console.log('[Production] 初始化实时同步');

      // 订阅健康记录实时更新
      QuickSetup.setupHealthRecordsSync(this, (data) => {
        console.log('[Production] 收到健康记录实时更新:', data);
        this.handleHealthRecordsUpdate(data);
      });

      // 订阅生产记录实时更新
      QuickSetup.setupProductionRecordsSync(this, (data) => {
        console.log('[Production] 收到生产记录实时更新:', data);
        this.handleProductionRecordsUpdate(data);
      });

      // 监听特定记录的变更
      this.setupRecordWatchers();

      this.setData({
        realtimeEnabled: true,
        lastSyncTime: new Date().toLocaleTimeString()
      });

    } catch (error) {
      console.error('[Production] 实时同步初始化失败:', error);
      this.setData({
        realtimeEnabled: false
      });
    }
  },

  // 设置记录监听器
  setupRecordWatchers: function() {
    // 监听健康记录列表变更
    QuickSetup.setupListWatch(this, 'health_records', (changeInfo) => {
      console.log('[Production] 健康记录列表变更:', changeInfo);
      this.handleRecordListChange('health', changeInfo);
    });

    // 监听生产记录列表变更
    QuickSetup.setupListWatch(this, 'production_records', (changeInfo) => {
      console.log('[Production] 生产记录列表变更:', changeInfo);
      this.handleRecordListChange('production', changeInfo);
    });
  },

  // 处理健康记录更新
  handleHealthRecordsUpdate: function(data) {
    if (Array.isArray(data)) {
      this.setData({
        healthRecords: data,
        lastSyncTime: new Date().toLocaleTimeString()
      });

      // 重新生成报告数据
      this.generateHealthReportFromRecords();

      // 显示更新提示
      this.showSyncNotification('健康记录已更新');
    }
  },

  // 处理生产记录更新
  handleProductionRecordsUpdate: function(data) {
    if (Array.isArray(data)) {
      // 更新生产相关数据
      this.updateProductionData(data);

      this.setData({
        lastSyncTime: new Date().toLocaleTimeString()
      });

      this.showSyncNotification('生产记录已更新');
    }
  },

  // 处理记录列表变更
  handleRecordListChange: function(type, changeInfo) {
    const { changeType, currentValue } = changeInfo;

    if (changeType === 'create') {
      this.showSyncNotification(`新增${type === 'health' ? '健康' : '生产'}记录`);
      // 刷新对应的数据
      if (type === 'health') {
        this.loadHealthRecords();
      } else {
        this.loadProductionData();
      }
    } else if (changeType === 'delete') {
      this.showSyncNotification(`删除${type === 'health' ? '健康' : '生产'}记录`);
      // 刷新对应的数据
      if (type === 'health') {
        this.loadHealthRecords();
      } else {
        this.loadProductionData();
      }
    }
  },

  // 显示同步通知
  showSyncNotification: function(message) {
    // 使用轻量级提示，避免打扰用户
    console.log('[Production] 同步通知:', message);

    // 可以在页面上显示一个小的指示器
    this.setData({
      syncNotification: message
    });

    // 3秒后清除通知
    setTimeout(() => {
      this.setData({
        syncNotification: ''
      });
    }, 3000);
  },

  // 更新生产数据
  updateProductionData: function(data) {
    // 处理生产记录数据更新
    if (data && Array.isArray(data)) {
      const recordsWithColorIndex = data.map(record => {
        const colorIndex = this.calculateBatchColorIndex(record.batch);
        return {
          ...record,
          batchColorIndex: colorIndex
        };
      });

      this.setData({
        productionRecords: recordsWithColorIndex
      });

      this.filterRecordList();
    }
  },

  // 加载生产数据
  loadProductionData: function() {
    // 重新初始化生产数据
    this.initializeProductionData();
  },

  // 页面卸载时的清理工作
  onUnload: function () {
    console.log('[Production] onUnload 页面卸载');

    // 调用 RealtimeSyncMixin 的 onUnload 方法
    if (RealtimeSyncMixin.onUnload) {
      RealtimeSyncMixin.onUnload.call(this);
    }

    // 清理实时同步
    this.cleanupRealtimeSync();
  },

  // 清理实时同步
  cleanupRealtimeSync: function() {
    try {
      // 这里可以添加清理实时同步的逻辑
      // 由于使用了混入对象，清理工作会自动处理
      console.log('[Production] 实时同步已清理');
    } catch (error) {
      console.error('[Production] 清理实时同步失败:', error);
    }
  },

  // 合并 RealtimeSyncMixin 的方法
  ...RealtimeSyncMixin.methods
});