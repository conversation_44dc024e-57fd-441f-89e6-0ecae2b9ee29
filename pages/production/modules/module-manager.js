/**
 * 生产页面 - 模块管理器
 * Production Page - Module Manager
 */

const HealthModule = require('./health-module.js');
const MaterialModule = require('./material-module.js');
const KnowledgeModule = require('./knowledge-module.js');
const ProductionModule = require('./production-module.js');

class ModuleManager {
  constructor(page) {
    this.page = page;
    this.modules = new Map();
    this.currentModule = null;
    this.isInitialized = false;
  }

  /**
   * 初始化模块管理器
   */
  initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('[ModuleManager] 初始化模块管理器');

      // 创建各个模块实例
      this.modules.set('production', new ProductionModule(this.page));
      this.modules.set('health', new HealthModule(this.page));
      this.modules.set('material', new MaterialModule(this.page));
      this.modules.set('knowledge', new KnowledgeModule(this.page));

      this.isInitialized = true;
      console.log('[ModuleManager] 模块管理器初始化完成');

    } catch (error) {
      console.error('[ModuleManager] 模块管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 切换模块
   */
  switchModule(moduleKey) {
    if (!this.isInitialized) {
      this.initialize();
    }

    const module = this.modules.get(moduleKey);
    if (!module) {
      console.error(`[ModuleManager] 模块不存在: ${moduleKey}`);
      return;
    }

    try {
      console.log(`[ModuleManager] 切换到模块: ${moduleKey}`);

      // 如果有当前模块，先执行清理
      if (this.currentModule && this.currentModule !== module) {
        this.currentModule.cleanup && this.currentModule.cleanup();
      }

      // 设置当前模块
      this.currentModule = module;

      // 初始化新模块
      if (module.initialize) {
        module.initialize();
      }

      return module;

    } catch (error) {
      console.error(`[ModuleManager] 切换模块失败: ${moduleKey}`, error);
      throw error;
    }
  }

  /**
   * 获取模块
   */
  getModule(moduleKey) {
    return this.modules.get(moduleKey);
  }

  /**
   * 获取当前模块
   */
  getCurrentModule() {
    return this.currentModule;
  }

  /**
   * 模块是否存在
   */
  hasModule(moduleKey) {
    return this.modules.has(moduleKey);
  }

  /**
   * 获取所有模块键名
   */
  getModuleKeys() {
    return Array.from(this.modules.keys());
  }

  /**
   * 执行模块方法
   */
  executeModuleMethod(moduleKey, methodName, ...args) {
    const module = this.modules.get(moduleKey);
    if (!module) {
      console.error(`[ModuleManager] 模块不存在: ${moduleKey}`);
      return null;
    }

    if (typeof module[methodName] !== 'function') {
      console.error(`[ModuleManager] 方法不存在: ${moduleKey}.${methodName}`);
      return null;
    }

    try {
      return module[methodName](...args);
    } catch (error) {
      console.error(`[ModuleManager] 执行方法失败: ${moduleKey}.${methodName}`, error);
      throw error;
    }
  }

  /**
   * 广播事件到所有模块
   */
  broadcastEvent(eventName, eventData) {
    console.log(`[ModuleManager] 广播事件: ${eventName}`, eventData);

    for (const [moduleKey, module] of this.modules.entries()) {
      try {
        if (module.onEvent && typeof module.onEvent === 'function') {
          module.onEvent(eventName, eventData);
        }
      } catch (error) {
        console.error(`[ModuleManager] 模块事件处理失败: ${moduleKey}`, error);
      }
    }
  }

  /**
   * 获取模块状态
   */
  getModuleStatus() {
    const status = {};

    for (const [moduleKey, module] of this.modules.entries()) {
      status[moduleKey] = {
        initialized: !!module.isInitialized,
        active: this.currentModule === module,
        hasData: module.getData ? Object.keys(module.getData()).length > 0 : false
      };
    }

    return {
      managerInitialized: this.isInitialized,
      currentModule: this.currentModule ? this.getModuleKeyByInstance(this.currentModule) : null,
      modules: status
    };
  }

  /**
   * 根据模块实例获取模块键名
   */
  getModuleKeyByInstance(moduleInstance) {
    for (const [key, module] of this.modules.entries()) {
      if (module === moduleInstance) {
        return key;
      }
    }
    return null;
  }

  /**
   * 预加载模块数据
   */
  async preloadModuleData(moduleKeys = []) {
    const targetKeys = moduleKeys.length > 0 ? moduleKeys : this.getModuleKeys();

    console.log('[ModuleManager] 开始预加载模块数据:', targetKeys);

    const promises = targetKeys.map(async (moduleKey) => {
      try {
        const module = this.modules.get(moduleKey);
        if (module && module.preload && typeof module.preload === 'function') {
          await module.preload();
          console.log(`[ModuleManager] 模块 ${moduleKey} 预加载完成`);
        }
      } catch (error) {
        console.error(`[ModuleManager] 模块 ${moduleKey} 预加载失败:`, error);
      }
    });

    await Promise.allSettled(promises);
    console.log('[ModuleManager] 模块数据预加载完成');
  }

  /**
   * 刷新模块数据
   */
  async refreshModuleData(moduleKey) {
    const module = this.modules.get(moduleKey);
    if (!module) {
      console.error(`[ModuleManager] 模块不存在: ${moduleKey}`);
      return;
    }

    try {
      console.log(`[ModuleManager] 刷新模块数据: ${moduleKey}`);

      if (module.refresh && typeof module.refresh === 'function') {
        await module.refresh();
      } else if (module.initialize && typeof module.initialize === 'function') {
        await module.initialize();
      }

      console.log(`[ModuleManager] 模块 ${moduleKey} 数据刷新完成`);

    } catch (error) {
      console.error(`[ModuleManager] 模块 ${moduleKey} 数据刷新失败:`, error);
      throw error;
    }
  }

  /**
   * 刷新所有模块数据
   */
  async refreshAllModules() {
    console.log('[ModuleManager] 刷新所有模块数据');

    const promises = this.getModuleKeys().map(moduleKey => 
      this.refreshModuleData(moduleKey).catch(error => {
        console.error(`刷新模块 ${moduleKey} 失败:`, error);
      })
    );

    await Promise.allSettled(promises);
    console.log('[ModuleManager] 所有模块数据刷新完成');
  }

  /**
   * 获取模块数据摘要
   */
  getModuleDataSummary() {
    const summary = {};

    for (const [moduleKey, module] of this.modules.entries()) {
      try {
        if (module.getData && typeof module.getData === 'function') {
          const data = module.getData();
          summary[moduleKey] = {
            hasData: !!data && Object.keys(data).length > 0,
            dataKeys: data ? Object.keys(data) : [],
            dataSize: data ? JSON.stringify(data).length : 0
          };
        } else {
          summary[moduleKey] = {
            hasData: false,
            dataKeys: [],
            dataSize: 0
          };
        }
      } catch (error) {
        console.error(`[ModuleManager] 获取模块 ${moduleKey} 数据摘要失败:`, error);
        summary[moduleKey] = {
          hasData: false,
          dataKeys: [],
          dataSize: 0,
          error: error.message
        };
      }
    }

    return summary;
  }

  /**
   * 清理模块资源
   */
  cleanup() {
    console.log('[ModuleManager] 开始清理模块资源');

    for (const [moduleKey, module] of this.modules.entries()) {
      try {
        if (module.destroy && typeof module.destroy === 'function') {
          module.destroy();
          console.log(`[ModuleManager] 模块 ${moduleKey} 已清理`);
        }
      } catch (error) {
        console.error(`[ModuleManager] 清理模块 ${moduleKey} 失败:`, error);
      }
    }

    this.modules.clear();
    this.currentModule = null;
    this.isInitialized = false;

    console.log('[ModuleManager] 模块资源清理完成');
  }

  /**
   * 销毁模块管理器
   */
  destroy() {
    this.cleanup();
    console.log('[ModuleManager] 模块管理器已销毁');
  }
}

module.exports = ModuleManager;
