/**
 * 生产页面 - 知识库模块
 * Production Page - Knowledge Base Module
 */

class KnowledgeModule {
  constructor(page) {
    this.page = page;
    this.categories = [];
    this.articles = [];
    this.filteredArticles = [];
    this.searchKeyword = '';
    this.activeCategory = 'all';
  }

  /**
   * 初始化知识库模块
   */
  initialize() {
    this.loadCategories();
    this.loadArticles();
  }

  /**
   * 加载分类
   */
  loadCategories() {
    this.categories = [
      { id: 'all', name: '全部', count: 0 },
      { id: 'breeding', name: '养殖技术', count: 0 },
      { id: 'disease', name: '疾病防治', count: 0 },
      { id: 'nutrition', name: '营养饲料', count: 0 },
      { id: 'management', name: '管理经验', count: 0 },
      { id: 'market', name: '市场信息', count: 0 }
    ];

    this.page.setData({
      categories: this.categories
    });
  }

  /**
   * 加载文章
   */
  async loadArticles() {
    try {
      console.log('[KnowledgeModule] 开始加载知识库文章');
      
      this.page.setData({ loading: true });

      // 模拟API调用
      const response = await this.fetchArticles();
      
      if (response && response.data) {
        this.articles = response.data;
        this.updateCategoryCounts();
        this.filterArticles();
        
        this.page.setData({
          articles: this.articles,
          loading: false
        });
        
        console.log('[KnowledgeModule] 知识库文章加载完成，文章数:', this.articles.length);
      }
    } catch (error) {
      console.error('[KnowledgeModule] 加载知识库文章失败:', error);
      this.page.setData({ loading: false });
      
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  }

  /**
   * 获取文章数据
   */
  async fetchArticles() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: [
            {
              id: 1,
              title: '雏鹅饲养管理要点',
              summary: '雏鹅是指0-4周龄的小鹅，这个阶段的饲养管理直接影响到鹅的生长发育和成活率...',
              category: 'breeding',
              categoryName: '养殖技术',
              author: '养殖专家',
              publishTime: '2024-01-15',
              readCount: 1250,
              likeCount: 89,
              tags: ['雏鹅', '饲养', '管理'],
              thumbnail: '/images/knowledge/chick-care.jpg'
            },
            {
              id: 2,
              title: '鹅常见疾病的预防与治疗',
              summary: '鹅在养殖过程中容易患上各种疾病，了解常见疾病的症状、预防和治疗方法对养殖成功至关重要...',
              category: 'disease',
              categoryName: '疾病防治',
              author: '兽医师',
              publishTime: '2024-01-14',
              readCount: 980,
              likeCount: 67,
              tags: ['疾病', '预防', '治疗'],
              thumbnail: '/images/knowledge/disease-prevention.jpg'
            },
            {
              id: 3,
              title: '鹅的营养需求与饲料配制',
              summary: '合理的营养搭配是鹅健康成长的基础，不同生长阶段的鹅对营养的需求也不同...',
              category: 'nutrition',
              categoryName: '营养饲料',
              author: '营养师',
              publishTime: '2024-01-13',
              readCount: 756,
              likeCount: 45,
              tags: ['营养', '饲料', '配制'],
              thumbnail: '/images/knowledge/nutrition.jpg'
            },
            {
              id: 4,
              title: '现代化鹅场管理经验分享',
              summary: '现代化鹅场管理需要科学的方法和先进的理念，本文分享一些实用的管理经验...',
              category: 'management',
              categoryName: '管理经验',
              author: '场长',
              publishTime: '2024-01-12',
              readCount: 634,
              likeCount: 38,
              tags: ['管理', '经验', '现代化'],
              thumbnail: '/images/knowledge/management.jpg'
            },
            {
              id: 5,
              title: '2024年鹅肉市场行情分析',
              summary: '随着消费者对健康食品需求的增加，鹅肉市场呈现出良好的发展前景...',
              category: 'market',
              categoryName: '市场信息',
              author: '市场分析师',
              publishTime: '2024-01-11',
              readCount: 892,
              likeCount: 56,
              tags: ['市场', '行情', '分析'],
              thumbnail: '/images/knowledge/market.jpg'
            },
            {
              id: 6,
              title: '鹅舍建设与环境控制',
              summary: '良好的鹅舍环境是养鹅成功的重要因素，包括温度、湿度、通风、光照等方面的控制...',
              category: 'breeding',
              categoryName: '养殖技术',
              author: '建筑师',
              publishTime: '2024-01-10',
              readCount: 567,
              likeCount: 34,
              tags: ['鹅舍', '建设', '环境'],
              thumbnail: '/images/knowledge/housing.jpg'
            },
            {
              id: 7,
              title: '鹅的繁殖技术与孵化管理',
              summary: '掌握鹅的繁殖技术和孵化管理是扩大养殖规模的关键，本文详细介绍相关技术要点...',
              category: 'breeding',
              categoryName: '养殖技术',
              author: '繁殖专家',
              publishTime: '2024-01-09',
              readCount: 723,
              likeCount: 42,
              tags: ['繁殖', '孵化', '技术'],
              thumbnail: '/images/knowledge/breeding.jpg'
            },
            {
              id: 8,
              title: '有机鹅养殖认证指南',
              summary: '有机鹅养殖认证可以提高产品附加值，本文介绍有机认证的要求和申请流程...',
              category: 'management',
              categoryName: '管理经验',
              author: '认证专家',
              publishTime: '2024-01-08',
              readCount: 445,
              likeCount: 28,
              tags: ['有机', '认证', '指南'],
              thumbnail: '/images/knowledge/organic.jpg'
            }
          ]
        });
      }, 600);
    });
  }

  /**
   * 更新分类计数
   */
  updateCategoryCounts() {
    const categoryCounts = {};
    
    this.articles.forEach(article => {
      categoryCounts[article.category] = (categoryCounts[article.category] || 0) + 1;
    });

    this.categories = this.categories.map(category => ({
      ...category,
      count: category.id === 'all' ? this.articles.length : (categoryCounts[category.id] || 0)
    }));

    this.page.setData({
      categories: this.categories
    });
  }

  /**
   * 分类切换
   */
  onCategoryChange(e) {
    const categoryId = e.currentTarget.dataset.category;
    
    this.activeCategory = categoryId;
    this.page.setData({
      activeCategory: categoryId
    });
    
    this.filterArticles();
  }

  /**
   * 搜索文章
   */
  onSearchInput(e) {
    this.searchKeyword = e.detail.value;
    this.page.setData({
      searchKeyword: this.searchKeyword
    });
    
    // 防抖搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.filterArticles();
    }, 300);
  }

  /**
   * 过滤文章
   */
  filterArticles() {
    let filteredArticles = this.articles;

    // 按分类过滤
    if (this.activeCategory !== 'all') {
      filteredArticles = filteredArticles.filter(article => 
        article.category === this.activeCategory
      );
    }

    // 按关键词搜索
    if (this.searchKeyword && this.searchKeyword.trim()) {
      const keyword = this.searchKeyword.trim().toLowerCase();
      filteredArticles = filteredArticles.filter(article => 
        article.title.toLowerCase().includes(keyword) ||
        article.summary.toLowerCase().includes(keyword) ||
        article.tags.some(tag => tag.toLowerCase().includes(keyword))
      );
    }

    this.filteredArticles = filteredArticles;
    this.page.setData({
      filteredArticles: this.filteredArticles
    });

    console.log('[KnowledgeModule] 文章过滤完成，结果数:', this.filteredArticles.length);
  }

  /**
   * 文章点击
   */
  onArticleTap(e) {
    const articleId = e.currentTarget.dataset.id;
    const article = this.articles.find(item => item.id === articleId);
    
    if (article) {
      // 增加阅读计数
      article.readCount++;
      
      // 跳转到文章详情页
      wx.navigateTo({
        url: `/pages/knowledge/detail?id=${articleId}`
      });
    }
  }

  /**
   * 文章收藏
   */
  onArticleLike(e) {
    e.stopPropagation(); // 阻止事件冒泡
    
    const articleId = e.currentTarget.dataset.id;
    const article = this.articles.find(item => item.id === articleId);
    
    if (article) {
      article.likeCount++;
      article.isLiked = true;
      
      this.filterArticles(); // 重新过滤以更新显示
      
      wx.showToast({
        title: '收藏成功',
        icon: 'success'
      });
    }
  }

  /**
   * 文章分享
   */
  onArticleShare(e) {
    e.stopPropagation();
    
    const articleId = e.currentTarget.dataset.id;
    const article = this.articles.find(item => item.id === articleId);
    
    if (article) {
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
    }
  }

  /**
   * 清空搜索
   */
  onClearSearch() {
    this.searchKeyword = '';
    this.page.setData({
      searchKeyword: ''
    });
    
    this.filterArticles();
  }

  /**
   * 刷新文章
   */
  onRefreshArticles() {
    this.loadArticles();
  }

  /**
   * 获取热门文章
   */
  getHotArticles(limit = 5) {
    return this.articles
      .sort((a, b) => b.readCount - a.readCount)
      .slice(0, limit);
  }

  /**
   * 获取最新文章
   */
  getLatestArticles(limit = 5) {
    return this.articles
      .sort((a, b) => new Date(b.publishTime) - new Date(a.publishTime))
      .slice(0, limit);
  }

  /**
   * 获取推荐文章
   */
  getRecommendedArticles(currentArticleId, limit = 3) {
    return this.articles
      .filter(article => article.id !== currentArticleId)
      .sort((a, b) => b.likeCount - a.likeCount)
      .slice(0, limit);
  }

  /**
   * 获取知识库模块数据
   */
  getData() {
    return {
      categories: this.categories,
      articles: this.articles,
      filteredArticles: this.filteredArticles,
      searchKeyword: this.searchKeyword,
      activeCategory: this.activeCategory
    };
  }

  /**
   * 销毁模块
   */
  destroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    this.categories = [];
    this.articles = [];
    this.filteredArticles = [];
    console.log('[KnowledgeModule] 模块已销毁');
  }
}

module.exports = KnowledgeModule;
