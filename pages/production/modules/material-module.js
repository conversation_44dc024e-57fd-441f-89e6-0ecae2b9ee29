/**
 * 生产页面 - 物料管理模块
 * Production Page - Material Management Module
 */

class MaterialModule {
  constructor(page) {
    this.page = page;
    this.materialList = [];
    this.materialStats = {
      feedCount: 0,
      medicineCount: 0,
      lowStockItems: 0
    };
    this.materialTabs = [
      { id: 'all', name: '全部' },
      { id: 'feed', name: '饲料' },
      { id: 'medicine', name: '药品' },
      { id: 'other', name: '其他' }
    ];
  }

  /**
   * 初始化物料模块
   */
  initialize() {
    this.initializeMaterialData();
    this.calculateMaterialStats();
    this.filterMaterialList();
  }

  /**
   * 初始化物料数据
   */
  initializeMaterialData() {
    this.materialList = [
      {
        id: 1,
        name: '雏鹅专用饲料',
        spec: '25kg/袋',
        stock: 120,
        status: 'normal',
        category: 'feed',
        unit: '袋',
        price: 85.00,
        supplier: '优质饲料厂',
        lastUpdate: '2024-01-15'
      },
      {
        id: 2,
        name: '育肥期饲料',
        spec: '25kg/袋',
        stock: 45,
        status: 'warning',
        category: 'feed',
        unit: '袋',
        price: 92.00,
        supplier: '优质饲料厂',
        lastUpdate: '2024-01-14'
      },
      {
        id: 3,
        name: '成鹅饲料',
        spec: '25kg/袋',
        stock: 80,
        status: 'normal',
        category: 'feed',
        unit: '袋',
        price: 88.00,
        supplier: '优质饲料厂',
        lastUpdate: '2024-01-13'
      },
      {
        id: 4,
        name: '维生素C',
        spec: '500g/瓶',
        stock: 15,
        status: 'low',
        category: 'medicine',
        unit: '瓶',
        price: 45.00,
        supplier: '兽药公司',
        lastUpdate: '2024-01-12'
      },
      {
        id: 5,
        name: '抗生素',
        spec: '100ml/瓶',
        stock: 25,
        status: 'normal',
        category: 'medicine',
        unit: '瓶',
        price: 120.00,
        supplier: '兽药公司',
        lastUpdate: '2024-01-11'
      },
      {
        id: 6,
        name: '消毒液',
        spec: '5L/桶',
        stock: 8,
        status: 'low',
        category: 'other',
        unit: '桶',
        price: 35.00,
        supplier: '清洁用品厂',
        lastUpdate: '2024-01-10'
      },
      {
        id: 7,
        name: '疫苗',
        spec: '1000羽份/盒',
        stock: 5,
        status: 'low',
        category: 'medicine',
        unit: '盒',
        price: 280.00,
        supplier: '生物制品厂',
        lastUpdate: '2024-01-09'
      },
      {
        id: 8,
        name: '饮水器',
        spec: '自动饮水器',
        stock: 50,
        status: 'normal',
        category: 'other',
        unit: '个',
        price: 25.00,
        supplier: '设备厂',
        lastUpdate: '2024-01-08'
      }
    ];

    this.page.setData({
      materialList: this.materialList
    });
  }

  /**
   * 计算物料统计
   */
  calculateMaterialStats() {
    const feedCount = this.materialList.filter(item => item.category === 'feed').length;
    const medicineCount = this.materialList.filter(item => item.category === 'medicine').length;
    const lowStockItems = this.materialList.filter(item => item.status === 'low' || item.status === 'warning').length;

    this.materialStats = {
      feedCount,
      medicineCount,
      lowStockItems
    };

    this.page.setData({
      materialStats: this.materialStats
    });
  }

  /**
   * 切换物料标签页
   */
  onMaterialTabChange(e) {
    const tabId = e.currentTarget.dataset.tab;
    
    this.page.setData({
      activeMaterialTab: tabId
    });
    
    this.filterMaterialList();
  }

  /**
   * 过滤物料列表
   */
  filterMaterialList() {
    const activeTab = this.page.data.activeMaterialTab || 'all';
    let filteredList = this.materialList;

    if (activeTab !== 'all') {
      filteredList = this.materialList.filter(item => item.category === activeTab);
    }

    this.page.setData({
      filteredMaterialList: filteredList
    });
  }

  /**
   * 物料项点击事件
   */
  onMaterialItemTap(e) {
    const itemId = e.currentTarget.dataset.id;
    const item = this.materialList.find(material => material.id === itemId);
    
    if (item) {
      this.showMaterialDetail(item);
    }
  }

  /**
   * 显示物料详情
   */
  showMaterialDetail(item) {
    wx.showModal({
      title: item.name,
      content: `规格: ${item.spec}\n库存: ${item.stock}${item.unit}\n价格: ¥${item.price}\n供应商: ${item.supplier}\n更新时间: ${item.lastUpdate}`,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '编辑',
      success: (res) => {
        if (res.confirm) {
          this.editMaterial(item);
        }
      }
    });
  }

  /**
   * 编辑物料
   */
  editMaterial(item) {
    // 这里可以跳转到物料编辑页面
    wx.navigateTo({
      url: `/pages/material/edit?id=${item.id}`
    });
  }

  /**
   * 添加物料
   */
  onAddMaterial() {
    wx.navigateTo({
      url: '/pages/material/add'
    });
  }

  /**
   * 库存预警处理
   */
  onStockWarning() {
    const lowStockItems = this.materialList.filter(item => 
      item.status === 'low' || item.status === 'warning'
    );

    if (lowStockItems.length === 0) {
      wx.showToast({
        title: '暂无库存预警',
        icon: 'success'
      });
      return;
    }

    const warningList = lowStockItems.map(item => 
      `${item.name}: ${item.stock}${item.unit}`
    ).join('\n');

    wx.showModal({
      title: `库存预警 (${lowStockItems.length}项)`,
      content: warningList,
      showCancel: true,
      cancelText: '知道了',
      confirmText: '去采购',
      success: (res) => {
        if (res.confirm) {
          this.goPurchase(lowStockItems);
        }
      }
    });
  }

  /**
   * 去采购
   */
  goPurchase(items) {
    // 这里可以跳转到采购页面
    const itemIds = items.map(item => item.id).join(',');
    wx.navigateTo({
      url: `/pages/purchase/create?items=${itemIds}`
    });
  }

  /**
   * 搜索物料
   */
  onSearchMaterial(e) {
    const keyword = e.detail.value.trim();
    
    if (!keyword) {
      this.filterMaterialList();
      return;
    }

    const filteredList = this.materialList.filter(item => 
      item.name.includes(keyword) || 
      item.spec.includes(keyword) ||
      item.supplier.includes(keyword)
    );

    this.page.setData({
      filteredMaterialList: filteredList
    });
  }

  /**
   * 获取物料状态样式
   */
  getMaterialStatusClass(status) {
    const statusMap = {
      'normal': 'status-normal',
      'warning': 'status-warning',
      'low': 'status-low',
      'out': 'status-out'
    };
    return statusMap[status] || 'status-normal';
  }

  /**
   * 获取物料状态文本
   */
  getMaterialStatusText(status) {
    const statusMap = {
      'normal': '正常',
      'warning': '预警',
      'low': '库存不足',
      'out': '缺货'
    };
    return statusMap[status] || '未知';
  }

  /**
   * 批量操作
   */
  onBatchOperation() {
    const actions = ['批量入库', '批量出库', '库存盘点', '导出数据'];
    
    wx.showActionSheet({
      itemList: actions,
      success: (res) => {
        const action = actions[res.tapIndex];
        this.handleBatchOperation(action);
      }
    });
  }

  /**
   * 处理批量操作
   */
  handleBatchOperation(action) {
    switch (action) {
      case '批量入库':
        wx.navigateTo({
          url: '/pages/material/batch-in'
        });
        break;
      case '批量出库':
        wx.navigateTo({
          url: '/pages/material/batch-out'
        });
        break;
      case '库存盘点':
        wx.navigateTo({
          url: '/pages/material/inventory'
        });
        break;
      case '导出数据':
        this.exportMaterialData();
        break;
    }
  }

  /**
   * 导出物料数据
   */
  exportMaterialData() {
    wx.showLoading({
      title: '导出中...'
    });

    // 模拟导出过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      });
    }, 2000);
  }

  /**
   * 获取物料模块数据
   */
  getData() {
    return {
      materialList: this.materialList,
      materialStats: this.materialStats,
      materialTabs: this.materialTabs
    };
  }

  /**
   * 销毁模块
   */
  destroy() {
    this.materialList = [];
    this.materialStats = null;
    console.log('[MaterialModule] 模块已销毁');
  }
}

module.exports = MaterialModule;
