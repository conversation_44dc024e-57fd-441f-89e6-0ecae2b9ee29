/**
 * 增强物料管理模块 - 第三阶段重构版本
 * Enhanced Material Management Module - Phase 3 Refactored Version
 * 
 * 功能包括：
 * - 物料库存管理
 * - 入库出库记录
 * - 库存预警
 * - 采购管理
 * - 成本核算
 * - 智能补货建议
 */

const { ultimateAPIClient } = require('../../../utils/ultimate-api-client');
const { enhancedDataIsolation } = require('../../../utils/enhanced-data-isolation');
const { permissionManager } = require('../../../utils/enhanced-permission-manager');

/**
 * 增强物料管理模块类
 */
class EnhancedMaterialModule {
  constructor(page) {
    this.page = page;
    this.moduleId = 'material';
    this.permissions = [];
    
    // 模块数据结构
    this.data = {
      // 物料统计数据
      materialStats: {
        feedCount: 0,
        medicineCount: 0,
        equipmentCount: 0,
        lowStockItems: 0,
        totalValue: 0,
        monthlyConsumption: 0,
        turnoverRate: 0,
        avgCostPerUnit: 0
      },

      // 物料分类标签
      activeMaterialTab: 'all',
      materialTabs: [
        { id: 'all', name: '全部', icon: 'all', permission: 'MATERIAL_VIEW' },
        { id: 'feed', name: '饲料', icon: 'feed', permission: 'MATERIAL_VIEW' },
        { id: 'medicine', name: '药品', icon: 'medicine', permission: 'MATERIAL_VIEW' },
        { id: 'equipment', name: '设备', icon: 'equipment', permission: 'MATERIAL_VIEW' },
        { id: 'consumables', name: '耗材', icon: 'consumables', permission: 'MATERIAL_VIEW' },
        { id: 'other', name: '其他', icon: 'other', permission: 'MATERIAL_VIEW' }
      ],

      // 物料列表
      materialList: [],
      filteredMaterialList: [],

      // 库存预警
      lowStockAlerts: [],
      expiryAlerts: [],
      overStockAlerts: [],

      // 出入库记录
      stockMovements: [],
      pendingMovements: [],

      // 采购管理
      purchaseOrders: [],
      suppliers: [],
      purchaseRequests: [],

      // 智能分析
      consumptionAnalysis: [],
      restockSuggestions: [],
      costAnalysis: {},

      // 搜索和过滤
      searchKeyword: '',
      sortBy: 'name',
      sortOrder: 'asc',
      filterStatus: 'all',

      // 加载状态
      loading: false,
      refreshing: false,
      hasMore: true,
      pageSize: 20,
      currentPage: 1
    };

    // 库存预警阈值配置
    this.alertThresholds = {
      lowStock: 10,        // 低库存预警
      criticalStock: 5,    // 严重缺货预警
      overStock: 0.8,      // 超储预警（占最大库存的比例）
      expiryDays: 30,      // 过期预警天数
      slowMovingDays: 90   // 滞销预警天数
    };

    // 缓存配置
    this.cacheConfig = {
      materialsCacheTTL: 5 * 60 * 1000,    // 5分钟
      movementsCacheTTL: 3 * 60 * 1000,    // 3分钟
      analysisCacheTTL: 15 * 60 * 1000     // 15分钟
    };

    // 事件监听器
    this.eventListeners = new Map();
  }

  /**
   * 初始化物料模块
   */
  async init() {
    try {
      // 1. 检查用户权限
      await this.checkPermissions();
      
      // 2. 初始化数据
      await this.initializeData();
      
      // 3. 设置事件监听
      this.setupEventListeners();
      
      // 4. 加载初始数据
      await this.loadInitialData();
      
      // 5. 启动定时任务
      this.startPeriodicTasks();
      
      console.log('[EnhancedMaterialModule] 初始化完成');
    } catch (error) {
      console.error('[EnhancedMaterialModule] 初始化失败:', error);
      this.handleInitError(error);
    }
  }

  /**
   * 检查用户权限
   */
  async checkPermissions() {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('用户未登录');
    }

    // 检查模块访问权限
    const hasMaterialAccess = await permissionManager.checkPermission(user, 'MATERIAL_VIEW');
    if (!hasMaterialAccess) {
      throw new Error('无权限访问物料模块');
    }

    // 获取用户所有物料相关权限
    const materialPermissions = [
      'MATERIAL_VIEW', 'MATERIAL_CREATE', 'MATERIAL_EDIT', 'MATERIAL_DELETE',
      'MATERIAL_INVENTORY', 'MATERIAL_PURCHASE', 'MATERIAL_EXPORT',
      'MATERIAL_STOCK_MOVEMENT', 'MATERIAL_ALERT_MANAGE',
      'MATERIAL_COST_ANALYSIS', 'MATERIAL_SUPPLIER_MANAGE'
    ];

    this.permissions = [];
    for (const permission of materialPermissions) {
      const hasPermission = await permissionManager.checkPermission(user, permission);
      if (hasPermission) {
        this.permissions.push(permission);
      }
    }

    // 根据权限过滤标签页
    this.filterTabsByPermissions();
  }

  /**
   * 根据权限过滤标签页
   */
  filterTabsByPermissions() {
    const filteredTabs = this.data.materialTabs.filter(tab => {
      return !tab.permission || this.permissions.includes(tab.permission);
    });

    this.data.materialTabs = filteredTabs;
  }

  /**
   * 初始化数据
   */
  async initializeData() {
    // 设置初始数据到页面
    this.page.setData({
      ...this.data,
      [`${this.moduleId}_initialized`]: true
    });
  }

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 监听物料数据变更
    this.addEventListener('materialListUpdate', this.handleMaterialListUpdate.bind(this));
    
    // 监听库存变更
    this.addEventListener('stockMovementUpdate', this.handleStockMovementUpdate.bind(this));
    
    // 监听预警更新
    this.addEventListener('alertUpdate', this.handleAlertUpdate.bind(this));
    
    // 监听采购订单更新
    this.addEventListener('purchaseOrderUpdate', this.handlePurchaseOrderUpdate.bind(this));
  }

  /**
   * 加载初始数据
   */
  async loadInitialData() {
    const loadTasks = [
      this.loadMaterialList(),
      this.loadStockMovements(),
      this.loadSuppliers()
    ];

    // 根据权限加载额外数据
    if (this.permissions.includes('MATERIAL_ALERT_MANAGE')) {
      loadTasks.push(this.loadAlerts());
    }

    if (this.permissions.includes('MATERIAL_PURCHASE')) {
      loadTasks.push(this.loadPurchaseOrders());
    }

    if (this.permissions.includes('MATERIAL_COST_ANALYSIS')) {
      loadTasks.push(this.loadCostAnalysis());
    }

    await Promise.all(loadTasks);

    // 计算统计数据
    this.calculateMaterialStats();
    
    // 生成智能建议
    this.generateRestockSuggestions();
  }

  /**
   * 启动定时任务
   */
  startPeriodicTasks() {
    // 每5分钟检查一次库存预警
    this.alertCheckInterval = setInterval(() => {
      this.checkAllAlerts();
    }, 5 * 60 * 1000);

    // 每小时更新一次消耗分析
    this.analysisUpdateInterval = setInterval(() => {
      this.updateConsumptionAnalysis();
    }, 60 * 60 * 1000);
  }

  /**
   * 物料标签页切换
   */
  async onMaterialTabChange(e) {
    const tabId = e.currentTarget.dataset.tab;
    
    // 检查标签页权限
    const tab = this.data.materialTabs.find(t => t.id === tabId);
    if (tab && tab.permission && !this.permissions.includes(tab.permission)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }

    this.page.setData({
      activeMaterialTab: tabId
    });

    // 过滤物料列表
    await this.filterMaterialList();
  }

  /**
   * 加载物料列表（增强版）
   */
  async loadMaterialList(refresh = false) {
    if (this.data.loading && !refresh) return;

    try {
      this.page.setData({ 
        loading: true,
        refreshing: refresh 
      });

      const query = {
        limit: this.data.pageSize,
        skip: refresh ? 0 : (this.data.currentPage - 1) * this.data.pageSize,
        sort: this.buildSortQuery()
      };

      // 添加搜索条件
      if (this.data.searchKeyword) {
        query.name = { $regex: this.data.searchKeyword, $options: 'i' };
      }

      // 添加状态过滤
      if (this.data.filterStatus !== 'all') {
        query.status = this.data.filterStatus;
      }

      // 使用安全查询
      const response = await ultimateAPIClient.secureQuery('materials', query, {
        cache: !refresh,
        cacheTTL: this.cacheConfig.materialsCacheTTL
      });

      if (response.success && response.data) {
        const materials = this.processMaterialData(response.data);
        
        const newData = {
          loading: false,
          refreshing: false,
          hasMore: materials.length === this.data.pageSize
        };

        if (refresh) {
          newData.materialList = materials;
          newData.currentPage = 1;
        } else {
          newData.materialList = [...this.data.materialList, ...materials];
          newData.currentPage = this.data.currentPage + 1;
        }

        this.page.setData(newData);

        // 过滤物料列表
        await this.filterMaterialList();

        // 触发数据更新事件
        this.emitEvent('materialListUpdate', materials);

        // 检查库存预警
        await this.checkStockAlerts(materials);
      }

    } catch (error) {
      console.error('加载物料列表失败:', error);
      this.page.setData({ 
        loading: false, 
        refreshing: false 
      });
      
      // 回退到模拟数据
      this.loadMockMaterialData();
    }
  }

  /**
   * 构建排序查询
   */
  buildSortQuery() {
    const sortMap = {
      'name': { name: this.data.sortOrder === 'asc' ? 1 : -1 },
      'stock': { current_stock: this.data.sortOrder === 'asc' ? 1 : -1 },
      'value': { total_value: this.data.sortOrder === 'asc' ? 1 : -1 },
      'updated': { updated_at: this.data.sortOrder === 'asc' ? 1 : -1 }
    };

    return sortMap[this.data.sortBy] || { name: 1 };
  }

  /**
   * 处理物料数据（增强版）
   */
  processMaterialData(rawMaterials) {
    return rawMaterials.map(material => {
      const currentStock = material.current_stock || 0;
      const unitPrice = material.unit_price || 0;
      const totalValue = currentStock * unitPrice;

      return {
        id: material._id,
        name: material.name,
        category: material.category || 'other',
        specification: material.specification || '',
        unit: material.unit || '个',
        currentStock,
        minStock: material.min_stock || 10,
        maxStock: material.max_stock || 1000,
        safetyStock: material.safety_stock || 20,
        unitPrice,
        totalValue,
        avgCost: material.avg_cost || unitPrice,
        supplier: material.supplier || '',
        supplierContact: material.supplier_contact || '',
        location: material.storage_location || '',
        expiryDate: material.expiry_date,
        batchNumber: material.batch_number || '',
        status: this.calculateMaterialStatus(material),
        turnoverRate: this.calculateTurnoverRate(material),
        lastMovementDate: material.last_movement_date,
        lastUpdated: material.updated_at,
        description: material.description || '',
        images: material.images || [],
        tags: material.tags || [],
        qrCode: material.qr_code || '',
        barcode: material.barcode || ''
      };
    });
  }

  /**
   * 计算物料状态（增强版）
   */
  calculateMaterialStatus(material) {
    const stock = material.current_stock || 0;
    const minStock = material.min_stock || 10;
    const maxStock = material.max_stock || 1000;
    const safetyStock = material.safety_stock || 20;
    
    if (stock <= 0) return 'out_of_stock';
    if (stock <= this.alertThresholds.criticalStock) return 'critical';
    if (stock <= minStock) return 'warning';
    if (stock <= safetyStock) return 'low';
    if (stock >= maxStock * this.alertThresholds.overStock) return 'overstock';
    
    // 检查是否滞销
    const lastMovement = material.last_movement_date;
    if (lastMovement) {
      const daysSinceMovement = (Date.now() - new Date(lastMovement)) / (1000 * 60 * 60 * 24);
      if (daysSinceMovement > this.alertThresholds.slowMovingDays) {
        return 'slow_moving';
      }
    }
    
    return 'normal';
  }

  /**
   * 计算周转率
   */
  calculateTurnoverRate(material) {
    // 基于历史出库数据计算年周转率
    const annualConsumption = material.annual_consumption || 0;
    const avgStock = (material.current_stock + (material.min_stock || 0)) / 2;
    
    return avgStock > 0 ? (annualConsumption / avgStock).toFixed(2) : 0;
  }

  /**
   * 过滤物料列表（增强版）
   */
  async filterMaterialList() {
    const { materialList, activeMaterialTab, searchKeyword, filterStatus } = this.page.data;
    
    let filteredList = materialList;
    
    // 按分类过滤
    if (activeMaterialTab !== 'all') {
      filteredList = filteredList.filter(material => 
        material.category === activeMaterialTab
      );
    }

    // 按搜索关键词过滤
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      filteredList = filteredList.filter(material => 
        material.name.toLowerCase().includes(keyword) ||
        material.specification.toLowerCase().includes(keyword) ||
        material.supplier.toLowerCase().includes(keyword)
      );
    }

    // 按状态过滤
    if (filterStatus !== 'all') {
      filteredList = filteredList.filter(material => 
        material.status === filterStatus
      );
    }

    this.page.setData({
      filteredMaterialList: filteredList
    });

    // 重新计算统计数据
    this.calculateMaterialStats();
  }

  /**
   * 计算物料统计数据（增强版）
   */
  calculateMaterialStats() {
    const materialList = this.page.data.materialList || [];
    
    const stats = {
      feedCount: materialList.filter(m => m.category === 'feed').length,
      medicineCount: materialList.filter(m => m.category === 'medicine').length,
      equipmentCount: materialList.filter(m => m.category === 'equipment').length,
      lowStockItems: materialList.filter(m => 
        ['warning', 'critical', 'out_of_stock', 'low'].includes(m.status)
      ).length,
      totalValue: materialList.reduce((sum, m) => sum + m.totalValue, 0),
      monthlyConsumption: this.calculateMonthlyConsumption(materialList),
      turnoverRate: this.calculateAvgTurnoverRate(materialList),
      avgCostPerUnit: this.calculateAvgCostPerUnit(materialList)
    };

    this.page.setData({
      materialStats: stats
    });
  }

  /**
   * 计算月消耗量（增强版）
   */
  calculateMonthlyConsumption(materialList) {
    const movements = this.page.data.stockMovements || [];
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    const monthlyOutbound = movements
      .filter(m => m.type === 'outbound' && new Date(m.date) >= oneMonthAgo)
      .reduce((sum, m) => sum + (m.quantity * m.unitPrice), 0);

    return monthlyOutbound;
  }

  /**
   * 计算平均周转率
   */
  calculateAvgTurnoverRate(materialList) {
    const validTurnoverRates = materialList
      .map(m => parseFloat(m.turnoverRate))
      .filter(rate => !isNaN(rate) && rate > 0);

    return validTurnoverRates.length > 0 
      ? (validTurnoverRates.reduce((sum, rate) => sum + rate, 0) / validTurnoverRates.length).toFixed(2)
      : 0;
  }

  /**
   * 计算平均单位成本
   */
  calculateAvgCostPerUnit(materialList) {
    const totalValue = materialList.reduce((sum, m) => sum + m.totalValue, 0);
    const totalQuantity = materialList.reduce((sum, m) => sum + m.currentStock, 0);

    return totalQuantity > 0 ? (totalValue / totalQuantity).toFixed(2) : 0;
  }

  /**
   * 搜索物料
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.page.setData({
      searchKeyword: keyword
    });

    // 防抖搜索
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.filterMaterialList();
    }, 500);
  }

  /**
   * 排序切换
   */
  onSortChange(e) {
    const sortBy = e.currentTarget.dataset.sort;
    const currentSortBy = this.page.data.sortBy;
    const currentSortOrder = this.page.data.sortOrder;

    let newSortOrder = 'asc';
    if (sortBy === currentSortBy) {
      newSortOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
    }

    this.page.setData({
      sortBy,
      sortOrder: newSortOrder
    });

    // 重新加载数据
    this.loadMaterialList(true);
  }

  /**
   * 状态过滤
   */
  onStatusFilterChange(e) {
    const status = e.currentTarget.dataset.status;
    this.page.setData({
      filterStatus: status
    });

    this.filterMaterialList();
  }

  // 事件系统
  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  removeEventListener(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emitEvent(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`事件处理器错误 [${event}]:`, error);
        }
      });
    }
  }

  // 事件处理器
  handleMaterialListUpdate(materials) {
    console.log('[EnhancedMaterialModule] 物料列表已更新:', materials.length);
  }

  handleStockMovementUpdate(movements) {
    console.log('[EnhancedMaterialModule] 出入库记录已更新:', movements.length);
  }

  handleAlertUpdate(alerts) {
    console.log('[EnhancedMaterialModule] 预警信息已更新:', alerts);
  }

  handlePurchaseOrderUpdate(orders) {
    console.log('[EnhancedMaterialModule] 采购订单已更新:', orders.length);
  }

  // 辅助方法
  getCurrentUser() {
    try {
      const app = getApp();
      return app?.globalData?.user || null;
    } catch (error) {
      return null;
    }
  }

  handleInitError(error) {
    console.error('[EnhancedMaterialModule] 初始化错误:', error);
    wx.showModal({
      title: '模块加载失败',
      content: error.message || '物料模块初始化失败，请重试',
      showCancel: false
    });
  }

  /**
   * 销毁模块
   */
  destroy() {
    // 清理定时器
    if (this.alertCheckInterval) {
      clearInterval(this.alertCheckInterval);
    }
    if (this.analysisUpdateInterval) {
      clearInterval(this.analysisUpdateInterval);
    }
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // 清理事件监听器
    this.eventListeners.clear();
    
    console.log('[EnhancedMaterialModule] 模块已销毁');
  }
}

module.exports = EnhancedMaterialModule;
