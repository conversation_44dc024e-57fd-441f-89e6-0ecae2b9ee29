/**
 * 增强健康监测模块 - 第三阶段重构版本
 * Enhanced Health Monitoring Module - Phase 3 Refactored Version
 * 
 * 功能包括：
 * - 健康记录管理
 * - AI诊断功能
 * - 健康报告生成
 * - 数据统计分析
 * - 实时数据同步
 */

const { ultimateAPIClient } = require('../../../utils/ultimate-api-client');
const { enhancedDataIsolation } = require('../../../utils/enhanced-data-isolation');
const { permissionManager } = require('../../../utils/enhanced-permission-manager');

/**
 * 增强健康监测模块类
 */
class EnhancedHealthModule {
  constructor(page) {
    this.page = page;
    this.moduleId = 'health';
    this.permissions = [];
    
    // 模块数据结构
    this.data = {
      // 健康记录数据和二级标签页
      healthRecords: [],
      activeHealthTab: 0,
      healthTabs: [
        { id: 0, name: '健康记录', icon: 'health', permission: 'HEALTH_VIEW' },
        { id: 1, name: '健康报告', icon: 'report', permission: 'HEALTH_REPORT_VIEW' },
        { id: 2, name: 'AI诊断', icon: 'ai', permission: 'HEALTH_AI_DIAGNOSIS' }
      ],

      // AI诊断数据
      symptoms: '',
      uploadedImages: [],
      diagnosisResult: null,
      isDiagnosing: false,
      diagnosisHistory: [],

      // 健康报告数据
      reportTypes: [
        { id: 'week', name: '周报', days: 7 },
        { id: 'month', name: '月报', days: 30 },
        { id: 'quarter', name: '季报', days: 90 },
        { id: 'year', name: '年报', days: 365 }
      ],
      activeReportType: 'week',
      reportData: {
        overview: {
          totalGeese: 0,
          healthyCount: 0,
          sickCount: 0,
          deathCount: 0,
          healthyRate: '0%',
          vaccinationRate: '0%',
          treatmentSuccessRate: '0%'
        },
        diseaseStats: [],
        treatmentStats: [],
        trendData: [],
        riskAnalysis: [],
        updateTime: ''
      },

      // 加载状态
      loading: false,
      refreshing: false,
      hasMore: true,
      pageSize: 20,
      currentPage: 1
    };

    // 缓存配置
    this.cacheConfig = {
      recordsCacheTTL: 5 * 60 * 1000, // 5分钟
      reportCacheTTL: 10 * 60 * 1000,  // 10分钟
      aiResultCacheTTL: 30 * 60 * 1000 // 30分钟
    };

    // 事件监听器
    this.eventListeners = new Map();
  }

  /**
   * 初始化健康模块
   */
  async init() {
    try {
      // 1. 检查用户权限
      await this.checkPermissions();
      
      // 2. 初始化数据
      await this.initializeData();
      
      // 3. 设置事件监听
      this.setupEventListeners();
      
      // 4. 加载初始数据
      await this.loadInitialData();
      
      console.log('[HealthModule] 初始化完成');
    } catch (error) {
      console.error('[HealthModule] 初始化失败:', error);
      this.handleInitError(error);
    }
  }

  /**
   * 检查用户权限
   */
  async checkPermissions() {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('用户未登录');
    }

    // 检查模块访问权限
    const hasHealthAccess = await permissionManager.checkPermission(user, 'HEALTH_VIEW');
    if (!hasHealthAccess) {
      throw new Error('无权限访问健康模块');
    }

    // 获取用户所有健康相关权限
    const healthPermissions = [
      'HEALTH_VIEW', 'HEALTH_CREATE', 'HEALTH_EDIT', 'HEALTH_DELETE',
      'HEALTH_REPORT_VIEW', 'HEALTH_REPORT_EXPORT',
      'HEALTH_AI_DIAGNOSIS', 'HEALTH_AI_HISTORY'
    ];

    this.permissions = [];
    for (const permission of healthPermissions) {
      const hasPermission = await permissionManager.checkPermission(user, permission);
      if (hasPermission) {
        this.permissions.push(permission);
      }
    }

    // 根据权限过滤标签页
    this.filterTabsByPermissions();
  }

  /**
   * 根据权限过滤标签页
   */
  filterTabsByPermissions() {
    const filteredTabs = this.data.healthTabs.filter(tab => {
      return !tab.permission || this.permissions.includes(tab.permission);
    });

    this.data.healthTabs = filteredTabs;
  }

  /**
   * 初始化数据
   */
  async initializeData() {
    // 设置初始数据到页面
    this.page.setData({
      ...this.data,
      [`${this.moduleId}_initialized`]: true
    });
  }

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 监听健康记录变更
    this.addEventListener('healthRecordsUpdate', this.handleHealthRecordsUpdate.bind(this));
    
    // 监听AI诊断完成
    this.addEventListener('aiDiagnosisComplete', this.handleAIDiagnosisComplete.bind(this));
    
    // 监听报告数据更新
    this.addEventListener('reportDataUpdate', this.handleReportDataUpdate.bind(this));
  }

  /**
   * 加载初始数据
   */
  async loadInitialData() {
    await Promise.all([
      this.loadHealthRecords(),
      this.loadDiagnosisHistory(),
      this.generateHealthReport()
    ]);
  }

  /**
   * 健康标签页切换
   */
  async onHealthTabChange(e) {
    const tabId = parseInt(e.currentTarget.dataset.tab);
    
    // 检查标签页权限
    const tab = this.data.healthTabs.find(t => t.id === tabId);
    if (tab && tab.permission && !this.permissions.includes(tab.permission)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }

    this.page.setData({
      activeHealthTab: tabId
    });

    // 根据标签页加载对应数据
    await this.loadTabData(tabId);
  }

  /**
   * 根据标签页加载数据
   */
  async loadTabData(tabId) {
    switch (tabId) {
      case 0: // 健康记录
        await this.loadHealthRecords();
        break;
      case 1: // 健康报告
        await this.generateHealthReport();
        break;
      case 2: // AI诊断
        await this.loadDiagnosisHistory();
        break;
    }
  }

  /**
   * 加载健康记录（增强版）
   */
  async loadHealthRecords(refresh = false) {
    if (this.data.loading && !refresh) return;

    try {
      this.page.setData({ 
        loading: true,
        refreshing: refresh 
      });

      const query = {
        limit: this.data.pageSize,
        skip: refresh ? 0 : (this.data.currentPage - 1) * this.data.pageSize,
        sort: { created_at: -1 }
      };

      // 使用安全查询（自动应用数据隔离）
      const response = await ultimateAPIClient.secureQuery('health_records', query, {
        cache: !refresh,
        cacheTTL: this.cacheConfig.recordsCacheTTL
      });

      if (response.success && response.data) {
        const records = this.processHealthRecords(response.data);
        
        const newData = {
          loading: false,
          refreshing: false,
          hasMore: records.length === this.data.pageSize
        };

        if (refresh) {
          newData.healthRecords = records;
          newData.currentPage = 1;
        } else {
          newData.healthRecords = [...this.data.healthRecords, ...records];
          newData.currentPage = this.data.currentPage + 1;
        }

        this.page.setData(newData);

        // 触发数据更新事件
        this.emitEvent('healthRecordsUpdate', records);

        // 自动生成报告数据
        await this.generateHealthReportFromRecords();
      }

    } catch (error) {
      console.error('加载健康记录失败:', error);
      this.page.setData({ 
        loading: false, 
        refreshing: false 
      });
      
      // 显示错误提示
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  }

  /**
   * 处理健康记录数据
   */
  processHealthRecords(rawRecords) {
    return rawRecords.map(record => ({
      id: record._id,
      flockId: record.flock_id,
      flockName: record.flock_name || '未知鹅群',
      checkType: record.check_type || 'health_check',
      status: this.mapStatusToDisplay(record.health_status, record.check_type),
      healthStatus: record.health_status,
      date: this.formatDate(record.check_date),
      description: record.description || record.symptoms || '无描述',
      temperature: record.temperature,
      weight: record.weight,
      treatment: record.treatment,
      veterinarian: record.veterinarian,
      followUpDate: record.follow_up_date,
      images: record.images || [],
      aiDiagnosis: record.ai_diagnosis,
      severity: record.severity || 'normal',
      tags: record.tags || [],
      createdAt: record.created_at,
      updatedAt: record.updated_at
    }));
  }

  /**
   * 状态映射显示
   */
  mapStatusToDisplay(healthStatus, checkType) {
    const statusMap = {
      'vaccination': '防疫记录',
      'disease': '生病记录',
      'treatment': '治疗记录',
      'health_check': '健康检查',
      'emergency': '紧急处理',
      'follow_up': '复查记录'
    };

    return statusMap[checkType] || statusMap[healthStatus] || '健康检查';
  }

  /**
   * 添加健康记录
   */
  async onAddRecord() {
    // 检查创建权限
    if (!this.permissions.includes('HEALTH_CREATE')) {
      wx.showToast({
        title: '无创建权限',
        icon: 'error'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/production-detail/record-detail/record-detail?module=health'
    });
  }

  /**
   * 查看健康记录详情
   */
  async onViewRecord(e) {
    const id = e.currentTarget.dataset.id;
    const record = this.data.healthRecords.find(r => r.id === id);
    
    if (!record) {
      wx.showToast({
        title: '记录不存在',
        icon: 'error'
      });
      return;
    }

    // 检查查看权限
    if (!this.permissions.includes('HEALTH_VIEW')) {
      wx.showToast({
        title: '无查看权限',
        icon: 'error'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/production-detail/record-detail/record-detail?id=${id}&mode=view&module=health`
    });
  }

  /**
   * AI诊断相关方法（增强版）
   */
  onSymptomInput(e) {
    this.page.setData({
      symptoms: e.detail.value
    });
  }

  async onChooseImage() {
    // 检查AI诊断权限
    if (!this.permissions.includes('HEALTH_AI_DIAGNOSIS')) {
      wx.showToast({
        title: '无AI诊断权限',
        icon: 'error'
      });
      return;
    }

    try {
      const res = await this.chooseImages();
      const tempFiles = res.tempFiles;
      const uploadedImages = this.page.data.uploadedImages;
      
      // 限制最多5张图片
      const newImages = tempFiles.slice(0, 5 - uploadedImages.length);
      
      this.page.setData({
        uploadedImages: uploadedImages.concat(newImages.map(file => ({
          path: file.path,
          size: file.size,
          type: file.type || 'image'
        })))
      });

    } catch (error) {
      console.error('选择图片失败:', error);
      wx.showToast({
        title: '选择图片失败',
        icon: 'error'
      });
    }
  }

  /**
   * Promise化的选择图片
   */
  chooseImages() {
    return new Promise((resolve, reject) => {
      wx.chooseImage({
        count: 3,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: resolve,
        fail: reject
      });
    });
  }

  onDeleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const uploadedImages = this.page.data.uploadedImages;
    uploadedImages.splice(index, 1);
    this.page.setData({
      uploadedImages: uploadedImages
    });
  }

  async onStartDiagnosis() {
    const { symptoms, uploadedImages } = this.page.data;

    // 验证输入
    if (!symptoms.trim() && uploadedImages.length === 0) {
      wx.showToast({
        title: '请填写症状或上传图片',
        icon: 'none'
      });
      return;
    }

    // 检查权限
    if (!this.permissions.includes('HEALTH_AI_DIAGNOSIS')) {
      wx.showToast({
        title: '无AI诊断权限',
        icon: 'error'
      });
      return;
    }

    this.page.setData({
      isDiagnosing: true,
      diagnosisResult: null
    });

    try {
      await this.callAIDiagnosis();
    } catch (error) {
      console.error('AI诊断失败:', error);
      this.page.setData({
        isDiagnosing: false
      });
      wx.showToast({
        title: 'AI诊断失败',
        icon: 'error'
      });
    }
  }

  /**
   * 调用AI诊断服务（增强版）
   */
  async callAIDiagnosis() {
    const { symptoms, uploadedImages } = this.page.data;

    try {
      // 1. 上传图片（如果有）
      let imageUrls = [];
      if (uploadedImages.length > 0) {
        imageUrls = await this.uploadDiagnosisImages(uploadedImages);
      }

      // 2. 构建诊断数据
      const diagnosisData = {
        symptoms: symptoms.trim(),
        images: imageUrls,
        timestamp: new Date().toISOString(),
        user_context: {
          experience_level: 'intermediate', // 可以从用户资料获取
          farm_type: 'goose_farm',
          region: 'china'
        }
      };

      // 3. 调用AI诊断API
      const response = await ultimateAPIClient.post('/api/v2/health/ai-diagnosis', diagnosisData, {
        timeout: 30000 // 30秒超时
      });

      if (response.success) {
        const result = this.processAIDiagnosisResult(response.data);
        
        this.page.setData({
          diagnosisResult: result,
          isDiagnosing: false
        });

        // 保存诊断历史
        await this.saveDiagnosisHistory(diagnosisData, result);

        // 触发诊断完成事件
        this.emitEvent('aiDiagnosisComplete', result);

      } else {
        throw new Error(response.message || 'AI诊断失败');
      }

    } catch (error) {
      console.error('AI诊断失败:', error);
      
      // 回退到模拟诊断
      await this.generateMockDiagnosisResult();
    }
  }

  /**
   * 上传诊断图片
   */
  async uploadDiagnosisImages(images) {
    const uploadPromises = images.map(async (image) => {
      try {
        const result = await ultimateAPIClient.utils.upload(image.path, {
          formData: {
            type: 'health_diagnosis',
            module: 'health'
          }
        });
        return result.data.url;
      } catch (error) {
        console.error('图片上传失败:', error);
        return null;
      }
    });

    const results = await Promise.all(uploadPromises);
    return results.filter(url => url !== null);
  }

  /**
   * 处理AI诊断结果
   */
  processAIDiagnosisResult(rawResult) {
    return {
      id: rawResult.id || Date.now().toString(),
      diagnosis: rawResult.diagnosis || '无法确定诊断',
      confidence: rawResult.confidence || 0,
      severity: rawResult.severity || 'unknown',
      symptoms_analysis: rawResult.symptoms_analysis || [],
      recommendations: rawResult.recommendations || [],
      follow_up_days: rawResult.follow_up_days || 7,
      risk_factors: rawResult.risk_factors || [],
      treatment_options: rawResult.treatment_options || [],
      prevention_tips: rawResult.prevention_tips || [],
      created_at: new Date().toISOString()
    };
  }

  /**
   * 保存诊断历史
   */
  async saveDiagnosisHistory(diagnosisData, result) {
    try {
      const historyRecord = {
        symptoms: diagnosisData.symptoms,
        images: diagnosisData.images,
        result: result,
        timestamp: diagnosisData.timestamp
      };

      // 保存到本地存储
      const history = this.page.data.diagnosisHistory || [];
      history.unshift(historyRecord);
      
      // 限制历史记录数量
      const maxHistory = 50;
      if (history.length > maxHistory) {
        history.splice(maxHistory);
      }

      this.page.setData({
        diagnosisHistory: history
      });

      // 可选：保存到服务器
      if (this.permissions.includes('HEALTH_AI_HISTORY')) {
        await ultimateAPIClient.post('/api/v2/health/ai-diagnosis-history', historyRecord);
      }

    } catch (error) {
      console.error('保存诊断历史失败:', error);
    }
  }

  /**
   * 生成模拟诊断结果
   */
  async generateMockDiagnosisResult() {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockResult = {
          id: Date.now().toString(),
          diagnosis: '疑似呼吸道感染',
          confidence: 0.85,
          severity: 'moderate',
          symptoms_analysis: [
            '呼吸急促 - 高风险指标',
            '食欲不振 - 中等风险指标',
            '精神萎靡 - 中等风险指标'
          ],
          recommendations: [
            '立即隔离患病鹅只',
            '使用抗生素治疗（如阿莫西林）',
            '加强通风，保持环境干燥',
            '3天后复查，观察治疗效果'
          ],
          follow_up_days: 3,
          risk_factors: ['环境湿度过高', '通风不良', '饲料质量问题'],
          treatment_options: [
            { name: '抗生素治疗', priority: 'high', duration: '5-7天' },
            { name: '环境改善', priority: 'high', duration: '立即执行' },
            { name: '营养补充', priority: 'medium', duration: '持续2周' }
          ],
          prevention_tips: [
            '定期清洁鹅舍，保持干燥',
            '确保充足的通风',
            '定期健康检查',
            '合理的饲养密度'
          ],
          created_at: new Date().toISOString()
        };

        this.page.setData({
          diagnosisResult: mockResult,
          isDiagnosing: false
        });

        // 保存模拟诊断历史
        this.saveDiagnosisHistory({
          symptoms: this.page.data.symptoms,
          images: this.page.data.uploadedImages.map(img => img.path),
          timestamp: new Date().toISOString()
        }, mockResult);

        resolve(mockResult);
      }, 2000);
    });
  }

  /**
   * 清除诊断结果
   */
  onClearResult() {
    this.page.setData({
      diagnosisResult: null,
      symptoms: '',
      uploadedImages: []
    });
  }

  /**
   * 加载诊断历史
   */
  async loadDiagnosisHistory() {
    if (!this.permissions.includes('HEALTH_AI_HISTORY')) {
      return;
    }

    try {
      const response = await ultimateAPIClient.get('/api/v2/health/ai-diagnosis-history', {
        params: { limit: 20 },
        cache: true,
        cacheTTL: this.cacheConfig.aiResultCacheTTL
      });

      if (response.success && response.data) {
        this.page.setData({
          diagnosisHistory: response.data
        });
      }
    } catch (error) {
      console.error('加载诊断历史失败:', error);
    }
  }

  // 事件系统
  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  removeEventListener(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emitEvent(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`事件处理器错误 [${event}]:`, error);
        }
      });
    }
  }

  // 事件处理器
  handleHealthRecordsUpdate(records) {
    console.log('[HealthModule] 健康记录已更新:', records.length);
  }

  handleAIDiagnosisComplete(result) {
    console.log('[HealthModule] AI诊断完成:', result.diagnosis);
  }

  handleReportDataUpdate(reportData) {
    console.log('[HealthModule] 报告数据已更新');
  }

  // 辅助方法
  getCurrentUser() {
    try {
      const app = getApp();
      return app?.globalData?.user || null;
    } catch (error) {
      return null;
    }
  }

  formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  }

  handleInitError(error) {
    console.error('[HealthModule] 初始化错误:', error);
    wx.showModal({
      title: '模块加载失败',
      content: error.message || '健康模块初始化失败，请重试',
      showCancel: false,
      success: () => {
        // 可以选择回退到上一页或重新初始化
      }
    });
  }

  /**
   * 健康报告相关方法（增强版）
   */
  async onReportTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    this.page.setData({
      activeReportType: type
    });
    await this.generateHealthReport();
  }

  async generateHealthReport() {
    if (!this.permissions.includes('HEALTH_REPORT_VIEW')) {
      return;
    }

    try {
      this.page.setData({ loading: true });

      const reportType = this.page.data.activeReportType;
      const days = this.data.reportTypes.find(t => t.id === reportType)?.days || 7;

      // 获取指定时间范围的健康记录
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

      const query = {
        check_date: {
          $gte: startDate.toISOString(),
          $lte: endDate.toISOString()
        }
      };

      const response = await ultimateAPIClient.secureQuery('health_records', query, {
        cache: true,
        cacheTTL: this.cacheConfig.reportCacheTTL
      });

      if (response.success && response.data) {
        const records = this.processHealthRecords(response.data);
        await this.generateHealthReportFromRecords(records);
      }

    } catch (error) {
      console.error('生成健康报告失败:', error);
    } finally {
      this.page.setData({ loading: false });
    }
  }

  async generateHealthReportFromRecords(records = null) {
    const healthRecords = records || this.page.data.healthRecords || [];

    // 计算各项统计数据
    const overview = this.calculateOverviewStats(healthRecords);
    const diseaseStats = this.calculateDiseaseStats(healthRecords);
    const treatmentStats = this.calculateTreatmentStats(healthRecords);
    const trendData = this.calculateTrendData(healthRecords);
    const riskAnalysis = this.calculateRiskAnalysis(healthRecords);

    const reportData = {
      overview,
      diseaseStats,
      treatmentStats,
      trendData,
      riskAnalysis,
      updateTime: new Date().toLocaleString()
    };

    this.page.setData({
      reportData
    });

    // 触发报告更新事件
    this.emitEvent('reportDataUpdate', reportData);
  }

  calculateOverviewStats(records) {
    const totalRecords = records.length;
    const sickRecords = records.filter(r => r.healthStatus === 'sick' || r.status === '生病记录');
    const treatmentRecords = records.filter(r => r.status === '治疗记录');
    const vaccinationRecords = records.filter(r => r.status === '防疫记录');

    const totalGeese = 150; // 从鹅群数据获取
    const sickCount = sickRecords.length;
    const healthyCount = totalGeese - sickCount;
    const deathCount = records.filter(r => r.healthStatus === 'dead').length;

    const healthyRate = totalGeese > 0 ? ((healthyCount / totalGeese) * 100).toFixed(1) + '%' : '0%';
    const vaccinationRate = totalGeese > 0 ? ((vaccinationRecords.length / totalGeese) * 100).toFixed(1) + '%' : '0%';
    const treatmentSuccessRate = treatmentRecords.length > 0 ?
      ((treatmentRecords.filter(r => r.healthStatus === 'recovered').length / treatmentRecords.length) * 100).toFixed(1) + '%' : '0%';

    return {
      totalGeese,
      healthyCount,
      sickCount,
      deathCount,
      healthyRate,
      vaccinationRate,
      treatmentSuccessRate,
      totalRecords
    };
  }

  calculateDiseaseStats(records) {
    const sickRecords = records.filter(r => r.healthStatus === 'sick' || r.status === '生病记录');
    const diseaseMap = {};

    sickRecords.forEach(record => {
      const disease = this.extractDiseaseFromDescription(record.description) || '未知疾病';
      diseaseMap[disease] = (diseaseMap[disease] || 0) + 1;
    });

    return Object.entries(diseaseMap)
      .map(([disease, count]) => ({
        disease,
        count,
        percentage: sickRecords.length > 0 ? ((count / sickRecords.length) * 100).toFixed(1) + '%' : '0%',
        severity: this.assessDiseaseSeverity(disease, count)
      }))
      .sort((a, b) => b.count - a.count);
  }

  calculateTreatmentStats(records) {
    const treatmentRecords = records.filter(r => r.status === '治疗记录');
    const treatmentMap = {};

    treatmentRecords.forEach(record => {
      const treatment = record.treatment || '未知治疗';
      treatmentMap[treatment] = (treatmentMap[treatment] || 0) + 1;
    });

    return Object.entries(treatmentMap)
      .map(([treatment, count]) => ({
        treatment,
        count,
        successRate: this.calculateTreatmentSuccessRate(treatment, treatmentRecords),
        avgDuration: this.calculateAvgTreatmentDuration(treatment, treatmentRecords)
      }))
      .sort((a, b) => b.count - a.count);
  }

  calculateTrendData(records) {
    const monthlyData = {};

    records.forEach(record => {
      const month = record.date.substring(0, 7); // YYYY-MM
      if (!monthlyData[month]) {
        monthlyData[month] = { healthy: 0, sick: 0, treatment: 0, vaccination: 0 };
      }

      switch (record.status) {
        case '健康检查':
          monthlyData[month].healthy++;
          break;
        case '生病记录':
          monthlyData[month].sick++;
          break;
        case '治疗记录':
          monthlyData[month].treatment++;
          break;
        case '防疫记录':
          monthlyData[month].vaccination++;
          break;
      }
    });

    return Object.entries(monthlyData)
      .map(([month, data]) => ({
        month,
        ...data,
        total: data.healthy + data.sick + data.treatment + data.vaccination
      }))
      .sort((a, b) => a.month.localeCompare(b.month));
  }

  calculateRiskAnalysis(records) {
    const risks = [];

    // 分析疾病风险
    const diseaseStats = this.calculateDiseaseStats(records);
    diseaseStats.forEach(stat => {
      if (stat.count > 5) {
        risks.push({
          type: 'disease',
          risk: `${stat.disease}高发`,
          level: stat.severity,
          probability: stat.percentage,
          recommendation: `加强${stat.disease}的预防措施`
        });
      }
    });

    // 分析季节性风险
    const seasonalRisks = this.analyzeSeasonalRisks(records);
    risks.push(...seasonalRisks);

    return risks.sort((a, b) => {
      const levelOrder = { 'high': 3, 'medium': 2, 'low': 1 };
      return (levelOrder[b.level] || 0) - (levelOrder[a.level] || 0);
    });
  }

  // 辅助分析方法
  extractDiseaseFromDescription(description) {
    const diseaseKeywords = {
      '呼吸道': ['呼吸', '咳嗽', '喘息', '鼻塞'],
      '消化道': ['腹泻', '便秘', '食欲不振', '呕吐'],
      '皮肤病': ['皮疹', '脱毛', '瘙痒', '红肿'],
      '感染性': ['发热', '精神萎靡', '食欲减退']
    };

    for (const [disease, keywords] of Object.entries(diseaseKeywords)) {
      if (keywords.some(keyword => description.includes(keyword))) {
        return disease + '疾病';
      }
    }

    return null;
  }

  assessDiseaseSeverity(disease, count) {
    if (count >= 10) return 'high';
    if (count >= 5) return 'medium';
    return 'low';
  }

  calculateTreatmentSuccessRate(treatment, records) {
    const treatmentRecords = records.filter(r => r.treatment === treatment);
    const successfulTreatments = treatmentRecords.filter(r => r.healthStatus === 'recovered');

    return treatmentRecords.length > 0 ?
      ((successfulTreatments.length / treatmentRecords.length) * 100).toFixed(1) + '%' : '0%';
  }

  calculateAvgTreatmentDuration(treatment, records) {
    const treatmentRecords = records.filter(r => r.treatment === treatment && r.followUpDate);

    if (treatmentRecords.length === 0) return '未知';

    const totalDays = treatmentRecords.reduce((sum, record) => {
      const startDate = new Date(record.date);
      const endDate = new Date(record.followUpDate);
      const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      return sum + (days > 0 ? days : 0);
    }, 0);

    const avgDays = Math.round(totalDays / treatmentRecords.length);
    return avgDays > 0 ? `${avgDays}天` : '未知';
  }

  analyzeSeasonalRisks(records) {
    const currentMonth = new Date().getMonth() + 1;
    const seasonalRisks = [];

    // 春季风险（3-5月）
    if (currentMonth >= 3 && currentMonth <= 5) {
      seasonalRisks.push({
        type: 'seasonal',
        risk: '春季呼吸道疾病高发',
        level: 'medium',
        probability: '30%',
        recommendation: '加强通风，注意保暖'
      });
    }

    // 夏季风险（6-8月）
    if (currentMonth >= 6 && currentMonth <= 8) {
      seasonalRisks.push({
        type: 'seasonal',
        risk: '夏季热应激风险',
        level: 'high',
        probability: '40%',
        recommendation: '做好防暑降温工作'
      });
    }

    // 秋季风险（9-11月）
    if (currentMonth >= 9 && currentMonth <= 11) {
      seasonalRisks.push({
        type: 'seasonal',
        risk: '秋季消化道疾病风险',
        level: 'medium',
        probability: '25%',
        recommendation: '注意饲料质量和卫生'
      });
    }

    // 冬季风险（12-2月）
    if (currentMonth === 12 || currentMonth <= 2) {
      seasonalRisks.push({
        type: 'seasonal',
        risk: '冬季感冒和冻伤风险',
        level: 'medium',
        probability: '35%',
        recommendation: '做好保温防寒工作'
      });
    }

    return seasonalRisks;
  }

  /**
   * 导出健康报告（增强版）
   */
  async onExportReport() {
    if (!this.permissions.includes('HEALTH_REPORT_EXPORT')) {
      wx.showToast({
        title: '无导出权限',
        icon: 'error'
      });
      return;
    }

    wx.showActionSheet({
      itemList: ['导出健康统计表', '导出AI诊断报告', '导出健康趋势图', '导出详细记录', '导出风险分析报告'],
      success: (res) => {
        const types = ['statistics', 'ai_diagnosis', 'trends', 'detailed', 'risk_analysis'];
        this.executeHealthExport(types[res.tapIndex]);
      }
    });
  }

  async executeHealthExport(type) {
    wx.showLoading({
      title: '生成报告中...'
    });

    try {
      const data = await this.prepareHealthExportData(type);

      // 调用导出API
      const response = await ultimateAPIClient.post('/api/v2/health/export-report', {
        type,
        data,
        format: 'excel' // 或 'pdf'
      });

      wx.hideLoading();

      if (response.success) {
        this.showExportResult(type, response.data);
      } else {
        throw new Error(response.message || '导出失败');
      }

    } catch (error) {
      wx.hideLoading();
      console.error('导出报告失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'error'
      });
    }
  }

  async prepareHealthExportData(type) {
    const healthRecords = this.page.data.healthRecords || [];
    const reportData = this.page.data.reportData;

    switch (type) {
      case 'statistics':
        return {
          overview: reportData.overview,
          diseaseStats: reportData.diseaseStats,
          treatmentStats: reportData.treatmentStats,
          reportType: this.page.data.activeReportType
        };
      case 'ai_diagnosis':
        return {
          diagnosisHistory: this.page.data.diagnosisHistory,
          insights: this.generateAIInsights(healthRecords),
          summary: this.generateAIDiagnosisSummary()
        };
      case 'trends':
        return {
          trendData: reportData.trendData,
          seasonalAnalysis: this.analyzeSeasonalHealth(healthRecords),
          predictions: this.generateHealthPredictions(reportData.trendData)
        };
      case 'detailed':
        return {
          records: healthRecords,
          groupedByStatus: this.groupRecordsByStatus(healthRecords),
          monthlyBreakdown: this.groupRecordsByMonth(healthRecords),
          statistics: reportData.overview
        };
      case 'risk_analysis':
        return {
          riskAnalysis: reportData.riskAnalysis,
          preventionPlan: this.generatePreventionPlan(reportData.riskAnalysis),
          recommendations: this.generateRiskRecommendations(reportData.riskAnalysis)
        };
      default:
        return {};
    }
  }

  generateAIInsights(records) {
    return [
      '春季呼吸道疾病发病率较高，建议加强通风',
      '夏季注意防暑降温，预防热应激',
      '定期进行健康检查，早发现早治疗',
      'AI诊断准确率达85%，建议结合专业兽医诊断'
    ];
  }

  generateAIDiagnosisSummary() {
    const history = this.page.data.diagnosisHistory || [];
    const totalDiagnoses = history.length;
    const avgConfidence = history.reduce((sum, item) => sum + (item.result?.confidence || 0), 0) / totalDiagnoses;

    return {
      totalDiagnoses,
      avgConfidence: (avgConfidence * 100).toFixed(1) + '%',
      mostCommonDiagnosis: this.getMostCommonDiagnosis(history),
      successRate: '85%' // 基于历史数据计算
    };
  }

  getMostCommonDiagnosis(history) {
    const diagnosisMap = {};
    history.forEach(item => {
      const diagnosis = item.result?.diagnosis || '未知';
      diagnosisMap[diagnosis] = (diagnosisMap[diagnosis] || 0) + 1;
    });

    const entries = Object.entries(diagnosisMap);
    if (entries.length === 0) return '无数据';

    return entries.sort((a, b) => b[1] - a[1])[0][0];
  }

  generateHealthPredictions(trendData) {
    // 基于趋势数据生成健康预测
    const predictions = [];

    if (trendData.length >= 3) {
      const recent = trendData.slice(-3);
      const sickTrend = recent.map(d => d.sick);

      if (sickTrend[2] > sickTrend[1] && sickTrend[1] > sickTrend[0]) {
        predictions.push({
          type: 'warning',
          message: '疾病发病率呈上升趋势，建议加强预防措施',
          probability: '70%'
        });
      }
    }

    return predictions;
  }

  generatePreventionPlan(riskAnalysis) {
    return riskAnalysis.map(risk => ({
      risk: risk.risk,
      preventionMeasures: [
        '定期健康检查',
        '环境卫生管理',
        '合理饲养密度',
        '疫苗接种计划'
      ],
      timeline: '持续执行',
      priority: risk.level
    }));
  }

  generateRiskRecommendations(riskAnalysis) {
    const highRisks = riskAnalysis.filter(r => r.level === 'high');
    const mediumRisks = riskAnalysis.filter(r => r.level === 'medium');

    return {
      immediate: highRisks.map(r => r.recommendation),
      shortTerm: mediumRisks.map(r => r.recommendation),
      longTerm: [
        '建立完善的健康档案系统',
        '定期培训饲养人员',
        '建立疾病预警机制'
      ]
    };
  }

  analyzeSeasonalHealth(records) {
    const seasons = { spring: 0, summer: 0, autumn: 0, winter: 0 };
    records.forEach(record => {
      const month = new Date(record.date).getMonth() + 1;
      if (month >= 3 && month <= 5) seasons.spring++;
      else if (month >= 6 && month <= 8) seasons.summer++;
      else if (month >= 9 && month <= 11) seasons.autumn++;
      else seasons.winter++;
    });
    return seasons;
  }

  groupRecordsByStatus(records) {
    const groups = {};
    records.forEach(record => {
      const status = record.status;
      if (!groups[status]) groups[status] = [];
      groups[status].push(record);
    });
    return groups;
  }

  groupRecordsByMonth(records) {
    const groups = {};
    records.forEach(record => {
      const month = record.date.substring(0, 7);
      if (!groups[month]) groups[month] = [];
      groups[month].push(record);
    });
    return groups;
  }

  showExportResult(type, data) {
    const typeNames = {
      'statistics': '健康统计表',
      'ai_diagnosis': 'AI诊断报告',
      'trends': '健康趋势图',
      'detailed': '详细记录',
      'risk_analysis': '风险分析报告'
    };

    wx.showModal({
      title: '导出成功',
      content: `${typeNames[type]}已生成完成\n文件大小：${(JSON.stringify(data).length / 1024).toFixed(2)}KB`,
      showCancel: false,
      success: () => {
        // 可以提供下载链接或分享功能
        if (data.downloadUrl) {
          wx.setClipboardData({
            data: data.downloadUrl,
            success: () => {
              wx.showToast({
                title: '下载链接已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  }

  /**
   * 销毁模块
   */
  destroy() {
    // 清理事件监听器
    this.eventListeners.clear();

    // 清理定时器等资源
    console.log('[HealthModule] 模块已销毁');
  }
}

module.exports = EnhancedHealthModule;
