/**
 * 生产页面 - 生产记录模块
 * Production Page - Production Records Module
 */

class ProductionModule {
  constructor(page) {
    this.page = page;
    this.productionRecords = [];
    this.batchColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];
    this.filterType = 'all';
    this.searchKeyword = '';
  }

  /**
   * 初始化生产模块
   */
  initialize() {
    this.initializeProductionData();
    this.filterRecordList();
  }

  /**
   * 初始化生产数据
   */
  initializeProductionData() {
    this.productionRecords = [
      {
        id: 1,
        date: '2024-01-15',
        time: '08:30',
        batch: 'A001',
        type: '产蛋记录',
        quantity: 245,
        unit: '枚',
        quality: '优',
        notes: '产蛋率正常，蛋品质量良好',
        operator: '张三',
        weather: '晴',
        temperature: '15°C',
        humidity: '65%'
      },
      {
        id: 2,
        date: '2024-01-15',
        time: '09:15',
        batch: 'B002',
        type: '饲料投喂',
        quantity: 120,
        unit: 'kg',
        quality: '正常',
        notes: '按标准配比投喂，鹅群食欲良好',
        operator: '李四',
        weather: '晴',
        temperature: '15°C',
        humidity: '65%'
      },
      {
        id: 3,
        date: '2024-01-14',
        time: '16:45',
        batch: 'A001',
        type: '体重测量',
        quantity: 50,
        unit: '只',
        quality: '良好',
        notes: '平均体重3.2kg，增重正常',
        operator: '王五',
        weather: '多云',
        temperature: '12°C',
        humidity: '70%'
      },
      {
        id: 4,
        date: '2024-01-14',
        time: '14:20',
        batch: 'C003',
        type: '清洁消毒',
        quantity: 1,
        unit: '次',
        quality: '完成',
        notes: '鹅舍全面清洁消毒，环境卫生良好',
        operator: '赵六',
        weather: '多云',
        temperature: '12°C',
        humidity: '70%'
      },
      {
        id: 5,
        date: '2024-01-13',
        time: '10:30',
        batch: 'B002',
        type: '疫苗接种',
        quantity: 200,
        unit: '只',
        quality: '完成',
        notes: '禽流感疫苗接种，无不良反应',
        operator: '孙七',
        weather: '阴',
        temperature: '10°C',
        humidity: '75%'
      },
      {
        id: 6,
        date: '2024-01-13',
        time: '07:45',
        batch: 'A001',
        type: '产蛋记录',
        quantity: 238,
        unit: '枚',
        quality: '优',
        notes: '产蛋率稳定，蛋重平均65g',
        operator: '张三',
        weather: '阴',
        temperature: '10°C',
        humidity: '75%'
      }
    ];

    // 为每个批次分配颜色索引
    const recordsWithColorIndex = this.productionRecords.map(record => {
      const colorIndex = this.calculateBatchColorIndex(record.batch);
      return {
        ...record,
        batchColorIndex: colorIndex
      };
    });

    this.page.setData({
      productionRecords: recordsWithColorIndex
    });
  }

  /**
   * 计算批次颜色索引
   */
  calculateBatchColorIndex(batch) {
    let hash = 0;
    for (let i = 0; i < batch.length; i++) {
      const char = batch.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash) % this.batchColors.length;
  }

  /**
   * 记录类型过滤
   */
  onRecordTypeFilter(e) {
    const filterType = e.currentTarget.dataset.type;
    
    this.filterType = filterType;
    this.page.setData({
      filterType: filterType
    });
    
    this.filterRecordList();
  }

  /**
   * 搜索记录
   */
  onSearchRecord(e) {
    this.searchKeyword = e.detail.value;
    this.page.setData({
      searchKeyword: this.searchKeyword
    });
    
    // 防抖搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.filterRecordList();
    }, 300);
  }

  /**
   * 过滤记录列表
   */
  filterRecordList() {
    let filteredRecords = this.productionRecords;

    // 按类型过滤
    if (this.filterType !== 'all') {
      filteredRecords = filteredRecords.filter(record => 
        record.type === this.filterType
      );
    }

    // 按关键词搜索
    if (this.searchKeyword && this.searchKeyword.trim()) {
      const keyword = this.searchKeyword.trim().toLowerCase();
      filteredRecords = filteredRecords.filter(record => 
        record.batch.toLowerCase().includes(keyword) ||
        record.type.toLowerCase().includes(keyword) ||
        record.notes.toLowerCase().includes(keyword) ||
        record.operator.toLowerCase().includes(keyword)
      );
    }

    this.page.setData({
      filteredProductionRecords: filteredRecords
    });

    console.log('[ProductionModule] 记录过滤完成，结果数:', filteredRecords.length);
  }

  /**
   * 记录点击事件
   */
  onRecordTap(e) {
    const recordId = e.currentTarget.dataset.id;
    const record = this.productionRecords.find(item => item.id === recordId);
    
    if (record) {
      this.showRecordDetail(record);
    }
  }

  /**
   * 显示记录详情
   */
  showRecordDetail(record) {
    this.page.setData({
      showRecordDetail: true,
      currentRecord: record
    });
  }

  /**
   * 关闭记录详情
   */
  onCloseRecordDetail() {
    this.page.setData({
      showRecordDetail: false,
      currentRecord: null
    });
  }

  /**
   * 添加新记录
   */
  onAddRecord() {
    wx.navigateTo({
      url: '/pages/production/add-record/add-record'
    });
  }

  /**
   * 编辑记录
   */
  onEditRecord(recordId) {
    wx.navigateTo({
      url: `/pages/production/edit-record/edit-record?id=${recordId}`
    });
  }

  /**
   * 删除记录
   */
  onDeleteRecord(recordId) {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条生产记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.deleteRecord(recordId);
        }
      }
    });
  }

  /**
   * 执行删除记录
   */
  deleteRecord(recordId) {
    const index = this.productionRecords.findIndex(record => record.id === recordId);
    
    if (index > -1) {
      this.productionRecords.splice(index, 1);
      this.filterRecordList();
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    }
  }

  /**
   * 批量操作
   */
  onBatchOperation() {
    const actions = ['批量导出', '批量删除', '数据统计', '生成报告'];
    
    wx.showActionSheet({
      itemList: actions,
      success: (res) => {
        const action = actions[res.tapIndex];
        this.handleBatchOperation(action);
      }
    });
  }

  /**
   * 处理批量操作
   */
  handleBatchOperation(action) {
    switch (action) {
      case '批量导出':
        this.exportRecords();
        break;
      case '批量删除':
        this.batchDeleteRecords();
        break;
      case '数据统计':
        this.showStatistics();
        break;
      case '生成报告':
        this.generateReport();
        break;
    }
  }

  /**
   * 导出记录
   */
  exportRecords() {
    wx.showLoading({
      title: '导出中...'
    });

    // 模拟导出过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      });
    }, 2000);
  }

  /**
   * 批量删除记录
   */
  batchDeleteRecords() {
    wx.showModal({
      title: '批量删除',
      content: '此功能需要先选择要删除的记录',
      showCancel: false
    });
  }

  /**
   * 显示统计信息
   */
  showStatistics() {
    const stats = this.calculateStatistics();
    
    wx.showModal({
      title: '数据统计',
      content: `总记录数: ${stats.total}\n产蛋记录: ${stats.eggCount}\n饲料投喂: ${stats.feedCount}\n其他记录: ${stats.otherCount}`,
      showCancel: false
    });
  }

  /**
   * 计算统计数据
   */
  calculateStatistics() {
    const total = this.productionRecords.length;
    const eggCount = this.productionRecords.filter(r => r.type === '产蛋记录').length;
    const feedCount = this.productionRecords.filter(r => r.type === '饲料投喂').length;
    const otherCount = total - eggCount - feedCount;

    return {
      total,
      eggCount,
      feedCount,
      otherCount
    };
  }

  /**
   * 生成报告
   */
  generateReport() {
    wx.navigateTo({
      url: '/pages/production/report/report'
    });
  }

  /**
   * 获取记录类型列表
   */
  getRecordTypes() {
    const types = [...new Set(this.productionRecords.map(record => record.type))];
    return ['all', ...types];
  }

  /**
   * 获取批次列表
   */
  getBatches() {
    const batches = [...new Set(this.productionRecords.map(record => record.batch))];
    return batches.sort();
  }

  /**
   * 按日期获取记录
   */
  getRecordsByDate(date) {
    return this.productionRecords.filter(record => record.date === date);
  }

  /**
   * 按批次获取记录
   */
  getRecordsByBatch(batch) {
    return this.productionRecords.filter(record => record.batch === batch);
  }

  /**
   * 获取最近记录
   */
  getRecentRecords(limit = 5) {
    return this.productionRecords
      .sort((a, b) => new Date(`${b.date} ${b.time}`) - new Date(`${a.date} ${a.time}`))
      .slice(0, limit);
  }

  /**
   * 获取生产模块数据
   */
  getData() {
    return {
      productionRecords: this.productionRecords,
      filterType: this.filterType,
      searchKeyword: this.searchKeyword
    };
  }

  /**
   * 销毁模块
   */
  destroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    this.productionRecords = [];
    console.log('[ProductionModule] 模块已销毁');
  }
}

module.exports = ProductionModule;
