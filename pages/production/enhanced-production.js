/**
 * 增强生产管理页面 - 第三阶段重构版本
 * Enhanced Production Management Page - Phase 3 Refactored Version
 * 
 * 采用模块化架构，整合所有生产相关功能：
 * - 健康监测模块
 * - 物料管理模块
 * - 生产记录模块
 * - 知识库模块
 */

const { ultimateAPIClient } = require('../../utils/ultimate-api-client');
const { configManager } = require('../../utils/unified-config-manager');
const { permissionManager } = require('../../utils/enhanced-permission-manager');

// 导入模块
const EnhancedHealthModule = require('./modules/enhanced-health-module');
const EnhancedMaterialModule = require('./modules/enhanced-material-module');

/**
 * 增强生产管理页面类
 */
class EnhancedProductionPage {
  constructor() {
    // 页面数据
    this.data = {
      // 页面状态
      loading: false,
      initialized: false,
      currentUser: null,

      // 主导航标签
      activeTab: 0,
      mainTabs: [
        { id: 0, name: '生产记录', icon: 'record', module: 'production' },
        { id: 1, name: '健康监测', icon: 'health', module: 'health' },
        { id: 2, name: '物料管理', icon: 'material', module: 'material' },
        { id: 3, name: '知识库', icon: 'knowledge', module: 'knowledge' }
      ],

      // 权限控制
      userPermissions: [],
      availableTabs: [],

      // 模块状态
      moduleStates: {
        production: { loaded: false, error: null },
        health: { loaded: false, error: null },
        material: { loaded: false, error: null },
        knowledge: { loaded: false, error: null }
      },

      // 全局搜索
      globalSearchVisible: false,
      globalSearchKeyword: '',
      globalSearchResults: [],

      // 快捷操作
      quickActions: [
        { id: 'add_record', name: '添加记录', icon: 'add', permission: 'PRODUCTION_CREATE' },
        { id: 'health_check', name: '健康检查', icon: 'health', permission: 'HEALTH_CREATE' },
        { id: 'material_in', name: '物料入库', icon: 'in', permission: 'MATERIAL_STOCK_MOVEMENT' },
        { id: 'export_report', name: '导出报告', icon: 'export', permission: 'PRODUCTION_EXPORT' }
      ],

      // 统计概览
      overviewStats: {
        totalRecords: 0,
        healthyRate: '0%',
        materialAlerts: 0,
        pendingTasks: 0
      }
    };

    // 模块实例
    this.modules = {
      health: null,
      material: null,
      production: null,
      knowledge: null
    };

    // 事件监听器
    this.eventListeners = new Map();

    // 页面配置
    this.pageConfig = {
      enablePullRefresh: true,
      enableReachBottom: true,
      tabSwitchAnimation: true,
      autoSaveInterval: 30000 // 30秒自动保存
    };
  }

  /**
   * 页面加载
   */
  async onLoad(options) {
    try {
      console.log('[EnhancedProductionPage] 页面开始加载');
      
      // 显示加载状态
      wx.showLoading({ title: '加载中...' });
      
      // 1. 初始化页面
      await this.initializePage(options);
      
      // 2. 检查用户权限
      await this.checkUserPermissions();
      
      // 3. 初始化模块
      await this.initializeModules();
      
      // 4. 加载页面数据
      await this.loadPageData();
      
      // 5. 设置页面配置
      this.setupPageConfig();
      
      wx.hideLoading();
      
      console.log('[EnhancedProductionPage] 页面加载完成');
      
    } catch (error) {
      wx.hideLoading();
      console.error('[EnhancedProductionPage] 页面加载失败:', error);
      this.handlePageError(error);
    }
  }

  /**
   * 初始化页面
   */
  async initializePage(options) {
    // 获取当前用户
    const app = getApp();
    const currentUser = app.globalData.user;
    
    if (!currentUser) {
      throw new Error('用户未登录');
    }

    // 解析页面参数
    const activeTab = parseInt(options.tab) || 0;
    const module = options.module || 'production';

    // 设置初始数据
    this.setData({
      currentUser,
      activeTab,
      loading: true
    });

    // 加载页面配置
    this.loadPageConfig();
  }

  /**
   * 加载页面配置
   */
  loadPageConfig() {
    const config = configManager.getByPrefix('business.production');
    
    // 更新页面配置
    Object.assign(this.pageConfig, {
      recordRetentionDays: config['business.production.recordRetentionDays'] || 365,
      batchSizeLimit: config['business.production.batchSizeLimit'] || 100,
      photoUploadLimit: config['business.production.photoUploadLimit'] || 5,
      autoSaveEnabled: config['business.production.autoSaveEnabled'] !== false
    });
  }

  /**
   * 检查用户权限
   */
  async checkUserPermissions() {
    const user = this.data.currentUser;
    
    // 获取用户权限信息
    const permissionInfo = await permissionManager.getUserPermissionInfo(user);
    const userPermissions = permissionInfo.allPermissions || [];

    // 过滤可用的标签页
    const availableTabs = this.data.mainTabs.filter(tab => {
      const requiredPermissions = this.getTabRequiredPermissions(tab.module);
      return requiredPermissions.some(permission => userPermissions.includes(permission));
    });

    // 过滤可用的快捷操作
    const availableQuickActions = this.data.quickActions.filter(action => {
      return !action.permission || userPermissions.includes(action.permission);
    });

    this.setData({
      userPermissions,
      availableTabs,
      quickActions: availableQuickActions
    });

    // 如果当前标签页不可用，切换到第一个可用标签页
    if (!availableTabs.find(tab => tab.id === this.data.activeTab)) {
      this.setData({
        activeTab: availableTabs[0]?.id || 0
      });
    }
  }

  /**
   * 获取标签页所需权限
   */
  getTabRequiredPermissions(module) {
    const permissionMap = {
      production: ['PRODUCTION_VIEW'],
      health: ['HEALTH_VIEW'],
      material: ['MATERIAL_VIEW'],
      knowledge: ['KNOWLEDGE_VIEW']
    };

    return permissionMap[module] || [];
  }

  /**
   * 初始化模块
   */
  async initializeModules() {
    const initPromises = [];

    // 根据可用标签页初始化对应模块
    for (const tab of this.data.availableTabs) {
      const modulePromise = this.initializeModule(tab.module);
      initPromises.push(modulePromise);
    }

    // 并行初始化所有模块
    const results = await Promise.allSettled(initPromises);
    
    // 处理初始化结果
    results.forEach((result, index) => {
      const module = this.data.availableTabs[index].module;
      
      if (result.status === 'fulfilled') {
        this.setData({
          [`moduleStates.${module}.loaded`]: true
        });
      } else {
        console.error(`模块 ${module} 初始化失败:`, result.reason);
        this.setData({
          [`moduleStates.${module}.error`]: result.reason.message
        });
      }
    });
  }

  /**
   * 初始化单个模块
   */
  async initializeModule(moduleName) {
    switch (moduleName) {
      case 'health':
        this.modules.health = new EnhancedHealthModule(this);
        await this.modules.health.init();
        break;
        
      case 'material':
        this.modules.material = new EnhancedMaterialModule(this);
        await this.modules.material.init();
        break;
        
      case 'production':
        // 生产记录模块（待实现）
        console.log('生产记录模块初始化');
        break;
        
      case 'knowledge':
        // 知识库模块（待实现）
        console.log('知识库模块初始化');
        break;
        
      default:
        throw new Error(`未知模块: ${moduleName}`);
    }
  }

  /**
   * 加载页面数据
   */
  async loadPageData() {
    try {
      // 加载概览统计数据
      await this.loadOverviewStats();
      
      // 设置页面为已初始化状态
      this.setData({
        initialized: true,
        loading: false
      });
      
    } catch (error) {
      console.error('加载页面数据失败:', error);
      throw error;
    }
  }

  /**
   * 加载概览统计数据
   */
  async loadOverviewStats() {
    try {
      const response = await ultimateAPIClient.get('/api/v2/production/overview-stats', {
        cache: true,
        cacheTTL: 2 * 60 * 1000 // 2分钟缓存
      });

      if (response.success && response.data) {
        this.setData({
          overviewStats: response.data
        });
      }
    } catch (error) {
      console.error('加载概览统计失败:', error);
      
      // 使用模拟数据
      this.setData({
        overviewStats: {
          totalRecords: 156,
          healthyRate: '92.5%',
          materialAlerts: 3,
          pendingTasks: 8
        }
      });
    }
  }

  /**
   * 设置页面配置
   */
  setupPageConfig() {
    // 设置下拉刷新
    if (this.pageConfig.enablePullRefresh) {
      wx.enablePullDownRefresh();
    }

    // 启动自动保存
    if (this.pageConfig.autoSaveEnabled) {
      this.startAutoSave();
    }

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '生产管理'
    });
  }

  /**
   * 标签页切换
   */
  async onTabChange(e) {
    const tabId = parseInt(e.currentTarget.dataset.tab);
    const tab = this.data.availableTabs.find(t => t.id === tabId);
    
    if (!tab) {
      wx.showToast({
        title: '标签页不存在',
        icon: 'error'
      });
      return;
    }

    // 检查模块是否已加载
    const moduleState = this.data.moduleStates[tab.module];
    if (!moduleState.loaded && !moduleState.error) {
      wx.showLoading({ title: '加载模块中...' });
      
      try {
        await this.initializeModule(tab.module);
        this.setData({
          [`moduleStates.${tab.module}.loaded`]: true
        });
      } catch (error) {
        this.setData({
          [`moduleStates.${tab.module}.error`]: error.message
        });
        wx.showToast({
          title: '模块加载失败',
          icon: 'error'
        });
        return;
      } finally {
        wx.hideLoading();
      }
    }

    // 切换标签页
    this.setData({
      activeTab: tabId
    });

    // 触发模块激活事件
    this.emitEvent('moduleActivated', { module: tab.module, tabId });
  }

  /**
   * 快捷操作处理
   */
  async onQuickAction(e) {
    const actionId = e.currentTarget.dataset.action;
    
    switch (actionId) {
      case 'add_record':
        await this.handleAddRecord();
        break;
      case 'health_check':
        await this.handleHealthCheck();
        break;
      case 'material_in':
        await this.handleMaterialIn();
        break;
      case 'export_report':
        await this.handleExportReport();
        break;
      default:
        console.warn('未知的快捷操作:', actionId);
    }
  }

  /**
   * 全局搜索
   */
  onGlobalSearch(e) {
    const keyword = e.detail.value;
    this.setData({
      globalSearchKeyword: keyword
    });

    // 防抖搜索
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.performGlobalSearch(keyword);
    }, 500);
  }

  /**
   * 执行全局搜索
   */
  async performGlobalSearch(keyword) {
    if (!keyword.trim()) {
      this.setData({
        globalSearchResults: []
      });
      return;
    }

    try {
      const response = await ultimateAPIClient.get('/api/v2/production/global-search', {
        params: { keyword, limit: 20 }
      });

      if (response.success && response.data) {
        this.setData({
          globalSearchResults: response.data
        });
      }
    } catch (error) {
      console.error('全局搜索失败:', error);
    }
  }

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      // 刷新当前激活的模块
      const activeTab = this.data.availableTabs.find(t => t.id === this.data.activeTab);
      if (activeTab && this.modules[activeTab.module]) {
        const module = this.modules[activeTab.module];
        if (typeof module.refresh === 'function') {
          await module.refresh();
        }
      }

      // 刷新概览数据
      await this.loadOverviewStats();

    } catch (error) {
      console.error('下拉刷新失败:', error);
    } finally {
      wx.stopPullDownRefresh();
    }
  }

  /**
   * 上拉加载更多
   */
  async onReachBottom() {
    // 触发当前模块的加载更多
    const activeTab = this.data.availableTabs.find(t => t.id === this.data.activeTab);
    if (activeTab && this.modules[activeTab.module]) {
      const module = this.modules[activeTab.module];
      if (typeof module.loadMore === 'function') {
        await module.loadMore();
      }
    }
  }

  /**
   * 启动自动保存
   */
  startAutoSave() {
    this.autoSaveInterval = setInterval(() => {
      this.performAutoSave();
    }, this.pageConfig.autoSaveInterval);
  }

  /**
   * 执行自动保存
   */
  performAutoSave() {
    // 触发所有模块的自动保存
    Object.values(this.modules).forEach(module => {
      if (module && typeof module.autoSave === 'function') {
        module.autoSave();
      }
    });
  }

  // 快捷操作处理方法
  async handleAddRecord() {
    wx.navigateTo({
      url: '/pages/production-detail/record-detail/record-detail?module=production'
    });
  }

  async handleHealthCheck() {
    if (this.modules.health) {
      await this.modules.health.onAddRecord();
    }
  }

  async handleMaterialIn() {
    wx.navigateTo({
      url: '/pages/production-detail/material-detail/material-detail?type=inbound'
    });
  }

  async handleExportReport() {
    wx.showActionSheet({
      itemList: ['导出生产报告', '导出健康报告', '导出物料报告', '导出综合报告'],
      success: (res) => {
        const types = ['production', 'health', 'material', 'comprehensive'];
        this.executeExport(types[res.tapIndex]);
      }
    });
  }

  async executeExport(type) {
    wx.showLoading({ title: '生成报告中...' });
    
    try {
      const response = await ultimateAPIClient.post('/api/v2/production/export-report', {
        type,
        format: 'excel'
      });

      wx.hideLoading();

      if (response.success) {
        wx.showModal({
          title: '导出成功',
          content: '报告已生成完成',
          showCancel: false
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '导出失败',
        icon: 'error'
      });
    }
  }

  // 事件系统
  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  emitEvent(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`事件处理器错误 [${event}]:`, error);
        }
      });
    }
  }

  /**
   * 处理页面错误
   */
  handlePageError(error) {
    console.error('[EnhancedProductionPage] 页面错误:', error);
    
    wx.showModal({
      title: '页面加载失败',
      content: error.message || '页面初始化失败，请重试',
      showCancel: true,
      cancelText: '返回',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          // 重新加载页面
          this.onLoad({});
        } else {
          // 返回上一页
          wx.navigateBack();
        }
      }
    });
  }

  /**
   * 页面卸载
   */
  onUnload() {
    // 清理定时器
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
    }
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // 销毁所有模块
    Object.values(this.modules).forEach(module => {
      if (module && typeof module.destroy === 'function') {
        module.destroy();
      }
    });

    // 清理事件监听器
    this.eventListeners.clear();

    console.log('[EnhancedProductionPage] 页面已卸载');
  }
}

// 创建页面实例
const enhancedProductionPage = new EnhancedProductionPage();

// 导出页面方法
Page({
  data: enhancedProductionPage.data,
  
  onLoad: enhancedProductionPage.onLoad.bind(enhancedProductionPage),
  onUnload: enhancedProductionPage.onUnload.bind(enhancedProductionPage),
  onPullDownRefresh: enhancedProductionPage.onPullDownRefresh.bind(enhancedProductionPage),
  onReachBottom: enhancedProductionPage.onReachBottom.bind(enhancedProductionPage),
  
  onTabChange: enhancedProductionPage.onTabChange.bind(enhancedProductionPage),
  onQuickAction: enhancedProductionPage.onQuickAction.bind(enhancedProductionPage),
  onGlobalSearch: enhancedProductionPage.onGlobalSearch.bind(enhancedProductionPage),
  
  // 健康模块方法代理
  onHealthTabChange: function(e) {
    if (enhancedProductionPage.modules.health) {
      return enhancedProductionPage.modules.health.onHealthTabChange(e);
    }
  },
  
  onAddHealthRecord: function() {
    if (enhancedProductionPage.modules.health) {
      return enhancedProductionPage.modules.health.onAddRecord();
    }
  },
  
  onViewHealthRecord: function(e) {
    if (enhancedProductionPage.modules.health) {
      return enhancedProductionPage.modules.health.onViewRecord(e);
    }
  },
  
  // 物料模块方法代理
  onMaterialTabChange: function(e) {
    if (enhancedProductionPage.modules.material) {
      return enhancedProductionPage.modules.material.onMaterialTabChange(e);
    }
  },
  
  onAddMaterial: function() {
    if (enhancedProductionPage.modules.material) {
      return enhancedProductionPage.modules.material.onAddMaterial();
    }
  },
  
  onViewMaterial: function(e) {
    if (enhancedProductionPage.modules.material) {
      return enhancedProductionPage.modules.material.onViewMaterial(e);
    }
  }
});
