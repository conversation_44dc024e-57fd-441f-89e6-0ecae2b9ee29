// pages/home/<USER>
const { IMAGES, UI, BUSINESS, API } = require('../../constants/index.js');
const { ultimateAPIClient, businessAPI } = require('../../utils/ultimate-api-client.js');
const { performanceMixin, dataCacheManager, debounce } = require('../../utils/performance.js');
const { optimizedPageMixin, FirstScreenOptimizer } = require('../../utils/performance-optimizer.js');

/**
 * 主页页面 - 智慧养鹅小程序核心页面
 * 功能：展示用户信息、健康数据、任务管理、公告和知识库
 * 遵循微信小程序最佳实践和性能优化规范
 * 性能优化：数据缓存、防抖机制、批量更新、内存管理
 */

Page(Object.assign({}, optimizedPageMixin, {
  data: {
    userInfo: {
      inventoryCount: 0,
      healthRate: '0%',
      environmentStatus: '优',
      avatar: IMAGES.DEFAULTS.AVATAR,
      name: '养殖户',
      farmName: '未设置养殖场'
    },
    healthData: {
      healthyCount: 1120,
      sickCount: 65,
      deathCount: 15,
      statusText: '整体健康',
      statusDesc: '鹅群状态良好，无异常'
    },
    tasks: [],
    announcements: [],
    knowledgeList: [],
    loading: true,
    // 性能优化相关
    refreshing: false,
    lastRefreshTime: 0
  },

  /**
   * 页面加载时执行
   * 初始化页面数据和配置
   */
  onLoad: function (options) {
    // 调用性能混入的onLoad
    if (optimizedPageMixin.onLoad) {
      optimizedPageMixin.onLoad.call(this, options);
    }
    
    this.initPage();
  },

  /**
   * 页面初次渲染完成
   */
  onReady: function () {
    // 调用性能混入的onReady
    if (optimizedPageMixin.onReady) {
      optimizedPageMixin.onReady.call(this);
    }
  },

  /**
   * 页面显示时执行
   * 刷新页面数据，支持防抖优化
   */
  onShow: function () {
    this.refreshPageData();
  },

  /**
   * 初始化页面
   */
  initPage: function() {
    // 立即显示默认数据，避免白屏
    this.setData({
      loading: false
    });
    
    // 异步加载数据
    wx.nextTick(() => {
      this.refreshPageData();
    });
  },

  /**
   * 刷新页面数据 - 支持防抖
   * 优化：防抖机制、批量更新、错误处理
   */
  refreshPageData: debounce(function() {
    const now = Date.now();
    const { refreshing } = this.data;
    
    if (refreshing) {
      return;
    }

    this.safeSetData({
      refreshing: true,
      lastRefreshTime: now
    });

    // 并行获取数据，提高性能
    Promise.all([
      this.getUserInfo(),
      this.getAnnouncements(),
      this.getTasks(),
      this.getKnowledgeList()
    ])
      .then(() => {
      })
      .catch(error => {
        try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] 数据刷新失败', error); } catch(_) {}
        wx.showToast({
          title: '刷新失败',
          icon: 'error'
        });
      })
      .finally(() => {
        this.safeSetData({
          refreshing: false,
          loading: false
        });
      });
  }, 1000),

  /**
   * 获取用户信息 - 与个人中心保持一致
   * 优化：数据缓存、安全setData，统一用户信息来源
   */
  getUserInfo: function() {
    return new Promise((resolve) => {
      try {
        // 首先从全局数据和本地存储获取用户信息（与个人中心保持一致）
        const app = getApp();
        let userInfo = app.globalData.userInfo || wx.getStorageSync('user_info');
        
        // 检查是否有有效的登录token
        const token = wx.getStorageSync('access_token');
        
        // 如果没有用户信息或信息不完整，且没有有效token，使用统一的模拟数据
        if (!userInfo || !userInfo.name) {
          if (!token) {
            // 无token时使用默认模拟数据
            userInfo = {
              avatar: IMAGES.DEFAULTS.AVATAR,
              name: '李经理',
              farmName: '智慧生态养鹅基地',
              role: '管理员',
              phone: '138****8888',
              email: '<EMAIL>'
            };
            
            // 保存到全局数据和本地存储
            app.globalData.userInfo = userInfo;
            wx.setStorageSync('user_info', userInfo);
            
          } else {
            // 有token但无用户信息，等待服务器返回数据
            userInfo = {
              avatar: IMAGES.DEFAULTS.AVATAR,
              name: '加载中...',
              farmName: '智慧生态养鹅基地'
            };
          }
        } else {
          // 确保头像有默认值  
          if (!userInfo.avatar) {
            userInfo.avatar = IMAGES.DEFAULTS.AVATAR;
          }
        }

        // 构建首页需要的用户信息格式
        const homeUserInfo = {
          inventoryCount: userInfo.inventoryCount || 1250,
          healthRate: userInfo.healthRate || '95%',
          environmentStatus: userInfo.environmentStatus || '优',
          avatar: userInfo.avatar,
          name: userInfo.name,
          farmName: userInfo.farmName
        };

        // 设置用户信息
        this.safeSetData({
          userInfo: homeUserInfo
        });

        // 尝试获取真实数据
        businessAPI.auth.getUserInfo()
          .then(res => {
            if (res && res.success && res.data) {
              const updatedUserInfo = {
                inventoryCount: res.data.inventoryCount || 1250,
                healthRate: (res.data.healthRate || 95) + '%',
                environmentStatus: res.data.environmentStatus || '优',
                avatar: res.data.avatar || userInfo.avatar,
                name: res.data.name || userInfo.name,
                farmName: res.data.farmName || userInfo.farmName
              };

              // 缓存用户信息
              dataCacheManager.set('userInfo', updatedUserInfo, 10 * 60 * 1000); // 10分钟缓存

              this.safeSetData({ userInfo: updatedUserInfo });
            }
            resolve();
          })
          .catch(err => {
            try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('[Home] 获取用户信息失败，使用本地数据', err); } catch(_) {}
            resolve(); // 即使失败也resolve，避免阻塞其他操作
          });
      } catch (error) {
        try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] getUserInfo方法执行失败', error); } catch(_) {}
        resolve();
      }
    });
  },

  /**
   * 获取公告信息 - 返回Promise支持并行执行
   */
  getAnnouncements: function() {
    return new Promise((resolve) => {
      try {
        // 设置默认公告数据
        const defaultAnnouncements = [
          {
            id: 1,
            title: '冬季养鹅注意事项及防疫措施',
            publishTime: '12-15',
            isImportant: true
          },
          {
            id: 2,
            title: '新品种鹅苗到货通知',
            publishTime: '12-14',
            isImportant: false
          },
          {
            id: 3,
            title: '养殖技术培训通知',
            publishTime: '12-13',
            isImportant: false
          }
        ];

        this.setData({
          announcements: defaultAnnouncements
        });

        // 尝试获取真实数据
        businessAPI.home.getAnnouncements({ page: 1, limit: 5 })
          .then(res => {
            if (res && res.success && res.data && Array.isArray(res.data)) {
              this.setData({
                announcements: res.data.map(item => ({
                  id: item.id,
                  title: item.title || '未知公告',
                  publishTime: this.formatDate(item.createdAt) || '未知时间',
                  isImportant: Boolean(item.isImportant)
                }))
              });
            }
            resolve();
          })
          .catch(err => {
            try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('[Home] 获取公告失败，使用默认数据', err); } catch(_) {}
            resolve();
          });
      } catch (error) {
        try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] getAnnouncements方法执行失败', error); } catch(_) {}
        resolve();
      }
    });
  },

  /**
   * 格式化日期 - 增强容错性
   */
  formatDate: function(dateString) {
    try {
      if (!dateString) return '未知时间';
      
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('[Home] 无效的日期格式', dateString); } catch(_) {}
        return '未知时间';
      }
      
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    } catch (error) {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] 日期格式化失败', error); } catch(_) {}
      return '未知时间';
    }
  },

  /**
   * 获取待办任务 - 返回Promise支持并行执行
   */
  getTasks: function() {
    return new Promise((resolve) => {
      try {
        // 引入统一的待办任务数据
        const { getHomeTaskList } = require('../../utils/task-data.js');
        
        // 获取首页展示的待办任务数据（前3条）
        const tasks = getHomeTaskList(3);

        this.safeSetData({
          tasks: tasks
        });

        resolve();
      } catch (error) {
        try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] getTasks方法执行失败', error); } catch(_) {}
        resolve();
      }
    });
  },


  /**
   * 获取知识库数据 - 返回Promise支持并行执行
   * 使用统一的知识库数据源，确保与健康页面数据一致
   */
  getKnowledgeList: function() {
    return new Promise((resolve) => {
      try {
        // 引入统一的知识库数据
        const { getHomeKnowledgeList } = require('../../utils/knowledge-data.js');
        
        // 获取首页展示的知识库数据（前2条）
        const knowledgeList = getHomeKnowledgeList(2);

        this.setData({
          knowledgeList: knowledgeList
        });

        resolve();
      } catch (error) {
        try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] getKnowledgeList方法执行失败', error); } catch(_) {}
        resolve();
      }
    });
  },



  /**
   * 任务完成处理 - 优化用户体验，完成后自动显示下一个任务
   */
  onTaskComplete: function(e) {
    try {
      const taskId = e.currentTarget.dataset.id;
      // 数据验证
      if (!taskId) {
        try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] 无效的任务ID', taskId); } catch(_) {}
        wx.showToast({
          title: '任务ID无效',
          icon: 'error'
        });
        return;
      }

      // 引入任务管理工具
      const { completeTask } = require('../../utils/task-data.js');
      
      // 标记任务为完成
      completeTask(taskId, new Date().toLocaleString());

      // 显示提示（移除震动反馈）
      wx.showToast({
        title: '任务已完成',
        icon: 'success',
        duration: 1500
      });

      // 延迟1秒后刷新任务列表，自动显示下一个任务
      setTimeout(() => {
        this.getTasks().then(() => {
          // 检查是否有新任务显示
          const currentTasks = this.data.tasks;
          if (currentTasks.length > 0) {
            // 有新任务时给用户提示
            wx.showToast({
              title: '新任务已就绪',
              icon: 'success',
              duration: 1000
            });
          } else {
            // 没有更多任务时的提示
            wx.showToast({
              title: '今日任务已完成',
              icon: 'success',
              duration: 1500
            });
          }
        });
      }, 1000);

    } catch (error) {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] 任务完成处理失败', error); } catch(_) {}
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    }
  },

  /**
   * 跳转到任务详情 - 增强错误处理
   */
  onTaskTap: function(e) {
    try {
      const taskId = e.currentTarget.dataset.id;
      if (!taskId) {
        try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('[Home] 任务ID为空'); } catch(_) {}
        return;
      }
      
      // 跳转到统一的任务详情页面
      wx.navigateTo({
        url: `/pages/task/detail/detail?id=${taskId}`,
        fail: (err) => {
          try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] 跳转任务详情失败', err); } catch(_) {}
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          });
        }
      });
    } catch (error) {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] onTaskTap执行失败', error); } catch(_) {}
    }
  },

  /**
   * 跳转到公告详情 - 增强错误处理
   */
  onAnnouncementTap: function(e) {
    try {
      const announcementId = e.currentTarget.dataset.id;
      if (!announcementId) {
        try { const logger = require('../../utils/logger.js'); logger.warn && logger.warn('[Home] 公告ID为空'); } catch(_) {}
        return;
      }
      
      wx.navigateTo({
        url: `/pages/announcement/announcement-detail/announcement-detail?id=${announcementId}`,
        fail: (err) => {
          try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] 跳转公告详情失败', err); } catch(_) {}
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          });
        }
      });
    } catch (error) {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('[Home] onAnnouncementTap执行失败', error); } catch(_) {}
    }
  },

  // 健康检查
  onHealthCheckTap: function() {
    wx.navigateTo({
      url: '/pages/production/production?tab=1'
    });
  },

  // 疫苗接种
  onVaccineTap: function() {
    wx.navigateTo({
      url: '/pages/production/production?tab=1'
    });
  },

  // 疾病治疗
  onTreatmentTap: function() {
    wx.navigateTo({
      url: '/pages/production/production?tab=1'
    });
  },


  // 查看全部任务 - 直接跳转到任务列表
  onViewAllTasks: function() {
    wx.navigateTo({
      url: '/pages/task/task-list/task-list'
    });
  },

  // 查看全部公告
  onViewAllAnnouncements: function() {
    wx.navigateTo({
      url: '/pages/announcement/announcement-list/announcement-list'
    });
  },

  // 查看全部知识库 - 跳转到健康页面的知识库标签页
  onViewAllKnowledge: function() {
    console.log('[Home] 点击知识库查看全部按钮');
    
    // 先检查页面是否存在
    const pages = getCurrentPages();
    console.log('[Home] 当前页面栈:', pages.map(p => p.route));
    
    // 设置全局变量，指定目标标签页
    getApp().globalData.targetTab = 3;
    
    // 使用switchTab跳转到tabbar页面
    wx.switchTab({
      url: '/pages/production/production',
      success: function() {
        console.log('[Home] 跳转成功');
      },
      fail: function(error) {
        console.error('[Home] 跳转失败详情:', error);
        console.error('[Home] 错误信息:', error.errMsg);
        console.error('[Home] 错误代码:', error.errCode);
        
        // 显示详细的错误信息
        let errorMsg = '未知错误';
        if (error.errMsg) {
          errorMsg = error.errMsg;
        } else if (error.errCode) {
          errorMsg = '错误代码: ' + error.errCode;
        }
        
        wx.showToast({
          title: '跳转失败: ' + errorMsg,
          icon: 'error',
          duration: 3000
        });
      }
    });
  },





  // 查看更多公告
  onMoreAnnouncementsTap: function() {
    wx.navigateTo({
      url: '/pages/announcement/announcement-list/announcement-list'
    });
  },

  /**
   * 下拉刷新处理
   */
  onPullDownRefresh: function() {
    this.refreshPageData();
    
    // 刷新完成后停止下拉刷新动画
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面隐藏时清除定时器和重置状态
   */
  onHide: function() {
    if (this.data.announcementTimer) {
      clearInterval(this.data.announcementTimer);
      this.setData({
        announcementTimer: null
      });
    }
  },

  /**
   * 页面卸载时清理资源
   */
  onUnload: function() {
    if (this.data.announcementTimer) {
      clearInterval(this.data.announcementTimer);
    }
  },

  // 知识库相关方法
  // "查看全部"跳转到健康模块知识库
  onKnowledgeMoreTap: function() {
    wx.navigateTo({
      url: '/pages/production-detail/knowledge/knowledge'
    });
  },

  // 点击文章跳转到独立的详情页面
  onKnowledgeItemTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/production-detail/knowledge/detail/detail?id=${id}`
    });
  },

  /**
   * 添加测试鹅群入栏记录
   */
  onAddTestFlock: function() {
    wx.showModal({
      title: '添加测试鹅群',
      content: '将添加一个今天入栏的测试鹅群，并自动生成防疫流程任务',
      success: (res) => {
        if (res.confirm) {
          try {
            const { addTestFlockEntry } = require('../../utils/flock-management.js');
            const result = addTestFlockEntry();
            
            if (result.success) {
              // 重新加载任务列表
              this.refreshPageData();
            } else {
              wx.showToast({
                title: result.message || '添加失败',
                icon: 'error'
              });
            }
          } catch (error) {
            console.error('添加测试鹅群失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'error'
            });
          }
        }
      }
    });
  }
}));
