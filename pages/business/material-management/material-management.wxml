<!--租户级物料管理页面-->
<view class="material-management">
  <!-- 页面标题和统计 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">物料管理</text>
      <text class="page-subtitle">租户专属物料库存管理</text>
    </view>
    <view class="stats-row">
      <view class="stat-item">
        <text class="stat-number">{{stats.totalMaterials}}</text>
        <text class="stat-label">物料种类</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.lowStockCount}}</text>
        <text class="stat-label">库存预警</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.totalValue}}</text>
        <text class="stat-label">总价值(元)</text>
      </view>
    </view>
  </view>

  <!-- 搜索和筛选 -->
  <view class="search-filter-section">
    <view class="search-bar">
      <image class="search-icon" src="/images/icon_search.png"></image>
      <input class="search-input" 
             placeholder="搜索物料名称或品牌" 
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             bindconfirm="onSearch"/>
    </view>
    <view class="filter-tabs">
      <view class="filter-tab {{activeFilter === 'all' ? 'active' : ''}}" 
            bindtap="onFilterChange" data-filter="all">
        全部
      </view>
      <view class="filter-tab {{activeFilter === 'feed' ? 'active' : ''}}" 
            bindtap="onFilterChange" data-filter="feed">
        饲料
      </view>
      <view class="filter-tab {{activeFilter === 'medicine' ? 'active' : ''}}" 
            bindtap="onFilterChange" data-filter="medicine">
        药品
      </view>
      <view class="filter-tab {{activeFilter === 'equipment' ? 'active' : ''}}" 
            bindtap="onFilterChange" data-filter="equipment">
        设备
      </view>
      <view class="filter-tab {{activeFilter === 'low_stock' ? 'active' : ''}}" 
            bindtap="onFilterChange" data-filter="low_stock">
        库存预警
      </view>
    </view>
  </view>

  <!-- 物料列表 -->
  <view class="material-list">
    <view class="material-item" 
          wx:for="{{materialList}}" 
          wx:key="_id"
          bindtap="onMaterialTap" 
          data-id="{{item._id}}">
      
      <!-- 物料基本信息 -->
      <view class="material-header">
        <view class="material-info">
          <view class="material-name">{{item.name}}</view>
          <view class="material-brand">{{item.brand}} | {{item.specification}}</view>
        </view>
        <view class="material-type-badge {{item.material_type}}">
          {{materialTypeMap[item.material_type]}}
        </view>
      </view>

      <!-- 库存信息 -->
      <view class="stock-info">
        <view class="stock-item">
          <text class="stock-label">当前库存</text>
          <text class="stock-value {{item.current_stock <= item.min_stock ? 'warning' : ''}}">
            {{item.current_stock}} {{item.unit}}
          </text>
        </view>
        <view class="stock-item">
          <text class="stock-label">预警线</text>
          <text class="stock-value">{{item.min_stock}} {{item.unit}}</text>
        </view>
        <view class="stock-item">
          <text class="stock-label">单价</text>
          <text class="stock-value">¥{{item.unit_price}}</text>
        </view>
      </view>

      <!-- 存储和过期信息 -->
      <view class="additional-info">
        <view class="info-item">
          <image class="info-icon" src="/images/icon_location.png"></image>
          <text class="info-text">{{item.storage_location}}</text>
        </view>
        <view class="info-item" wx:if="{{item.expiry_date}}">
          <image class="info-icon" src="/images/icon_calendar.png"></image>
          <text class="info-text {{isExpiringSoon(item.expiry_date) ? 'warning' : ''}}">
            {{formatDate(item.expiry_date)}}到期
          </text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="action-btn secondary" 
                bindtap="onEditMaterial" 
                data-id="{{item._id}}"
                data-item="{{item}}">
          编辑
        </button>
        <button class="action-btn primary" 
                bindtap="onStockOperation" 
                data-id="{{item._id}}"
                data-name="{{item.name}}">
          库存操作
        </button>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{materialList.length === 0 && !loading}}">
      <image class="empty-icon" src="/images/empty_material.png"></image>
      <text class="empty-text">暂无物料记录</text>
      <button class="empty-action-btn" bindtap="onAddMaterial">添加第一个物料</button>
    </view>
  </view>

  <!-- 分页加载 -->
  <view class="pagination" wx:if="{{pagination.pages > 1}}">
    <button class="page-btn" 
            bindtap="onPageChange" 
            data-page="{{pagination.page - 1}}"
            disabled="{{!pagination.hasPrev}}">
      上一页
    </button>
    <text class="page-info">
      {{pagination.page}} / {{pagination.pages}}
    </text>
    <button class="page-btn" 
            bindtap="onPageChange" 
            data-page="{{pagination.page + 1}}"
            disabled="{{!pagination.hasNext}}">
      下一页
    </button>
  </view>

  <!-- 浮动添加按钮 -->
  <view class="fab" bindtap="onAddMaterial">
    <image class="fab-icon" src="/images/icon_add.png"></image>
  </view>
</view>

<!-- 库存操作弹窗 -->
<view class="modal-overlay" wx:if="{{showStockModal}}" bindtap="onCloseStockModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">库存操作 - {{selectedMaterial.name}}</text>
      <image class="modal-close" src="/images/icon_close.png" bindtap="onCloseStockModal"></image>
    </view>
    
    <view class="modal-body">
      <view class="current-stock-info">
        <text class="current-stock-label">当前库存：</text>
        <text class="current-stock-value">{{selectedMaterial.current_stock}} {{selectedMaterial.unit}}</text>
      </view>
      
      <view class="operation-tabs">
        <view class="operation-tab {{stockOperation === 'in' ? 'active' : ''}}" 
              bindtap="onStockOperationChange" data-operation="in">
          入库
        </view>
        <view class="operation-tab {{stockOperation === 'out' ? 'active' : ''}}" 
              bindtap="onStockOperationChange" data-operation="out">
          出库
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">{{stockOperation === 'in' ? '入库' : '出库'}}数量</text>
        <input class="form-input" 
               type="number" 
               placeholder="请输入数量"
               value="{{stockQuantity}}"
               bindinput="onStockQuantityInput"/>
        <text class="form-unit">{{selectedMaterial.unit}}</text>
      </view>
      
      <view class="form-group">
        <text class="form-label">操作说明</text>
        <textarea class="form-textarea" 
                  placeholder="请输入操作说明（可选）"
                  value="{{stockRemark}}"
                  bindinput="onStockRemarkInput">
        </textarea>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="modal-btn secondary" bindtap="onCloseStockModal">取消</button>
      <button class="modal-btn primary" bindtap="onConfirmStockOperation">确认操作</button>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-overlay" wx:if="{{loading}}">
  <view class="loading-content">
    <image class="loading-icon" src="/images/loading.gif"></image>
    <text class="loading-text">加载中...</text>
  </view>
</view>
