/**
 * 租户级物料管理页面
 * Tenant-Level Material Management Page
 */

const { withPermissionCheck } = require('../../../utils/route-guard');
const { DataHelper } = require('../../../utils/data-scheduler');
const { getCurrentUserInfo } = require('../../../utils/auth');

Page(withPermissionCheck({
  data: {
    // 基础数据
    materialList: [],
    stats: {
      totalMaterials: 0,
      lowStockCount: 0,
      totalValue: 0
    },
    
    // 搜索和筛选
    searchKeyword: '',
    activeFilter: 'all',
    
    // 分页
    pagination: {
      page: 1,
      limit: 20,
      total: 0,
      pages: 0,
      hasNext: false,
      hasPrev: false
    },
    
    // 状态
    loading: false,
    refreshing: false,
    
    // 物料类型映射
    materialTypeMap: {
      feed: '饲料',
      medicine: '药品',
      equipment: '设备',
      other: '其他'
    },
    
    // 库存操作弹窗
    showStockModal: false,
    selectedMaterial: {},
    stockOperation: 'in', // in: 入库, out: 出库
    stockQuantity: '',
    stockRemark: '',
    
    // 用户信息
    userInfo: null
  },

  onLoad(options) {
    this.initPage();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadMaterialList();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  onReachBottom() {
    this.loadMoreData();
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      const userInfo = await getCurrentUserInfo();
      this.setData({ userInfo });
      
      await this.loadStats();
      await this.loadMaterialList();
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'error'
      });
    }
  },

  /**
   * 加载统计数据
   */
  async loadStats() {
    try {
      const result = await DataHelper.query('tenant_materials', {
        action: 'statistics'
      }, this.data.userInfo);

      if (result.success) {
        this.setData({
          stats: result.data
        });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  /**
   * 加载物料列表
   */
  async loadMaterialList(reset = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const { searchKeyword, activeFilter, pagination } = this.data;
      const page = reset ? 1 : pagination.page;

      // 构建查询条件
      const filters = {};
      if (searchKeyword) {
        filters.keyword = searchKeyword;
      }
      if (activeFilter !== 'all') {
        if (activeFilter === 'low_stock') {
          filters.low_stock = true;
        } else {
          filters.material_type = activeFilter;
        }
      }

      const result = await DataHelper.query('tenant_materials', {
        action: 'list',
        data: { filters },
        page,
        limit: pagination.limit
      }, this.data.userInfo);

      if (result.success) {
        const newList = reset ? result.data.list : [...this.data.materialList, ...result.data.list];
        
        this.setData({
          materialList: newList,
          pagination: result.data.pagination
        });
      } else {
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('加载物料列表失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
      if (this.data.refreshing) {
        wx.stopPullDownRefresh();
        this.setData({ refreshing: false });
      }
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({ refreshing: true });
    await this.loadStats();
    await this.loadMaterialList(true);
  },

  /**
   * 加载更多数据
   */
  async loadMoreData() {
    const { pagination } = this.data;
    if (pagination.hasNext && !this.data.loading) {
      this.setData({
        'pagination.page': pagination.page + 1
      });
      await this.loadMaterialList();
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 执行搜索
   */
  onSearch() {
    this.loadMaterialList(true);
  },

  /**
   * 筛选条件改变
   */
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      activeFilter: filter,
      'pagination.page': 1
    });
    this.loadMaterialList(true);
  },

  /**
   * 分页改变
   */
  onPageChange(e) {
    const page = e.currentTarget.dataset.page;
    this.setData({
      'pagination.page': page
    });
    this.loadMaterialList(true);
  },

  /**
   * 点击物料项
   */
  onMaterialTap(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/business/material-detail/material-detail?id=${id}`
    });
  },

  /**
   * 添加物料
   */
  onAddMaterial() {
    wx.navigateTo({
      url: '/pages/business/material-form/material-form'
    });
  },

  /**
   * 编辑物料
   */
  onEditMaterial(e) {
    e.stopPropagation();
    const { id, item } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/business/material-form/material-form?id=${id}&mode=edit`
    });
  },

  /**
   * 库存操作
   */
  onStockOperation(e) {
    e.stopPropagation();
    const { id, name } = e.currentTarget.dataset;
    const material = this.data.materialList.find(item => item._id === id);
    
    this.setData({
      showStockModal: true,
      selectedMaterial: material,
      stockOperation: 'in',
      stockQuantity: '',
      stockRemark: ''
    });
  },

  /**
   * 关闭库存操作弹窗
   */
  onCloseStockModal() {
    this.setData({
      showStockModal: false,
      selectedMaterial: {},
      stockQuantity: '',
      stockRemark: ''
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 库存操作类型改变
   */
  onStockOperationChange(e) {
    const operation = e.currentTarget.dataset.operation;
    this.setData({
      stockOperation: operation
    });
  },

  /**
   * 库存数量输入
   */
  onStockQuantityInput(e) {
    this.setData({
      stockQuantity: e.detail.value
    });
  },

  /**
   * 库存备注输入
   */
  onStockRemarkInput(e) {
    this.setData({
      stockRemark: e.detail.value
    });
  },

  /**
   * 确认库存操作
   */
  async onConfirmStockOperation() {
    const { selectedMaterial, stockOperation, stockQuantity, stockRemark } = this.data;
    
    if (!stockQuantity || parseFloat(stockQuantity) <= 0) {
      wx.showToast({
        title: '请输入有效数量',
        icon: 'error'
      });
      return;
    }

    // 检查出库数量是否超过库存
    if (stockOperation === 'out' && parseFloat(stockQuantity) > selectedMaterial.current_stock) {
      wx.showToast({
        title: '出库数量不能超过当前库存',
        icon: 'error'
      });
      return;
    }

    try {
      wx.showLoading({ title: '处理中...' });

      const result = await DataHelper.update('tenant_materials', {
        id: selectedMaterial._id,
        action: 'stock_operation',
        operation: stockOperation,
        quantity: parseFloat(stockQuantity),
        remark: stockRemark
      }, this.data.userInfo);

      if (result.success) {
        wx.showToast({
          title: '操作成功',
          icon: 'success'
        });
        
        this.onCloseStockModal();
        this.refreshData();
      } else {
        wx.showToast({
          title: result.message || '操作失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('库存操作失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 格式化日期
   */
  formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  /**
   * 检查是否即将过期
   */
  isExpiringSoon(dateStr) {
    if (!dateStr) return false;
    const expiryDate = new Date(dateStr);
    const now = new Date();
    const diffDays = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
    return diffDays <= 30 && diffDays >= 0; // 30天内过期
  }
}));
