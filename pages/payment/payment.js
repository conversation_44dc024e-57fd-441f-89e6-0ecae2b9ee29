// pages/payment/payment.js
const app = getApp();
const logger = require('../../utils/logger.js');
const { PaymentAPI } = require('../../utils/payment/index.js');

Page({
  data: {
    orderId: '',
    orderInfo: null,
    paymentMethods: [
      {
        id: 'wechat',
        name: '微信支付',
        icon: '/images/icons/wechat-pay.png',
        desc: '安全便捷的微信支付',
        selected: true
      }
    ],
    selectedPayment: 'wechat',
    loading: false,
    paymentLoading: false
  },

  onLoad(options) {
    const orderId = options.orderId;
    if (orderId) {
      this.setData({
        orderId: orderId
      });
      this.loadOrderInfo();
    } else {
      wx.showToast({
        title: '订单信息错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载订单信息
  loadOrderInfo() {
    this.setData({
      loading: true
    });

    // 模拟API调用
    setTimeout(() => {
      const orderInfo = {
        id: this.data.orderId,
        orderNumber: 'GO202401150001',
        createTime: '2024-01-15 10:30',
        totalPrice: '299.98',
        discountAmount: '0.00',
        shippingFee: '0.00',
        actualAmount: '299.98',
        goods: [
          {
            id: '1',
            name: '优质鹅饲料',
            price: '99.99',
            quantity: 1,
            image: '/images/icons/goods1.png'
          },
          {
            id: '2',
            name: '疫苗套装',
            price: '199.99',
            quantity: 1,
            image: '/images/icons/goods2.png'
          }
        ],
        address: {
          name: '张三',
          phone: '138****8888',
          detail: '北京市朝阳区xxx街道xxx号'
        }
      };

      this.setData({
        orderInfo: orderInfo,
        loading: false
      });
    }, 500);
  },

  // 支付方式固定为微信支付，无需选择
  // onSelectPayment 方法已移除，因为只支持微信支付

  // 立即支付
  onPay() {
    if (!this.data.orderInfo) {
      wx.showToast({
        title: '订单信息加载中',
        icon: 'none'
      });
      return;
    }

    this.setData({
      paymentLoading: true
    });

    // 调用微信支付
    this.wechatPay();
  },

  // 微信支付
  async wechatPay() {
    wx.showLoading({
      title: '正在调起支付...'
    });

    try {
      // 构建订单数据
      const orderData = {
        orderId: this.data.orderId,
        amount: this.data.orderInfo.totalAmount,
        description: this.data.orderInfo.description || '智慧养鹅云服务',
        userId: app.globalData.userInfo?.id,
        metadata: {
          orderType: this.data.orderInfo.type || 'product',
          source: 'miniprogram'
        }
      };

      // 使用新的支付系统
      const paymentResult = await PaymentAPI.pay(orderData);

      wx.hideLoading();

      if (paymentResult.success) {
        logger.info && logger.info('支付成功', paymentResult);
        this.paymentSuccess();
      } else {
        // 使用统一错误处理
        const action = await this.handlePaymentError(paymentResult);
        this.handleUserAction(action);
      }

    } catch (error) {
      wx.hideLoading();
      logger.error && logger.error('支付异常', error);
      this.paymentFailed('支付系统异常，请稍后重试');
    } finally {
      this.setData({
        paymentLoading: false
      });
    }
  },

  // 生成随机字符串
  generateNonceStr() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // 支付成功
  paymentSuccess() {
    wx.showToast({
      title: '支付成功',
      icon: 'success',
      duration: 2000
    });

    // 跳转到支付成功页面
    setTimeout(() => {
      wx.redirectTo({
        url: `/pages/shop/order-success?orderId=${this.data.orderId}&type=payment`
      });
    }, 2000);
  },

  // 支付失败
  paymentFailed(message) {
    wx.showModal({
      title: '支付失败',
      content: message || '支付过程中出现错误，请重试',
      showCancel: false,
      confirmText: '确定'
    });

    this.setData({
      paymentLoading: false
    });
  },

  // 处理支付错误
  async handlePaymentError(paymentResult) {
    const { paymentErrorHandler } = require('../../utils/payment/index.js');
    return await paymentErrorHandler.showErrorDialog(paymentResult);
  },

  // 处理用户操作
  handleUserAction(action) {
    switch (action) {
      case 'retry_payment':
        // 重新支付
        this.onPay();
        break;
      case 'recreate_order':
        // 重新创建订单
        this.recreateOrder();
        break;
      case 'view_order':
        // 查看订单
        wx.navigateTo({
          url: `/pages/orders/detail?id=${this.data.orderId}`
        });
        break;
      case 'contact_support':
        // 联系客服
        this.contactSupport();
        break;
      case 'go_back':
      case 'cancel':
        // 返回上一页
        wx.navigateBack();
        break;
      default:
        // 默认返回
        wx.navigateBack();
    }
  },

  // 重新创建订单
  recreateOrder() {
    wx.showModal({
      title: '重新下单',
      content: '将为您重新创建订单，是否继续？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 联系客服
  contactSupport() {
    wx.showModal({
      title: '联系客服',
      content: '如需帮助，请联系客服：400-123-4567',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 返回订单列表
  onBackToOrders() {
    wx.navigateTo({
      url: '/pages/orders/orders'
    });
  }
});
