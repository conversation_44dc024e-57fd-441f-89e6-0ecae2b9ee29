// 云开发管理后台仪表板
const app = getApp()

Page({
  data: {
    // 用户权限
    userRole: '',
    isAdmin: false,
    isSuperAdmin: false,
    
    // 仪表板数据
    stats: {
      user_count: 0,
      flock_count: 0,
      health_record_count: 0,
      order_count: 0,
      active_user_count: 0,
      stats_date: ''
    },
    
    // 快捷管理功能
    adminActions: [
      {
        id: 'users',
        title: '用户管理',
        icon: '/images/icons/users.png',
        url: '/pages/admin/users/users',
        description: '管理用户账户和权限',
        permission: 'admin'
      },
      {
        id: 'tenants',
        title: '租户管理',
        icon: '/images/icons/tenants.png',
        url: '/pages/admin/tenants/tenants',
        description: '管理租户信息和订阅',
        permission: 'super_admin'
      },
      {
        id: 'announcements',
        title: '公告管理',
        icon: '/images/icons/announcement.png',
        url: '/pages/admin/announcements/announcements',
        description: '发布和管理系统公告',
        permission: 'admin'
      },
      {
        id: 'knowledge',
        title: '知识库',
        icon: '/images/icons/knowledge.png',
        url: '/pages/admin/knowledge/knowledge',
        description: '管理知识库内容',
        permission: 'admin'
      },
      {
        id: 'config',
        title: '系统配置',
        icon: '/images/icons/config.png',
        url: '/pages/admin/config/config',
        description: '系统参数和配置管理',
        permission: 'admin'
      },
      {
        id: 'analytics',
        title: '数据分析',
        icon: '/images/icons/analytics.png',
        url: '/pages/admin/analytics/analytics',
        description: '查看系统使用统计',
        permission: 'admin'
      }
    ],
    
    // 待处理事项
    pendingItems: {
      pending_approvals: 0,
      system_alerts: 0,
      new_registrations: 0,
      support_tickets: 0
    },
    
    // 加载状态
    loading: true,
    refreshing: false
  },

  onLoad: function (options) {
    this.checkAdminPermission()
  },

  onShow: function () {
    if (this.data.isAdmin) {
      this.loadDashboardData()
    }
  },

  onPullDownRefresh: function () {
    this.setData({ refreshing: true })
    this.loadDashboardData().finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  // 检查管理员权限
  checkAdminPermission: function() {
    const userInfo = app.globalData.userInfo || wx.getStorageSync('user_info')
    
    if (!userInfo || !userInfo.role) {
      wx.showModal({
        title: '权限不足',
        content: '您没有管理员权限',
        showCancel: false,
        complete: () => {
          wx.navigateBack()
        }
      })
      return
    }
    
    const isAdmin = ['admin', 'super_admin'].includes(userInfo.role)
    const isSuperAdmin = userInfo.role === 'super_admin'
    
    if (!isAdmin) {
      wx.showModal({
        title: '权限不足',
        content: '您没有管理员权限',
        showCancel: false,
        complete: () => {
          wx.navigateBack()
        }
      })
      return
    }
    
    // 根据权限过滤功能菜单
    const filteredActions = this.data.adminActions.filter(action => {
      if (action.permission === 'super_admin') {
        return isSuperAdmin
      }
      return isAdmin
    })
    
    this.setData({
      userRole: userInfo.role,
      isAdmin: isAdmin,
      isSuperAdmin: isSuperAdmin,
      adminActions: filteredActions
    })
  },

  // 加载仪表板数据
  loadDashboardData: async function() {
    try {
      this.setData({ loading: true })
      
      // 调用系统管理云函数获取统计数据
      const result = await wx.cloud.callFunction({
        name: 'systemManagement',
        data: {
          action: 'getSystemStats'
        }
      })
      
      if (result.result.success) {
        this.setData({
          stats: result.result.data
        })
      } else {
        console.error('获取统计数据失败:', result.result.message)
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
      }
      
      // 加载待处理事项
      await this.loadPendingItems()
      
    } catch (error) {
      console.error('加载仪表板数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载待处理事项
  loadPendingItems: async function() {
    try {
      // 这里可以调用多个云函数获取待处理事项
      // 暂时使用模拟数据
      this.setData({
        pendingItems: {
          pending_approvals: 5,
          system_alerts: 2,
          new_registrations: 8,
          support_tickets: 3
        }
      })
    } catch (error) {
      console.error('加载待处理事项失败:', error)
    }
  },

  // 点击快捷功能
  onActionTap: function(e) {
    const action = e.currentTarget.dataset.action
    
    if (action && action.url) {
      wx.navigateTo({
        url: action.url
      })
    }
  },

  // 查看统计详情
  onStatsDetailTap: function(e) {
    const type = e.currentTarget.dataset.type
    
    switch (type) {
      case 'users':
        wx.navigateTo({
          url: '/pages/admin/users/users'
        })
        break
      case 'flocks':
        wx.navigateTo({
          url: '/pages/admin/analytics/analytics?tab=flocks'
        })
        break
      case 'health':
        wx.navigateTo({
          url: '/pages/admin/analytics/analytics?tab=health'
        })
        break
      case 'orders':
        wx.navigateTo({
          url: '/pages/admin/analytics/analytics?tab=orders'
        })
        break
      default:
        wx.navigateTo({
          url: '/pages/admin/analytics/analytics'
        })
    }
  },

  // 查看待处理事项
  onPendingItemTap: function(e) {
    const type = e.currentTarget.dataset.type
    
    switch (type) {
      case 'approvals':
        wx.navigateTo({
          url: '/pages/admin/approvals/approvals'
        })
        break
      case 'alerts':
        wx.navigateTo({
          url: '/pages/admin/alerts/alerts'
        })
        break
      case 'registrations':
        wx.navigateTo({
          url: '/pages/admin/users/users?filter=new'
        })
        break
      case 'tickets':
        wx.navigateTo({
          url: '/pages/admin/support/support'
        })
        break
    }
  },

  // 格式化数字显示
  formatNumber: function(num) {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  },

  // 格式化日期显示
  formatDate: function(dateStr) {
    if (!dateStr) return ''
    
    const date = new Date(dateStr)
    const now = new Date()
    const diff = now - date
    
    if (diff < 60000) { // 1分钟内
      return '刚刚更新'
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前更新'
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前更新'
    } else {
      return date.toLocaleDateString() + ' 更新'
    }
  }
})
