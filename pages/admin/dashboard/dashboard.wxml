<!--云开发管理后台仪表板-->
<view class="admin-dashboard">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">管理后台</text>
      <text class="page-subtitle">系统管理和数据监控</text>
    </view>
    <view class="header-actions">
      <view class="refresh-btn" bindtap="loadDashboardData">
        <image src="/images/icons/refresh.png" class="refresh-icon {{refreshing ? 'rotating' : ''}}"></image>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="dashboard-content">
    <!-- 统计概览 -->
    <view class="stats-section">
      <view class="section-title">数据概览</view>
      <view class="stats-grid">
        <view class="stat-card" bindtap="onStatsDetailTap" data-type="users">
          <view class="stat-icon user-icon"></view>
          <view class="stat-content">
            <text class="stat-number">{{stats.user_count}}</text>
            <text class="stat-label">总用户数</text>
          </view>
          <view class="stat-trend">
            <text class="trend-text">活跃: {{stats.active_user_count}}</text>
          </view>
        </view>

        <view class="stat-card" bindtap="onStatsDetailTap" data-type="flocks">
          <view class="stat-icon flock-icon"></view>
          <view class="stat-content">
            <text class="stat-number">{{stats.flock_count}}</text>
            <text class="stat-label">鹅群数量</text>
          </view>
        </view>

        <view class="stat-card" bindtap="onStatsDetailTap" data-type="health">
          <view class="stat-icon health-icon"></view>
          <view class="stat-content">
            <text class="stat-number">{{stats.health_record_count}}</text>
            <text class="stat-label">健康记录</text>
          </view>
        </view>

        <view class="stat-card" bindtap="onStatsDetailTap" data-type="orders">
          <view class="stat-icon order-icon"></view>
          <view class="stat-content">
            <text class="stat-number">{{stats.order_count}}</text>
            <text class="stat-label">订单总数</text>
          </view>
        </view>
      </view>
      
      <view class="stats-update">
        <text class="update-time">{{formatDate(stats.stats_date)}}</text>
      </view>
    </view>

    <!-- 待处理事项 -->
    <view class="pending-section">
      <view class="section-title">待处理事项</view>
      <view class="pending-list">
        <view class="pending-item" bindtap="onPendingItemTap" data-type="approvals">
          <view class="pending-icon approval-icon"></view>
          <view class="pending-content">
            <text class="pending-title">待审批</text>
            <text class="pending-desc">需要处理的审批申请</text>
          </view>
          <view class="pending-badge" wx:if="{{pendingItems.pending_approvals > 0}}">
            <text class="badge-text">{{pendingItems.pending_approvals}}</text>
          </view>
        </view>

        <view class="pending-item" bindtap="onPendingItemTap" data-type="alerts">
          <view class="pending-icon alert-icon"></view>
          <view class="pending-content">
            <text class="pending-title">系统告警</text>
            <text class="pending-desc">需要关注的系统异常</text>
          </view>
          <view class="pending-badge warning" wx:if="{{pendingItems.system_alerts > 0}}">
            <text class="badge-text">{{pendingItems.system_alerts}}</text>
          </view>
        </view>

        <view class="pending-item" bindtap="onPendingItemTap" data-type="registrations">
          <view class="pending-icon user-icon"></view>
          <view class="pending-content">
            <text class="pending-title">新用户注册</text>
            <text class="pending-desc">最近注册的新用户</text>
          </view>
          <view class="pending-badge success" wx:if="{{pendingItems.new_registrations > 0}}">
            <text class="badge-text">{{pendingItems.new_registrations}}</text>
          </view>
        </view>

        <view class="pending-item" bindtap="onPendingItemTap" data-type="tickets">
          <view class="pending-icon support-icon"></view>
          <view class="pending-content">
            <text class="pending-title">支持工单</text>
            <text class="pending-desc">用户反馈和技术支持</text>
          </view>
          <view class="pending-badge" wx:if="{{pendingItems.support_tickets > 0}}">
            <text class="badge-text">{{pendingItems.support_tickets}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷管理功能 -->
    <view class="actions-section">
      <view class="section-title">管理功能</view>
      <view class="actions-grid">
        <view 
          wx:for="{{adminActions}}" 
          wx:key="id" 
          class="action-card" 
          bindtap="onActionTap" 
          data-action="{{item}}"
        >
          <view class="action-icon">
            <image src="{{item.icon}}" class="icon-image"></image>
          </view>
          <view class="action-content">
            <text class="action-title">{{item.title}}</text>
            <text class="action-desc">{{item.description}}</text>
          </view>
          <view class="action-arrow">
            <image src="/images/icons/arrow-right.png" class="arrow-icon"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 系统信息 -->
    <view class="system-info">
      <view class="info-item">
        <text class="info-label">当前角色:</text>
        <text class="info-value">{{userRole === 'super_admin' ? '超级管理员' : '管理员'}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">系统版本:</text>
        <text class="info-value">云开发版 v1.0.0</text>
      </view>
    </view>
  </view>
</view>
