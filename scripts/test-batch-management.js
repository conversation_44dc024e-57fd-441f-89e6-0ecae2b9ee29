/**
 * BatchManagement 功能测试脚本
 * 验证 getStatusLabel 方法是否正常工作
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: (key) => {
    const mockData = {
      'active_batches': [
        {
          id: 'batch_001',
          batchNumber: 'QY-20240301-001',
          breed: '太湖鹅',
          status: 'growing',
          entryDate: '2024-03-01',
          initialCount: 500,
          currentCount: 485,
          dayAge: 45,
          averageWeight: 2.8
        }
      ]
    };
    return mockData[key] || [];
  },
  setStorageSync: (key, data) => {
    console.log(`设置存储: ${key}`, data);
  },
  removeStorageSync: (key) => {
    console.log(`移除存储: ${key}`);
  }
};

class BatchManagementTester {
  constructor() {
    this.testResults = [];
    this.errors = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始 BatchManagement 功能测试...');
    
    // 1. 测试模块导入
    await this.testModuleImport();
    
    // 2. 测试静态方法
    await this.testStaticMethods();
    
    // 3. 测试实例方法
    await this.testInstanceMethods();
    
    // 4. 测试活跃批次获取
    await this.testGetActiveBatches();
    
    // 5. 生成测试报告
    this.generateTestReport();
    
    console.log('✅ BatchManagement 功能测试完成！');
  }

  /**
   * 测试模块导入
   */
  async testModuleImport() {
    console.log('📦 测试模块导入...');
    
    try {
      const { BatchManagement, BatchManagementClass } = require('../utils/business/batch-management.js');
      
      if (BatchManagement && BatchManagementClass) {
        this.testResults.push('✅ 模块导入成功');
        
        // 验证实例和类的类型
        if (typeof BatchManagement === 'object') {
          this.testResults.push('✅ BatchManagement 实例类型正确');
        }
        
        if (typeof BatchManagementClass === 'function') {
          this.testResults.push('✅ BatchManagementClass 类型正确');
        }
        
        return { BatchManagement, BatchManagementClass };
      } else {
        this.errors.push('❌ 模块导入失败：缺少导出对象');
        return null;
      }
    } catch (error) {
      this.errors.push(`❌ 模块导入失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 测试静态方法
   */
  async testStaticMethods() {
    console.log('🔧 测试静态方法...');
    
    try {
      const { BatchManagementClass } = require('../utils/business/batch-management.js');
      
      // 测试 getStatusLabel 静态方法
      const testStatuses = ['active', 'growing', 'ready_for_slaughter', 'completed', 'suspended', 'unknown'];
      
      for (const status of testStatuses) {
        const label = BatchManagementClass.getStatusLabel(status);
        
        if (typeof label === 'string' && label.length > 0) {
          this.testResults.push(`✅ getStatusLabel('${status}') = '${label}'`);
        } else {
          this.errors.push(`❌ getStatusLabel('${status}') 返回无效结果: ${label}`);
        }
      }
      
    } catch (error) {
      this.errors.push(`❌ 静态方法测试失败: ${error.message}`);
    }
  }

  /**
   * 测试实例方法
   */
  async testInstanceMethods() {
    console.log('⚙️ 测试实例方法...');
    
    try {
      const { BatchManagement } = require('../utils/business/batch-management.js');
      
      // 测试 getActiveBatches 方法
      const activeBatches = await BatchManagement.getActiveBatches();
      
      if (Array.isArray(activeBatches)) {
        this.testResults.push(`✅ getActiveBatches 返回数组，长度: ${activeBatches.length}`);
        
        // 检查批次对象结构
        if (activeBatches.length > 0) {
          const batch = activeBatches[0];
          const requiredFields = ['id', 'batchNumber', 'breed', 'status', 'currentCount'];
          
          const missingFields = requiredFields.filter(field => !(field in batch));
          if (missingFields.length === 0) {
            this.testResults.push('✅ 批次对象结构完整');
          } else {
            this.errors.push(`❌ 批次对象缺少字段: ${missingFields.join(', ')}`);
          }
        }
      } else {
        this.errors.push(`❌ getActiveBatches 返回类型错误: ${typeof activeBatches}`);
      }
      
    } catch (error) {
      this.errors.push(`❌ 实例方法测试失败: ${error.message}`);
    }
  }

  /**
   * 测试活跃批次获取（模拟页面调用）
   */
  async testGetActiveBatches() {
    console.log('📋 测试活跃批次获取（模拟页面调用）...');
    
    try {
      const { BatchManagement, BatchManagementClass } = require('../utils/business/batch-management.js');
      
      // 模拟页面中的调用逻辑
      const activeBatches = await BatchManagement.getActiveBatches();
      
      const batchOptions = activeBatches.map(batch => ({
        value: batch.batchNumber,
        label: `${batch.batchNumber} (${batch.breed}, ${batch.currentCount}只)`,
        batchNumber: batch.batchNumber,
        breed: batch.breed,
        currentCount: batch.currentCount,
        entryDate: batch.entryDate,
        avgWeight: batch.averageWeight > 0 ? `${batch.averageWeight}kg` : '未称重',
        status: batch.status,
        statusLabel: BatchManagementClass.getStatusLabel(batch.status) // 修复后的调用
      }));
      
      if (batchOptions.length > 0) {
        this.testResults.push(`✅ 批次选项生成成功，数量: ${batchOptions.length}`);
        
        // 检查第一个选项的结构
        const firstOption = batchOptions[0];
        if (firstOption.statusLabel && typeof firstOption.statusLabel === 'string') {
          this.testResults.push(`✅ statusLabel 生成成功: '${firstOption.statusLabel}'`);
        } else {
          this.errors.push(`❌ statusLabel 生成失败: ${firstOption.statusLabel}`);
        }
      } else {
        this.testResults.push('⚠️ 没有活跃批次数据');
      }
      
    } catch (error) {
      this.errors.push(`❌ 活跃批次获取测试失败: ${error.message}`);
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    const totalTests = this.testResults.length + this.errors.length;
    const passedTests = this.testResults.length;
    const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
    
    const report = `# BatchManagement 功能测试报告

## 测试概览
- 总测试项: ${totalTests}
- 通过测试: ${passedTests}
- 成功率: ${successRate}%
- 错误数量: ${this.errors.length}

## ✅ 通过的测试
${this.testResults.map(result => `- ${result}`).join('\n')}

## ❌ 失败的测试
${this.errors.length > 0 ? 
  this.errors.map(error => `- ${error}`).join('\n') : 
  '- 无失败测试'}

## 修复验证

### 问题描述
原始错误: \`BatchManagement.getStatusLabel is not a function\`

### 修复方案
1. **导入修复**: 在 \`record-add.js\` 中同时导入实例和类
   \`\`\`javascript
   const { BatchManagement, BatchManagementClass } = require('../../../utils/business/batch-management.js');
   \`\`\`

2. **调用修复**: 使用类名调用静态方法
   \`\`\`javascript
   statusLabel: BatchManagementClass.getStatusLabel(batch.status)
   \`\`\`

3. **内部调用修复**: 在类内部使用 \`this.constructor.getStatusLabel\`

### 测试结果
${successRate >= 90 ? '🎉 修复成功！所有功能正常工作' : 
  successRate >= 70 ? '⚠️ 大部分功能正常，建议检查剩余问题' : 
  '❌ 仍存在问题，需要进一步修复'}

## 使用建议
1. 静态方法调用使用类名: \`BatchManagementClass.getStatusLabel()\`
2. 实例方法调用使用实例: \`BatchManagement.getActiveBatches()\`
3. 在类内部调用静态方法使用: \`this.constructor.getStatusLabel()\`

---
测试时间: ${new Date().toLocaleString()}
测试环境: Node.js 模拟环境
`;

    const fs = require('fs');
    const path = require('path');
    
    fs.writeFileSync(
      path.join(process.cwd(), 'docs/batch-management-test-report.md'),
      report
    );
    
    console.log('\n📊 BatchManagement 测试报告:');
    console.log(`成功率: ${successRate}%`);
    console.log(`通过测试: ${passedTests}/${totalTests}`);
    console.log(`失败测试: ${this.errors.length}个`);
    console.log('详细报告已保存到: docs/batch-management-test-report.md');
    
    if (successRate >= 90) {
      console.log('🎉 恭喜！BatchManagement 功能测试通过！');
    } else if (successRate >= 70) {
      console.log('⚠️ 大部分功能正常，建议检查剩余问题');
    } else {
      console.log('❌ 仍存在问题，需要进一步修复');
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new BatchManagementTester();
  tester.runAllTests().catch(console.error);
}

module.exports = BatchManagementTester;
