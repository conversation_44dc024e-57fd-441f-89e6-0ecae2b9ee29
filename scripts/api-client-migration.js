/**
 * API客户端迁移脚本 - 第四阶段部署
 * API Client Migration Script - Phase 4 Deployment
 * 
 * 将项目中所有API调用迁移到ultimate-api-client.js
 */

const fs = require('fs');
const path = require('path');

/**
 * API客户端迁移器
 */
class APIClientMigrator {
  constructor(rootPath = process.cwd()) {
    this.rootPath = rootPath;
    this.migrationResults = [];
    this.backupDir = path.join(rootPath, '.migration-backup');
    
    // 需要迁移的文件模式
    this.targetFiles = [
      'pages/**/*.js',
      'components/**/*.js',
      'utils/**/*.js'
    ];
    
    // 旧API客户端导入模式
    this.oldImportPatterns = [
      /const\s+{\s*([^}]+)\s*}\s*=\s*require\(['"`].*\/api\.js['"`]\)/g,
      /const\s+{\s*([^}]+)\s*}\s*=\s*require\(['"`].*\/api-client-unified\.js['"`]\)/g,
      /const\s+{\s*([^}]+)\s*}\s*=\s*require\(['"`].*\/api-client-final\.js['"`]\)/g,
      /const\s+{\s*([^}]+)\s*}\s*=\s*require\(['"`].*\/request\.js['"`]\)/g,
      /const\s+api\s*=\s*require\(['"`].*\/api\.js['"`]\)/g
    ];
    
    // API调用模式映射
    this.apiCallMappings = new Map([
      // 认证相关
      ['auth.login', 'ultimateAPIClient.businessAPI.auth.login'],
      ['auth.logout', 'ultimateAPIClient.businessAPI.auth.logout'],
      ['auth.getUserInfo', 'ultimateAPIClient.businessAPI.auth.getUserInfo'],
      
      // 首页相关
      ['home.getHomeData', 'ultimateAPIClient.businessAPI.home.getHomeData'],
      ['home.getAnnouncements', 'ultimateAPIClient.businessAPI.home.getAnnouncements'],
      
      // 生产相关
      ['production.getRecords', 'ultimateAPIClient.businessAPI.flocks.getList'],
      ['production.createRecord', 'ultimateAPIClient.businessAPI.flocks.create'],
      
      // 健康相关
      ['health.getRecords', 'ultimateAPIClient.businessAPI.health.getRecords'],
      ['health.createRecord', 'ultimateAPIClient.businessAPI.health.createRecord'],
      
      // 物料相关
      ['material.getList', 'ultimateAPIClient.businessAPI.materials.getList'],
      ['material.create', 'ultimateAPIClient.businessAPI.materials.create'],
      
      // 财务相关
      ['finance.getRecords', 'ultimateAPIClient.businessAPI.finance.getRecords'],
      ['finance.createRecord', 'ultimateAPIClient.businessAPI.finance.createRecord'],
      
      // 商城相关
      ['shop.getProducts', 'ultimateAPIClient.businessAPI.shop.getProducts'],
      ['shop.getProductDetail', 'ultimateAPIClient.businessAPI.shop.getProductDetail'],
      
      // 用户相关
      ['profile.getSettings', 'ultimateAPIClient.businessAPI.profile.getSettings'],
      ['profile.updateSettings', 'ultimateAPIClient.businessAPI.profile.updateSettings']
    ]);
  }

  /**
   * 执行完整迁移
   */
  async migrate() {
    console.log('🚀 开始API客户端迁移...\n');

    try {
      // 1. 创建备份
      await this.createBackup();

      // 2. 扫描需要迁移的文件
      const filesToMigrate = await this.scanFiles();
      console.log(`📁 发现 ${filesToMigrate.length} 个需要迁移的文件\n`);

      // 3. 执行迁移
      for (const filePath of filesToMigrate) {
        await this.migrateFile(filePath);
      }

      // 4. 生成迁移报告
      this.generateMigrationReport();

      // 5. 验证迁移结果
      await this.validateMigration();

      console.log('✅ API客户端迁移完成！');

    } catch (error) {
      console.error('❌ 迁移失败:', error);
      await this.rollback();
    }
  }

  /**
   * 创建备份
   */
  async createBackup() {
    console.log('📦 创建迁移备份...');

    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.backupDir, `api-migration-${timestamp}`);
    
    // 备份关键文件
    const filesToBackup = [
      'utils/api.js',
      'utils/api-client-unified.js',
      'utils/api-client-final.js',
      'utils/request.js'
    ];

    for (const file of filesToBackup) {
      const sourcePath = path.join(this.rootPath, file);
      if (fs.existsSync(sourcePath)) {
        const backupFilePath = path.join(backupPath, file);
        const backupFileDir = path.dirname(backupFilePath);
        
        if (!fs.existsSync(backupFileDir)) {
          fs.mkdirSync(backupFileDir, { recursive: true });
        }
        
        fs.copyFileSync(sourcePath, backupFilePath);
      }
    }

    console.log(`✅ 备份已创建: ${backupPath}\n`);
  }

  /**
   * 扫描需要迁移的文件
   */
  async scanFiles() {
    const filesToMigrate = [];

    const scanDirectory = (dir) => {
      const files = fs.readdirSync(dir);
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
          scanDirectory(filePath);
        } else if (file.endsWith('.js') && this.shouldMigrateFile(filePath)) {
          filesToMigrate.push(filePath);
        }
      }
    };

    // 扫描主要目录
    const dirsToScan = ['pages', 'components', 'utils'];
    for (const dir of dirsToScan) {
      const dirPath = path.join(this.rootPath, dir);
      if (fs.existsSync(dirPath)) {
        scanDirectory(dirPath);
      }
    }

    return filesToMigrate;
  }

  /**
   * 判断文件是否需要迁移
   */
  shouldMigrateFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否包含旧API客户端的导入
      const hasOldImports = this.oldImportPatterns.some(pattern => pattern.test(content));
      
      // 检查是否包含API调用
      const hasApiCalls = Array.from(this.apiCallMappings.keys()).some(oldCall => 
        content.includes(oldCall)
      );

      return hasOldImports || hasApiCalls;
    } catch (error) {
      console.warn(`⚠️  无法读取文件 ${filePath}:`, error.message);
      return false;
    }
  }

  /**
   * 迁移单个文件
   */
  async migrateFile(filePath) {
    console.log(`🔄 迁移文件: ${path.relative(this.rootPath, filePath)}`);

    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;

      // 1. 替换导入语句
      const newImport = "const { ultimateAPIClient, businessAPI } = require('../../utils/ultimate-api-client');";
      
      for (const pattern of this.oldImportPatterns) {
        if (pattern.test(content)) {
          content = content.replace(pattern, newImport);
          modified = true;
        }
      }

      // 2. 替换API调用
      for (const [oldCall, newCall] of this.apiCallMappings) {
        const oldCallRegex = new RegExp(`\\b${oldCall.replace('.', '\\.')}\\b`, 'g');
        if (oldCallRegex.test(content)) {
          content = content.replace(oldCallRegex, newCall);
          modified = true;
        }
      }

      // 3. 替换直接的wx.request调用
      content = this.replaceDirectWxRequests(content);

      // 4. 替换云函数调用
      content = this.replaceCloudFunctionCalls(content);

      // 5. 保存修改后的文件
      if (modified) {
        fs.writeFileSync(filePath, content);
        
        this.migrationResults.push({
          file: path.relative(this.rootPath, filePath),
          status: 'success',
          changes: this.getChangeSummary(content)
        });
        
        console.log(`  ✅ 迁移成功`);
      } else {
        console.log(`  ⏭️  无需修改`);
      }

    } catch (error) {
      console.error(`  ❌ 迁移失败:`, error.message);
      
      this.migrationResults.push({
        file: path.relative(this.rootPath, filePath),
        status: 'error',
        error: error.message
      });
    }
  }

  /**
   * 替换直接的wx.request调用
   */
  replaceDirectWxRequests(content) {
    // 替换简单的wx.request调用
    const wxRequestPattern = /wx\.request\(\s*{\s*url:\s*['"`]([^'"`]+)['"`]\s*,\s*method:\s*['"`](\w+)['"`]\s*,?\s*data:\s*([^,}]+)?\s*,?\s*success:\s*([^,}]+)\s*,?\s*fail:\s*([^}]+)\s*}\s*\)/g;
    
    return content.replace(wxRequestPattern, (match, url, method, data, success, fail) => {
      const methodLower = method.toLowerCase();
      const dataParam = data ? `, ${data}` : '';
      
      return `ultimateAPIClient.${methodLower}('${url}'${dataParam}).then(${success}).catch(${fail})`;
    });
  }

  /**
   * 替换云函数调用
   */
  replaceCloudFunctionCalls(content) {
    // 替换wx.cloud.callFunction调用
    const cloudFunctionPattern = /wx\.cloud\.callFunction\(\s*{\s*name:\s*['"`]([^'"`]+)['"`]\s*,\s*data:\s*([^,}]+)\s*,?\s*success:\s*([^,}]+)\s*,?\s*fail:\s*([^}]+)\s*}\s*\)/g;
    
    return content.replace(cloudFunctionPattern, (match, functionName, data, success, fail) => {
      // 根据云函数名称映射到对应的API调用
      const apiMapping = this.getCloudFunctionAPIMapping(functionName);
      
      if (apiMapping) {
        return `${apiMapping}(${data}).then(${success}).catch(${fail})`;
      }
      
      return match; // 如果没有映射，保持原样
    });
  }

  /**
   * 获取云函数到API的映射
   */
  getCloudFunctionAPIMapping(functionName) {
    const mappings = {
      'auth': 'ultimateAPIClient.businessAPI.auth.login',
      'health': 'ultimateAPIClient.businessAPI.health.getRecords',
      'production': 'ultimateAPIClient.businessAPI.flocks.getList',
      'materialManagement': 'ultimateAPIClient.businessAPI.materials.getList',
      'financeManagement': 'ultimateAPIClient.businessAPI.finance.getRecords'
    };
    
    return mappings[functionName];
  }

  /**
   * 获取变更摘要
   */
  getChangeSummary(content) {
    const changes = [];
    
    if (content.includes('ultimate-api-client')) {
      changes.push('导入统一API客户端');
    }
    
    if (content.includes('ultimateAPIClient.businessAPI')) {
      changes.push('使用业务API模块');
    }
    
    return changes;
  }

  /**
   * 生成迁移报告
   */
  generateMigrationReport() {
    console.log('\n📊 迁移报告:');
    console.log('='.repeat(50));

    const successCount = this.migrationResults.filter(r => r.status === 'success').length;
    const errorCount = this.migrationResults.filter(r => r.status === 'error').length;

    console.log(`总文件数: ${this.migrationResults.length}`);
    console.log(`成功迁移: ${successCount}`);
    console.log(`迁移失败: ${errorCount}`);
    console.log(`成功率: ${((successCount / this.migrationResults.length) * 100).toFixed(1)}%`);

    if (errorCount > 0) {
      console.log('\n❌ 失败的文件:');
      this.migrationResults
        .filter(r => r.status === 'error')
        .forEach(result => {
          console.log(`  ${result.file}: ${result.error}`);
        });
    }

    // 保存详细报告
    const reportPath = path.join(this.rootPath, 'api-migration-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: {
        total: this.migrationResults.length,
        success: successCount,
        error: errorCount,
        successRate: ((successCount / this.migrationResults.length) * 100).toFixed(1) + '%'
      },
      details: this.migrationResults
    }, null, 2));

    console.log(`\n📋 详细报告已保存: ${reportPath}`);
  }

  /**
   * 验证迁移结果
   */
  async validateMigration() {
    console.log('\n🔍 验证迁移结果...');

    const validationResults = [];

    for (const result of this.migrationResults) {
      if (result.status === 'success') {
        const filePath = path.join(this.rootPath, result.file);
        const isValid = await this.validateFile(filePath);
        
        validationResults.push({
          file: result.file,
          valid: isValid
        });
      }
    }

    const validCount = validationResults.filter(r => r.valid).length;
    console.log(`✅ 验证通过: ${validCount}/${validationResults.length} 个文件`);

    if (validCount < validationResults.length) {
      console.log('\n⚠️  验证失败的文件:');
      validationResults
        .filter(r => !r.valid)
        .forEach(result => {
          console.log(`  ${result.file}`);
        });
    }
  }

  /**
   * 验证单个文件
   */
  async validateFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否还有旧的API导入
      const hasOldImports = this.oldImportPatterns.some(pattern => pattern.test(content));
      
      // 检查是否有新的API导入
      const hasNewImport = content.includes('ultimate-api-client');
      
      return !hasOldImports && hasNewImport;
    } catch (error) {
      return false;
    }
  }

  /**
   * 回滚迁移
   */
  async rollback() {
    console.log('🔄 回滚迁移...');
    
    // 从备份恢复文件
    // 这里可以实现具体的回滚逻辑
    
    console.log('✅ 回滚完成');
  }

  /**
   * 清理旧文件
   */
  async cleanupOldFiles() {
    console.log('🧹 清理旧API客户端文件...');

    const oldFiles = [
      'utils/api-client-unified.js',
      'utils/api-client-final.js',
      'utils/request.js'
    ];

    for (const file of oldFiles) {
      const filePath = path.join(this.rootPath, file);
      if (fs.existsSync(filePath)) {
        // 重命名为.deprecated而不是直接删除
        const deprecatedPath = filePath + '.deprecated';
        fs.renameSync(filePath, deprecatedPath);
        console.log(`  📦 ${file} -> ${file}.deprecated`);
      }
    }

    console.log('✅ 旧文件清理完成');
  }
}

// 导出迁移器
module.exports = APIClientMigrator;

// 如果直接运行此脚本
if (require.main === module) {
  const migrator = new APIClientMigrator();
  
  migrator.migrate().then(() => {
    console.log('\n🎉 API客户端迁移完成！');
    
    // 询问是否清理旧文件
    console.log('\n是否清理旧的API客户端文件？(y/n)');
    process.stdin.once('data', (data) => {
      const input = data.toString().trim().toLowerCase();
      if (input === 'y' || input === 'yes') {
        migrator.cleanupOldFiles().then(() => {
          process.exit(0);
        });
      } else {
        process.exit(0);
      }
    });
  }).catch((error) => {
    console.error('❌ 迁移失败:', error);
    process.exit(1);
  });
}
