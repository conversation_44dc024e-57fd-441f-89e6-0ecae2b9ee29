/**
 * API客户端部署验证测试 - 第四阶段
 * API Client Deployment Verification Test - Phase 4
 * 
 * 验证ultimate-api-client.js的部署和集成效果
 */

/**
 * API客户端部署测试类
 */
class APIClientDeploymentTest {
  constructor() {
    this.testResults = [];
    this.testStartTime = Date.now();
  }

  /**
   * 运行所有部署测试
   */
  async runAllTests() {
    console.log('🚀 开始API客户端部署验证测试...\n');

    try {
      // 1. 模块导入测试
      await this.testModuleImports();

      // 2. API客户端初始化测试
      await this.testAPIClientInitialization();

      // 3. 业务API模块测试
      await this.testBusinessAPIModules();

      // 4. 兼容性测试
      await this.testBackwardCompatibility();

      // 5. 性能测试
      await this.testPerformance();

      // 6. 错误处理测试
      await this.testErrorHandling();

      // 输出测试结果
      this.printTestResults();

    } catch (error) {
      console.error('❌ 部署验证测试失败:', error);
    }
  }

  /**
   * 模块导入测试
   */
  async testModuleImports() {
    console.log('📋 测试1: 模块导入测试');

    // 测试ultimate-api-client导入
    this.addTest('ultimate-api-client模块导入', () => {
      try {
        const { ultimateAPIClient, businessAPI } = require('../utils/ultimate-api-client');
        return ultimateAPIClient && businessAPI && typeof ultimateAPIClient.request === 'function';
      } catch (error) {
        console.error('导入失败:', error);
        return false;
      }
    });

    // 测试增强权限管理器导入
    this.addTest('enhanced-permission-manager模块导入', () => {
      try {
        const { permissionManager } = require('../utils/enhanced-permission-manager');
        return permissionManager && typeof permissionManager.checkPermission === 'function';
      } catch (error) {
        console.error('导入失败:', error);
        return false;
      }
    });

    // 测试统一配置管理器导入
    this.addTest('unified-config-manager模块导入', () => {
      try {
        const { configManager } = require('../utils/unified-config-manager');
        return configManager && typeof configManager.get === 'function';
      } catch (error) {
        console.error('导入失败:', error);
        return false;
      }
    });
  }

  /**
   * API客户端初始化测试
   */
  async testAPIClientInitialization() {
    console.log('📋 测试2: API客户端初始化测试');

    const { ultimateAPIClient } = require('../utils/ultimate-api-client');

    // 测试客户端实例
    this.addTest('API客户端实例化', () => {
      return ultimateAPIClient && 
             typeof ultimateAPIClient.get === 'function' &&
             typeof ultimateAPIClient.post === 'function' &&
             typeof ultimateAPIClient.put === 'function' &&
             typeof ultimateAPIClient.delete === 'function';
    });

    // 测试配置获取
    this.addTest('API客户端配置', () => {
      const stats = ultimateAPIClient.getStats();
      return stats && typeof stats.totalRequests === 'number';
    });

    // 测试缓存功能
    this.addTest('API客户端缓存', () => {
      ultimateAPIClient.clearCache();
      const stats = ultimateAPIClient.getStats();
      return stats.cacheSize !== undefined;
    });
  }

  /**
   * 业务API模块测试
   */
  async testBusinessAPIModules() {
    console.log('📋 测试3: 业务API模块测试');

    const { businessAPI } = require('../utils/ultimate-api-client');

    // 测试认证模块
    this.addTest('认证API模块', () => {
      return businessAPI.auth &&
             typeof businessAPI.auth.login === 'function' &&
             typeof businessAPI.auth.getUserInfo === 'function';
    });

    // 测试首页模块
    this.addTest('首页API模块', () => {
      return businessAPI.home &&
             typeof businessAPI.home.getHomeData === 'function' &&
             typeof businessAPI.home.getAnnouncements === 'function';
    });

    // 测试健康模块
    this.addTest('健康API模块', () => {
      return businessAPI.health &&
             typeof businessAPI.health.getRecords === 'function' &&
             typeof businessAPI.health.createRecord === 'function';
    });

    // 测试物料模块
    this.addTest('物料API模块', () => {
      return businessAPI.materials &&
             typeof businessAPI.materials.getList === 'function' &&
             typeof businessAPI.materials.create === 'function';
    });

    // 测试财务模块
    this.addTest('财务API模块', () => {
      return businessAPI.finance &&
             typeof businessAPI.finance.getRecords === 'function' &&
             typeof businessAPI.finance.createRecord === 'function';
    });

    // 测试工具模块
    this.addTest('工具API模块', () => {
      return businessAPI.utils &&
             typeof businessAPI.utils.batchRequest === 'function' &&
             typeof businessAPI.utils.clearCache === 'function';
    });
  }

  /**
   * 兼容性测试
   */
  async testBackwardCompatibility() {
    console.log('📋 测试4: 向后兼容性测试');

    // 测试旧API导入兼容性
    this.addTest('旧API导入兼容性', () => {
      try {
        // 模拟旧的导入方式
        const { ultimateAPIClient: apiClient } = require('../utils/ultimate-api-client');
        
        // 检查是否支持旧的调用方式
        return typeof apiClient.get === 'function' &&
               typeof apiClient.post === 'function';
      } catch (error) {
        return false;
      }
    });

    // 测试API调用格式兼容性
    this.addTest('API调用格式兼容性', async () => {
      try {
        const { ultimateAPIClient } = require('../utils/ultimate-api-client');
        
        // 模拟旧的调用格式
        const mockResponse = await ultimateAPIClient.get('/test', {
          data: { test: true },
          cache: false
        });
        
        // 应该返回统一格式的响应
        return true; // 如果没有抛出异常就算通过
      } catch (error) {
        // 网络错误是正常的，只要不是格式错误
        return !error.message.includes('format') && !error.message.includes('syntax');
      }
    });
  }

  /**
   * 性能测试
   */
  async testPerformance() {
    console.log('📋 测试5: 性能测试');

    const { ultimateAPIClient } = require('../utils/ultimate-api-client');

    // 测试缓存性能
    this.addTest('缓存性能测试', async () => {
      const iterations = 100;
      const testUrl = '/test-cache-performance';
      
      // 第一次调用（建立缓存）
      const startTime1 = Date.now();
      for (let i = 0; i < iterations; i++) {
        try {
          await ultimateAPIClient.get(testUrl, { cache: true });
        } catch (error) {
          // 忽略网络错误
        }
      }
      const time1 = Date.now() - startTime1;

      // 第二次调用（使用缓存）
      const startTime2 = Date.now();
      for (let i = 0; i < iterations; i++) {
        try {
          await ultimateAPIClient.get(testUrl, { cache: true });
        } catch (error) {
          // 忽略网络错误
        }
      }
      const time2 = Date.now() - startTime2;

      console.log(`  首次调用: ${time1}ms, 缓存调用: ${time2}ms`);
      
      // 缓存调用应该更快（或至少不慢太多）
      return time2 <= time1 * 1.5;
    });

    // 测试批量请求性能
    this.addTest('批量请求性能测试', async () => {
      const requests = [
        { url: '/test1', method: 'GET' },
        { url: '/test2', method: 'GET' },
        { url: '/test3', method: 'GET' }
      ];

      try {
        const startTime = Date.now();
        const result = await ultimateAPIClient.batch(requests, { concurrency: 2 });
        const duration = Date.now() - startTime;

        console.log(`  批量请求耗时: ${duration}ms`);
        
        // 检查返回格式
        return result && 
               typeof result.successCount === 'number' &&
               typeof result.errorCount === 'number' &&
               Array.isArray(result.results);
      } catch (error) {
        // 网络错误是正常的
        return true;
      }
    });
  }

  /**
   * 错误处理测试
   */
  async testErrorHandling() {
    console.log('📋 测试6: 错误处理测试');

    const { ultimateAPIClient } = require('../utils/ultimate-api-client');

    // 测试网络错误处理
    this.addTest('网络错误处理', async () => {
      try {
        await ultimateAPIClient.get('/non-existent-endpoint');
        return false; // 应该抛出错误
      } catch (error) {
        // 应该有友好的错误信息
        return error.message && error.message.length > 0;
      }
    });

    // 测试参数验证
    this.addTest('参数验证', async () => {
      try {
        await ultimateAPIClient.get(''); // 空URL
        return false; // 应该抛出错误
      } catch (error) {
        return error.message.includes('URL') || error.message.includes('url');
      }
    });

    // 测试超时处理
    this.addTest('超时处理', async () => {
      try {
        await ultimateAPIClient.get('/slow-endpoint', { timeout: 1 }); // 1ms超时
        return false; // 应该超时
      } catch (error) {
        return error.message.includes('timeout') || error.message.includes('超时');
      }
    });
  }

  /**
   * 添加测试用例
   */
  addTest(name, testFunction) {
    try {
      const result = testFunction();
      
      if (result instanceof Promise) {
        result.then(success => {
          this.testResults.push({
            name,
            success,
            error: null,
            duration: Date.now() - this.testStartTime
          });
        }).catch(error => {
          this.testResults.push({
            name,
            success: false,
            error: error.message,
            duration: Date.now() - this.testStartTime
          });
        });
      } else {
        this.testResults.push({
          name,
          success: result,
          error: null,
          duration: Date.now() - this.testStartTime
        });
      }
    } catch (error) {
      this.testResults.push({
        name,
        success: false,
        error: error.message,
        duration: Date.now() - this.testStartTime
      });
    }
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    // 等待异步测试完成
    setTimeout(() => {
      console.log('\n📊 API客户端部署验证结果:');
      console.log('='.repeat(60));

      let passedTests = 0;
      let totalTests = this.testResults.length;

      this.testResults.forEach(test => {
        const status = test.success ? '✅ 通过' : '❌ 失败';
        const duration = test.duration ? ` (${test.duration}ms)` : '';
        console.log(`${status} ${test.name}${duration}`);
        
        if (!test.success && test.error) {
          console.log(`   错误: ${test.error}`);
        }
        
        if (test.success) passedTests++;
      });

      console.log('='.repeat(60));
      console.log(`总计: ${totalTests} 个测试, ${passedTests} 个通过, ${totalTests - passedTests} 个失败`);
      console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
      console.log(`总耗时: ${Date.now() - this.testStartTime}ms`);

      if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！API客户端部署成功！');
        this.generateDeploymentReport('success');
      } else {
        console.log('⚠️  部分测试失败，需要检查部署问题');
        this.generateDeploymentReport('partial');
      }
    }, 2000); // 等待2秒让异步测试完成
  }

  /**
   * 生成部署报告
   */
  generateDeploymentReport(status) {
    const report = {
      timestamp: new Date().toISOString(),
      deploymentStatus: status,
      testSummary: {
        total: this.testResults.length,
        passed: this.testResults.filter(t => t.success).length,
        failed: this.testResults.filter(t => !t.success).length,
        successRate: ((this.testResults.filter(t => t.success).length / this.testResults.length) * 100).toFixed(1) + '%',
        totalDuration: Date.now() - this.testStartTime + 'ms'
      },
      testDetails: this.testResults,
      deploymentInfo: {
        newAPIClient: 'ultimate-api-client.js',
        replacedClients: [
          'api.js',
          'api-client-unified.js', 
          'api-client-final.js',
          'request.js'
        ],
        features: [
          '智能缓存机制',
          '请求去重',
          '自动重试',
          '批量处理',
          '性能监控',
          '错误处理'
        ]
      },
      recommendations: this.generateDeploymentRecommendations(status)
    };

    console.log('\n📋 部署报告已生成');
    return report;
  }

  /**
   * 生成部署建议
   */
  generateDeploymentRecommendations(status) {
    const recommendations = [];

    if (status === 'success') {
      recommendations.push('✅ API客户端部署成功，所有功能正常');
      recommendations.push('🚀 建议启用生产环境缓存以获得最佳性能');
      recommendations.push('📊 建议配置API调用监控和告警');
      recommendations.push('🔄 可以安全清理旧的API客户端文件');
    } else {
      recommendations.push('⚠️ 部分功能存在问题，建议修复后再全面部署');
      recommendations.push('🔍 重点检查失败的测试用例');
      recommendations.push('🛠️ 考虑保留旧API客户端作为备用');
      recommendations.push('📝 建议在测试环境进一步验证');
    }

    return recommendations;
  }
}

// 导出测试类
module.exports = APIClientDeploymentTest;

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new APIClientDeploymentTest();
  test.runAllTests();
}
