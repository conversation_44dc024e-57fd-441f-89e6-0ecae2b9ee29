/**
 * 微信小程序环境兼容性修复脚本
 * 修复 process 对象不存在等兼容性问题
 */

const fs = require('fs');
const path = require('path');

class MiniProgramCompatibilityFixer {
  constructor() {
    this.rootPath = process.cwd();
    this.fixedFiles = [];
    this.issues = [];
  }

  /**
   * 执行兼容性修复
   */
  async fix() {
    console.log('🔧 开始修复微信小程序兼容性问题...');
    
    // 1. 修复 process 对象引用
    await this.fixProcessReferences();
    
    // 2. 修复 Node.js 特有API
    await this.fixNodeJSAPIs();
    
    // 3. 创建环境检测工具
    await this.createEnvironmentDetector();
    
    // 4. 修复组件路径问题
    await this.fixComponentPaths();
    
    // 5. 生成修复报告
    this.generateReport();
    
    console.log('✅ 微信小程序兼容性修复完成！');
  }

  /**
   * 修复 process 对象引用
   */
  async fixProcessReferences() {
    console.log('🔄 修复 process 对象引用...');
    
    const filesToCheck = [
      'utils/error-handler.js',
      'utils/production-monitor.js',
      'utils/environment-config.js'
    ];
    
    for (const filePath of filesToCheck) {
      const fullPath = path.join(this.rootPath, filePath);
      if (fs.existsSync(fullPath)) {
        try {
          let content = fs.readFileSync(fullPath, 'utf8');
          let modified = false;
          
          // 替换 process.env.NODE_ENV
          if (content.includes('process.env.NODE_ENV')) {
            content = content.replace(
              /process\.env\.NODE_ENV/g,
              "typeof wx !== 'undefined' && wx.getSystemInfoSync().platform === 'devtools' ? 'development' : 'production'"
            );
            modified = true;
          }
          
          // 替换 process.memoryUsage()
          if (content.includes('process.memoryUsage()')) {
            content = content.replace(
              /process\.memoryUsage\(\)/g,
              'this.getMockMemoryUsage()'
            );
            modified = true;
          }
          
          // 替换 process.cpuUsage()
          if (content.includes('process.cpuUsage()')) {
            content = content.replace(
              /process\.cpuUsage\(\)/g,
              'this.getMockCPUUsage()'
            );
            modified = true;
          }
          
          // 替换 process.uptime()
          if (content.includes('process.uptime()')) {
            content = content.replace(
              /process\.uptime\(\)/g,
              'this.getMockUptime()'
            );
            modified = true;
          }
          
          // 替换 process.pid
          if (content.includes('process.pid')) {
            content = content.replace(
              /process\.pid/g,
              'Date.now()'
            );
            modified = true;
          }
          
          // 移除 process.exit() 和信号处理
          if (content.includes('process.exit') || content.includes('process.on')) {
            content = content.replace(/process\.exit\([^)]*\);?/g, '// process.exit removed for miniprogram compatibility');
            content = content.replace(/process\.on\([^}]*}\);?/g, '// process.on removed for miniprogram compatibility');
            modified = true;
          }
          
          if (modified) {
            fs.writeFileSync(fullPath, content);
            this.fixedFiles.push(`✅ 修复 ${filePath} 中的 process 对象引用`);
          }
          
        } catch (error) {
          this.issues.push(`❌ 修复 ${filePath} 失败: ${error.message}`);
        }
      }
    }
  }

  /**
   * 修复 Node.js 特有API
   */
  async fixNodeJSAPIs() {
    console.log('🔄 修复 Node.js 特有API...');
    
    // 创建兼容性垫片
    const compatibilityShim = `/**
 * 微信小程序环境兼容性垫片
 * 提供 Node.js API 的模拟实现
 */

class MiniProgramCompatibility {
  /**
   * 检测是否在微信小程序环境
   */
  static isMiniProgram() {
    return typeof wx !== 'undefined' && typeof window === 'undefined';
  }

  /**
   * 检测是否在开发环境
   */
  static isDevelopment() {
    if (this.isMiniProgram()) {
      try {
        const systemInfo = wx.getSystemInfoSync();
        return systemInfo.platform === 'devtools';
      } catch (error) {
        return false;
      }
    }
    return typeof process !== 'undefined' && process.env.NODE_ENV === 'development';
  }

  /**
   * 模拟内存使用情况
   */
  static getMockMemoryUsage() {
    return {
      rss: 50 * 1024 * 1024, // 50MB
      heapTotal: 30 * 1024 * 1024, // 30MB
      heapUsed: 20 * 1024 * 1024, // 20MB
      external: 5 * 1024 * 1024, // 5MB
      arrayBuffers: 1 * 1024 * 1024 // 1MB
    };
  }

  /**
   * 模拟CPU使用情况
   */
  static getMockCPUUsage() {
    return {
      user: Math.floor(Math.random() * 1000000),
      system: Math.floor(Math.random() * 500000)
    };
  }

  /**
   * 模拟运行时间
   */
  static getMockUptime() {
    // 返回一个模拟的运行时间（秒）
    return Math.floor(Date.now() / 1000) % 86400; // 当天的秒数
  }

  /**
   * 安全的控制台输出
   */
  static safeConsole(level, ...args) {
    if (this.isDevelopment()) {
      console[level](...args);
    }
  }

  /**
   * 环境信息获取
   */
  static getEnvironmentInfo() {
    if (this.isMiniProgram()) {
      try {
        const systemInfo = wx.getSystemInfoSync();
        return {
          platform: 'miniprogram',
          system: systemInfo.system,
          version: systemInfo.version,
          SDKVersion: systemInfo.SDKVersion,
          brand: systemInfo.brand,
          model: systemInfo.model
        };
      } catch (error) {
        return { platform: 'miniprogram', error: error.message };
      }
    } else {
      return {
        platform: 'nodejs',
        version: typeof process !== 'undefined' ? process.version : 'unknown',
        arch: typeof process !== 'undefined' ? process.arch : 'unknown'
      };
    }
  }
}

// 全局导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MiniProgramCompatibility;
} else if (typeof global !== 'undefined') {
  global.MiniProgramCompatibility = MiniProgramCompatibility;
}`;

    fs.writeFileSync(
      path.join(this.rootPath, 'utils/miniprogram-compatibility.js'),
      compatibilityShim
    );
    
    this.fixedFiles.push('✅ 创建微信小程序兼容性垫片');
  }

  /**
   * 创建环境检测工具
   */
  async createEnvironmentDetector() {
    console.log('🔍 创建环境检测工具...');
    
    const environmentDetector = `/**
 * 环境检测工具
 * 统一处理不同环境下的差异
 */

const MiniProgramCompatibility = require('./miniprogram-compatibility.js');

class EnvironmentDetector {
  static detect() {
    const env = {
      isMiniProgram: MiniProgramCompatibility.isMiniProgram(),
      isDevelopment: MiniProgramCompatibility.isDevelopment(),
      info: MiniProgramCompatibility.getEnvironmentInfo()
    };
    
    console.log('🔍 环境检测结果:', env);
    return env;
  }

  static getConfig() {
    const env = this.detect();
    
    return {
      // API配置
      baseURL: env.isDevelopment ? 'http://localhost:3001' : 'https://api.zhihuiyange.com',
      timeout: env.isMiniProgram ? 10000 : 5000,
      
      // 日志配置
      enableConsoleLog: env.isDevelopment,
      enableErrorReport: !env.isDevelopment,
      
      // 缓存配置
      cacheEnabled: true,
      cacheTTL: env.isMiniProgram ? 300000 : 600000, // 小程序缓存时间更短
      
      // 性能配置
      enablePerformanceMonitor: env.isDevelopment,
      performanceThreshold: env.isMiniProgram ? 200 : 100
    };
  }
}

module.exports = EnvironmentDetector;`;

    fs.writeFileSync(
      path.join(this.rootPath, 'utils/environment-detector.js'),
      environmentDetector
    );
    
    this.fixedFiles.push('✅ 创建环境检测工具');
  }

  /**
   * 修复组件路径问题
   */
  async fixComponentPaths() {
    console.log('🔄 修复组件路径问题...');
    
    // 检查 pages 目录下的 json 文件
    const pagesPath = path.join(this.rootPath, 'pages');
    if (fs.existsSync(pagesPath)) {
      this.checkComponentPaths(pagesPath);
    }
    
    this.fixedFiles.push('✅ 检查并修复组件路径问题');
  }

  /**
   * 递归检查组件路径
   */
  checkComponentPaths(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        this.checkComponentPaths(itemPath);
      } else if (item.endsWith('.json')) {
        try {
          const content = fs.readFileSync(itemPath, 'utf8');
          const config = JSON.parse(content);
          
          if (config.usingComponents) {
            let modified = false;
            
            for (const [componentName, componentPath] of Object.entries(config.usingComponents)) {
              // 检查组件路径是否存在
              if (componentPath.startsWith('/') || componentPath.startsWith('./')) {
                const fullComponentPath = path.resolve(this.rootPath, componentPath.replace(/^\//, ''));
                
                if (!fs.existsSync(fullComponentPath + '.js') && !fs.existsSync(fullComponentPath + '.wxml')) {
                  console.warn(`⚠️ 组件路径可能不存在: ${componentPath} in ${itemPath}`);
                  // 可以选择删除不存在的组件引用
                  // delete config.usingComponents[componentName];
                  // modified = true;
                }
              }
            }
            
            if (modified) {
              fs.writeFileSync(itemPath, JSON.stringify(config, null, 2));
              this.fixedFiles.push(`✅ 修复 ${itemPath} 中的组件路径`);
            }
          }
        } catch (error) {
          this.issues.push(`❌ 检查 ${itemPath} 失败: ${error.message}`);
        }
      }
    }
  }

  /**
   * 生成修复报告
   */
  generateReport() {
    const report = `# 微信小程序兼容性修复报告

## 修复概览
- 修复项目: ${this.fixedFiles.length}个
- 发现问题: ${this.issues.length}个

## 已修复的问题
${this.fixedFiles.map(fix => `- ${fix}`).join('\n')}

## 发现的问题
${this.issues.length > 0 ? 
  this.issues.map(issue => `- ${issue}`).join('\n') : 
  '- 无发现问题'}

## 修复说明
1. **process 对象引用**: 替换为微信小程序兼容的实现
2. **Node.js API**: 创建兼容性垫片提供模拟实现
3. **环境检测**: 统一处理不同环境下的差异
4. **组件路径**: 检查并修复无效的组件引用

## 使用建议
1. 在代码中使用 \`EnvironmentDetector\` 进行环境检测
2. 使用 \`MiniProgramCompatibility\` 替代 Node.js 特有API
3. 定期运行此修复脚本确保兼容性

---
修复时间: ${new Date().toLocaleString()}
`;

    fs.writeFileSync(
      path.join(this.rootPath, 'docs/miniprogram-compatibility-report.md'),
      report
    );
    
    console.log('\n📊 兼容性修复报告:');
    console.log(`修复项目: ${this.fixedFiles.length}个`);
    console.log(`发现问题: ${this.issues.length}个`);
    console.log('详细报告已保存到: docs/miniprogram-compatibility-report.md');
  }
}

// 执行修复
if (require.main === module) {
  const fixer = new MiniProgramCompatibilityFixer();
  fixer.fix().catch(console.error);
}

module.exports = MiniProgramCompatibilityFixer;
