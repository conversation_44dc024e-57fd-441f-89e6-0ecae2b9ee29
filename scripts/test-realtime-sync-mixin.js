/**
 * 实时同步 Mixin 功能测试脚本
 * 验证 subscribeData 方法是否正常工作
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: (key) => {
    const mockData = {
      'health_records': [
        { id: 1, type: '日常检查', status: 'healthy' },
        { id: 2, type: '疫苗接种', status: 'treatment' }
      ],
      'production_records': [
        { id: 1, type: '称重记录', weight: 3.2 },
        { id: 2, type: '出栏记录', count: 50 }
      ]
    };
    return mockData[key] || [];
  },
  setStorageSync: (key, data) => {
    console.log(`设置存储: ${key}`, data);
  }
};

class RealtimeSyncMixinTester {
  constructor() {
    this.testResults = [];
    this.errors = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始实时同步 Mixin 功能测试...');
    
    // 1. 测试模块导入
    await this.testModuleImport();
    
    // 2. 测试 Mixin 结构
    await this.testMixinStructure();
    
    // 3. 测试页面集成
    await this.testPageIntegration();
    
    // 4. 测试实时同步功能
    await this.testRealtimeSyncFunctions();
    
    // 5. 生成测试报告
    this.generateTestReport();
    
    console.log('✅ 实时同步 Mixin 功能测试完成！');
  }

  /**
   * 测试模块导入
   */
  async testModuleImport() {
    console.log('📦 测试模块导入...');
    
    try {
      const { RealtimeSyncMixin, QuickSetup } = require('../utils/websocket/index.js');
      
      if (RealtimeSyncMixin && QuickSetup) {
        this.testResults.push('✅ 模块导入成功');
        
        // 验证 RealtimeSyncMixin 结构
        if (RealtimeSyncMixin.data && RealtimeSyncMixin.methods) {
          this.testResults.push('✅ RealtimeSyncMixin 结构正确');
        } else {
          this.errors.push('❌ RealtimeSyncMixin 结构不完整');
        }
        
        return { RealtimeSyncMixin, QuickSetup };
      } else {
        this.errors.push('❌ 模块导入失败：缺少导出对象');
        return null;
      }
    } catch (error) {
      this.errors.push(`❌ 模块导入失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 测试 Mixin 结构
   */
  async testMixinStructure() {
    console.log('🔧 测试 Mixin 结构...');
    
    try {
      const { RealtimeSyncMixin } = require('../utils/websocket/index.js');
      
      // 检查必需的数据字段
      const requiredDataFields = ['realtimeSubscriptions', 'dataWatchers'];
      const missingDataFields = requiredDataFields.filter(field => 
        !RealtimeSyncMixin.data || !(field in RealtimeSyncMixin.data)
      );
      
      if (missingDataFields.length === 0) {
        this.testResults.push('✅ Mixin 数据字段完整');
      } else {
        this.errors.push(`❌ Mixin 缺少数据字段: ${missingDataFields.join(', ')}`);
      }
      
      // 检查必需的方法
      const requiredMethods = ['subscribeData', 'watchData', 'cleanupRealtimeSync'];
      const missingMethods = requiredMethods.filter(method => 
        !RealtimeSyncMixin.methods || !(method in RealtimeSyncMixin.methods)
      );
      
      if (missingMethods.length === 0) {
        this.testResults.push('✅ Mixin 方法完整');
      } else {
        this.errors.push(`❌ Mixin 缺少方法: ${missingMethods.join(', ')}`);
      }
      
      // 检查生命周期方法
      if (RealtimeSyncMixin.onLoad && RealtimeSyncMixin.onUnload) {
        this.testResults.push('✅ Mixin 生命周期方法存在');
      } else {
        this.errors.push('❌ Mixin 缺少生命周期方法');
      }
      
    } catch (error) {
      this.errors.push(`❌ Mixin 结构测试失败: ${error.message}`);
    }
  }

  /**
   * 测试页面集成
   */
  async testPageIntegration() {
    console.log('📄 测试页面集成...');
    
    try {
      const { RealtimeSyncMixin } = require('../utils/websocket/index.js');
      
      // 模拟页面对象
      const mockPage = {
        data: {
          ...RealtimeSyncMixin.data,
          activeTab: 0
        },
        setData: function(data) {
          Object.assign(this.data, data);
          console.log('页面数据更新:', data);
        },
        ...RealtimeSyncMixin.methods
      };
      
      // 测试 subscribeData 方法是否存在
      if (typeof mockPage.subscribeData === 'function') {
        this.testResults.push('✅ 页面集成 subscribeData 方法成功');
        
        // 测试方法调用
        try {
          const unsubscribe = mockPage.subscribeData('test_data', (data) => {
            console.log('收到测试数据:', data);
          });
          
          if (typeof unsubscribe === 'function') {
            this.testResults.push('✅ subscribeData 返回取消订阅函数');
          }
        } catch (error) {
          this.errors.push(`❌ subscribeData 调用失败: ${error.message}`);
        }
      } else {
        this.errors.push('❌ 页面集成失败：subscribeData 方法不存在');
      }
      
    } catch (error) {
      this.errors.push(`❌ 页面集成测试失败: ${error.message}`);
    }
  }

  /**
   * 测试实时同步功能
   */
  async testRealtimeSyncFunctions() {
    console.log('🔄 测试实时同步功能...');
    
    try {
      const { QuickSetup } = require('../utils/websocket/index.js');
      
      // 模拟页面对象（包含 subscribeData 方法）
      const mockPage = {
        data: {
          realtimeSubscriptions: [],
          dataWatchers: []
        },
        subscribeData: function(dataType, callback) {
          console.log(`订阅数据类型: ${dataType}`);
          // 模拟订阅成功
          const unsubscribe = () => {
            console.log(`取消订阅: ${dataType}`);
          };
          this.data.realtimeSubscriptions.push(unsubscribe);
          return unsubscribe;
        }
      };
      
      // 测试 QuickSetup 方法
      const testMethods = [
        'setupHealthRecordsSync',
        'setupProductionRecordsSync',
        'setupFlocksSync'
      ];
      
      for (const methodName of testMethods) {
        if (typeof QuickSetup[methodName] === 'function') {
          try {
            const unsubscribe = QuickSetup[methodName](mockPage, (data) => {
              console.log(`${methodName} 回调:`, data);
            });
            
            if (typeof unsubscribe === 'function') {
              this.testResults.push(`✅ ${methodName} 测试成功`);
            } else {
              this.errors.push(`❌ ${methodName} 未返回取消订阅函数`);
            }
          } catch (error) {
            this.errors.push(`❌ ${methodName} 调用失败: ${error.message}`);
          }
        } else {
          this.errors.push(`❌ QuickSetup 缺少方法: ${methodName}`);
        }
      }
      
    } catch (error) {
      this.errors.push(`❌ 实时同步功能测试失败: ${error.message}`);
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    const totalTests = this.testResults.length + this.errors.length;
    const passedTests = this.testResults.length;
    const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
    
    const report = `# 实时同步 Mixin 功能测试报告

## 测试概览
- 总测试项: ${totalTests}
- 通过测试: ${passedTests}
- 成功率: ${successRate}%
- 错误数量: ${this.errors.length}

## ✅ 通过的测试
${this.testResults.map(result => `- ${result}`).join('\n')}

## ❌ 失败的测试
${this.errors.length > 0 ? 
  this.errors.map(error => `- ${error}`).join('\n') : 
  '- 无失败测试'}

## 修复验证

### 问题描述
原始错误: \`page.subscribeData is not a function\`

### 修复方案
1. **数据合并**: 在页面 data 中合并 RealtimeSyncMixin.data
   \`\`\`javascript
   data: {
     ...RealtimeSyncMixin.data,
     // 页面自己的数据
   }
   \`\`\`

2. **方法合并**: 在页面对象中合并 RealtimeSyncMixin.methods
   \`\`\`javascript
   // 页面方法
   ...RealtimeSyncMixin.methods
   \`\`\`

3. **生命周期调用**: 在页面生命周期中调用 mixin 的生命周期方法
   \`\`\`javascript
   onLoad: function(options) {
     if (RealtimeSyncMixin.onLoad) {
       RealtimeSyncMixin.onLoad.call(this);
     }
     // 页面自己的逻辑
   }
   \`\`\`

### 测试结果
${successRate >= 90 ? '🎉 修复成功！所有功能正常工作' : 
  successRate >= 70 ? '⚠️ 大部分功能正常，建议检查剩余问题' : 
  '❌ 仍存在问题，需要进一步修复'}

## 使用建议
1. 确保页面正确合并 mixin 的数据和方法
2. 在生命周期方法中调用 mixin 的对应方法
3. 使用 \`this.subscribeData()\` 订阅实时数据
4. 在页面卸载时清理订阅

---
测试时间: ${new Date().toLocaleString()}
测试环境: Node.js 模拟环境
`;

    const fs = require('fs');
    const path = require('path');
    
    fs.writeFileSync(
      path.join(process.cwd(), 'docs/realtime-sync-mixin-test-report.md'),
      report
    );
    
    console.log('\n📊 实时同步 Mixin 测试报告:');
    console.log(`成功率: ${successRate}%`);
    console.log(`通过测试: ${passedTests}/${totalTests}`);
    console.log(`失败测试: ${this.errors.length}个`);
    console.log('详细报告已保存到: docs/realtime-sync-mixin-test-report.md');
    
    if (successRate >= 90) {
      console.log('🎉 恭喜！实时同步 Mixin 功能测试通过！');
    } else if (successRate >= 70) {
      console.log('⚠️ 大部分功能正常，建议检查剩余问题');
    } else {
      console.log('❌ 仍存在问题，需要进一步修复');
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new RealtimeSyncMixinTester();
  tester.runAllTests().catch(console.error);
}

module.exports = RealtimeSyncMixinTester;
