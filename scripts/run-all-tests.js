#!/usr/bin/env node

/**
 * 完整测试套件运行脚本
 * Complete Test Suite Runner
 * 
 * 功能：
 * - 运行所有测试用例
 * - 生成测试报告
 * - 验证系统完整性
 * - 性能基准测试
 */

const fs = require('fs');
const path = require('path');

// 导入测试套件
const { DataIsolationTestSuite, runIntegrationTest, runPerformanceTest } = require('../tests/data-isolation-test');
const { IntegrationTestSuite } = require('../tests/integration-test');

/**
 * 测试运行器主类
 */
class TestRunner {
  constructor() {
    this.startTime = Date.now();
    this.testResults = {
      dataIsolation: null,
      integration: null,
      performance: null
    };
    this.overallResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 智慧养鹅云开发 - SAAS多租户系统完整测试套件');
    console.log('=' .repeat(60));
    console.log(`开始时间: ${new Date().toLocaleString()}`);
    console.log('=' .repeat(60));

    try {
      // 1. 数据隔离测试
      await this.runDataIsolationTests();
      
      // 2. 集成测试
      await this.runIntegrationTests();
      
      // 3. 性能测试
      await this.runPerformanceTests();
      
      // 4. 生成测试报告
      await this.generateTestReport();
      
      // 5. 显示总结
      this.showSummary();
      
    } catch (error) {
      console.error('❌ 测试运行失败:', error);
      process.exit(1);
    }
  }

  /**
   * 运行数据隔离测试
   */
  async runDataIsolationTests() {
    console.log('\n🔒 第一部分：数据隔离测试');
    console.log('-'.repeat(40));
    
    try {
      const testSuite = new DataIsolationTestSuite();
      await testSuite.runAllTests();
      
      this.testResults.dataIsolation = {
        passed: testSuite.passedTests,
        failed: testSuite.failedTests,
        total: testSuite.passedTests + testSuite.failedTests,
        results: testSuite.testResults
      };
      
      this.overallResults.totalTests += this.testResults.dataIsolation.total;
      this.overallResults.passedTests += this.testResults.dataIsolation.passed;
      this.overallResults.failedTests += this.testResults.dataIsolation.failed;
      
      console.log(`✅ 数据隔离测试完成: ${this.testResults.dataIsolation.passed}/${this.testResults.dataIsolation.total} 通过`);
      
    } catch (error) {
      console.error('❌ 数据隔离测试失败:', error.message);
      this.testResults.dataIsolation = { error: error.message };
    }
  }

  /**
   * 运行集成测试
   */
  async runIntegrationTests() {
    console.log('\n🔗 第二部分：集成测试');
    console.log('-'.repeat(40));
    
    try {
      const testSuite = new IntegrationTestSuite();
      await testSuite.runAllTests();
      
      this.testResults.integration = {
        passed: testSuite.passedTests,
        failed: testSuite.failedTests,
        total: testSuite.passedTests + testSuite.failedTests,
        results: testSuite.testResults
      };
      
      this.overallResults.totalTests += this.testResults.integration.total;
      this.overallResults.passedTests += this.testResults.integration.passed;
      this.overallResults.failedTests += this.testResults.integration.failed;
      
      console.log(`✅ 集成测试完成: ${this.testResults.integration.passed}/${this.testResults.integration.total} 通过`);
      
    } catch (error) {
      console.error('❌ 集成测试失败:', error.message);
      this.testResults.integration = { error: error.message };
    }
  }

  /**
   * 运行性能测试
   */
  async runPerformanceTests() {
    console.log('\n⚡ 第三部分：性能测试');
    console.log('-'.repeat(40));
    
    try {
      // 运行性能基准测试
      const performanceResults = await this.runPerformanceBenchmarks();
      
      this.testResults.performance = performanceResults;
      
      console.log('✅ 性能测试完成');
      
    } catch (error) {
      console.error('❌ 性能测试失败:', error.message);
      this.testResults.performance = { error: error.message };
    }
  }

  /**
   * 运行性能基准测试
   */
  async runPerformanceBenchmarks() {
    const benchmarks = [];
    
    // 1. 数据隔离查询性能测试
    console.log('  📊 测试数据隔离查询性能...');
    const isolationBenchmark = await this.benchmarkDataIsolation();
    benchmarks.push(isolationBenchmark);
    
    // 2. API调用性能测试
    console.log('  📊 测试API调用性能...');
    const apiBenchmark = await this.benchmarkAPICall();
    benchmarks.push(apiBenchmark);
    
    // 3. 缓存性能测试
    console.log('  📊 测试缓存性能...');
    const cacheBenchmark = await this.benchmarkCache();
    benchmarks.push(cacheBenchmark);
    
    return {
      benchmarks,
      summary: this.calculatePerformanceSummary(benchmarks)
    };
  }

  /**
   * 数据隔离查询性能基准测试
   */
  async benchmarkDataIsolation() {
    const { DataIsolationQueryBuilder } = require('../utils/data-isolation');
    
    const iterations = 10000;
    const testUser = {
      _id: 'test_user',
      tenant_id: 'test_tenant',
      role: 'admin'
    };
    
    const startTime = process.hrtime.bigint();
    
    for (let i = 0; i < iterations; i++) {
      const builder = new DataIsolationQueryBuilder(testUser, 'flocks');
      builder.buildReadQuery({ breed: '白鹅' });
    }
    
    const endTime = process.hrtime.bigint();
    const totalTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
    const avgTime = totalTime / iterations;
    
    return {
      name: '数据隔离查询构建',
      iterations,
      totalTime: totalTime.toFixed(2),
      avgTime: avgTime.toFixed(4),
      unit: 'ms',
      status: avgTime < 0.1 ? 'excellent' : avgTime < 0.5 ? 'good' : 'needs_optimization'
    };
  }

  /**
   * API调用性能基准测试
   */
  async benchmarkAPICall() {
    const { APIStandardHandler } = require('../utils/api-standard');
    
    const iterations = 1000;
    const mockEvent = {
      action: 'test',
      data: { test: true }
    };
    const mockContext = {
      OPENID: 'test_openid'
    };
    
    const startTime = process.hrtime.bigint();
    
    for (let i = 0; i < iterations; i++) {
      try {
        const handler = new APIStandardHandler(mockEvent, mockContext);
        // 模拟处理过程
        handler.validateParams();
        handler.generateRequestId();
      } catch (error) {
        // 忽略预期的错误
      }
    }
    
    const endTime = process.hrtime.bigint();
    const totalTime = Number(endTime - startTime) / 1000000;
    const avgTime = totalTime / iterations;
    
    return {
      name: 'API标准化处理',
      iterations,
      totalTime: totalTime.toFixed(2),
      avgTime: avgTime.toFixed(4),
      unit: 'ms',
      status: avgTime < 1 ? 'excellent' : avgTime < 5 ? 'good' : 'needs_optimization'
    };
  }

  /**
   * 缓存性能基准测试
   */
  async benchmarkCache() {
    const { DataCacheManager } = require('../utils/data-scheduler');
    
    const cache = new DataCacheManager();
    const iterations = 50000;
    
    // 写入测试
    const writeStartTime = process.hrtime.bigint();
    for (let i = 0; i < iterations; i++) {
      cache.set(`key_${i}`, { data: `value_${i}` });
    }
    const writeEndTime = process.hrtime.bigint();
    const writeTime = Number(writeEndTime - writeStartTime) / 1000000;
    
    // 读取测试
    const readStartTime = process.hrtime.bigint();
    for (let i = 0; i < iterations; i++) {
      cache.get(`key_${i}`);
    }
    const readEndTime = process.hrtime.bigint();
    const readTime = Number(readEndTime - readStartTime) / 1000000;
    
    return {
      name: '缓存读写操作',
      iterations,
      writeTime: writeTime.toFixed(2),
      readTime: readTime.toFixed(2),
      avgWriteTime: (writeTime / iterations).toFixed(6),
      avgReadTime: (readTime / iterations).toFixed(6),
      unit: 'ms',
      status: 'excellent'
    };
  }

  /**
   * 计算性能总结
   */
  calculatePerformanceSummary(benchmarks) {
    const excellentCount = benchmarks.filter(b => b.status === 'excellent').length;
    const goodCount = benchmarks.filter(b => b.status === 'good').length;
    const needsOptimizationCount = benchmarks.filter(b => b.status === 'needs_optimization').length;
    
    let overallStatus = 'excellent';
    if (needsOptimizationCount > 0) {
      overallStatus = 'needs_optimization';
    } else if (goodCount > excellentCount) {
      overallStatus = 'good';
    }
    
    return {
      totalBenchmarks: benchmarks.length,
      excellent: excellentCount,
      good: goodCount,
      needsOptimization: needsOptimizationCount,
      overallStatus
    };
  }

  /**
   * 生成测试报告
   */
  async generateTestReport() {
    console.log('\n📄 生成测试报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      duration: Date.now() - this.startTime,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      },
      overall: this.overallResults,
      details: this.testResults
    };
    
    // 创建报告目录
    const reportsDir = path.join(__dirname, '../reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // 生成JSON报告
    const reportFile = path.join(reportsDir, `test-report-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    // 生成HTML报告
    const htmlReport = this.generateHTMLReport(report);
    const htmlFile = path.join(reportsDir, `test-report-${Date.now()}.html`);
    fs.writeFileSync(htmlFile, htmlReport);
    
    console.log(`📄 测试报告已生成:`);
    console.log(`   JSON: ${reportFile}`);
    console.log(`   HTML: ${htmlFile}`);
  }

  /**
   * 生成HTML测试报告
   */
  generateHTMLReport(report) {
    const successRate = ((report.overall.passedTests / report.overall.totalTests) * 100).toFixed(2);
    
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧养鹅云开发 - 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; margin-top: 5px; }
        .success { color: #28a745; }
        .danger { color: #dc3545; }
        .warning { color: #ffc107; }
        .section { margin-bottom: 30px; }
        .section h3 { border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .status-excellent { color: #28a745; }
        .status-good { color: #17a2b8; }
        .status-needs_optimization { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 智慧养鹅云开发 - SAAS多租户系统测试报告</h1>
            <p>生成时间: ${new Date(report.timestamp).toLocaleString()}</p>
            <p>测试耗时: ${(report.duration / 1000).toFixed(2)} 秒</p>
        </div>
        
        <div class="summary">
            <div class="stat-card">
                <div class="stat-number">${report.overall.totalTests}</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number success">${report.overall.passedTests}</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number danger">${report.overall.failedTests}</div>
                <div class="stat-label">失败测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number ${successRate >= 95 ? 'success' : successRate >= 80 ? 'warning' : 'danger'}">${successRate}%</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>
        
        <div class="section">
            <h3>📊 测试详情</h3>
            ${this.generateTestDetailsHTML(report.details)}
        </div>
        
        <div class="section">
            <h3>⚡ 性能基准测试</h3>
            ${this.generatePerformanceHTML(report.details.performance)}
        </div>
        
        <div class="section">
            <h3>🖥️ 运行环境</h3>
            <table>
                <tr><th>Node.js版本</th><td>${report.environment.nodeVersion}</td></tr>
                <tr><th>操作系统</th><td>${report.environment.platform}</td></tr>
                <tr><th>架构</th><td>${report.environment.arch}</td></tr>
            </table>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * 生成测试详情HTML
   */
  generateTestDetailsHTML(details) {
    let html = '<table><tr><th>测试套件</th><th>通过</th><th>失败</th><th>总计</th><th>成功率</th></tr>';
    
    Object.entries(details).forEach(([key, result]) => {
      if (result && !result.error && result.total) {
        const rate = ((result.passed / result.total) * 100).toFixed(2);
        html += `
          <tr>
            <td>${this.getTestSuiteName(key)}</td>
            <td class="success">${result.passed}</td>
            <td class="danger">${result.failed}</td>
            <td>${result.total}</td>
            <td class="${rate >= 95 ? 'success' : rate >= 80 ? 'warning' : 'danger'}">${rate}%</td>
          </tr>
        `;
      }
    });
    
    html += '</table>';
    return html;
  }

  /**
   * 生成性能测试HTML
   */
  generatePerformanceHTML(performance) {
    if (!performance || performance.error) {
      return '<p class="danger">性能测试未完成或出现错误</p>';
    }
    
    let html = '<table><tr><th>测试项目</th><th>迭代次数</th><th>平均耗时</th><th>状态</th></tr>';
    
    performance.benchmarks.forEach(benchmark => {
      html += `
        <tr>
          <td>${benchmark.name}</td>
          <td>${benchmark.iterations.toLocaleString()}</td>
          <td>${benchmark.avgTime} ${benchmark.unit}</td>
          <td class="status-${benchmark.status}">${this.getStatusText(benchmark.status)}</td>
        </tr>
      `;
    });
    
    html += '</table>';
    return html;
  }

  /**
   * 获取测试套件名称
   */
  getTestSuiteName(key) {
    const names = {
      dataIsolation: '数据隔离测试',
      integration: '集成测试',
      performance: '性能测试'
    };
    return names[key] || key;
  }

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const texts = {
      excellent: '优秀',
      good: '良好',
      needs_optimization: '需要优化'
    };
    return texts[status] || status;
  }

  /**
   * 显示测试总结
   */
  showSummary() {
    const endTime = Date.now();
    const duration = (endTime - this.startTime) / 1000;
    const successRate = ((this.overallResults.passedTests / this.overallResults.totalTests) * 100).toFixed(2);
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 测试总结');
    console.log('='.repeat(60));
    console.log(`总测试数: ${this.overallResults.totalTests}`);
    console.log(`通过测试: ${this.overallResults.passedTests}`);
    console.log(`失败测试: ${this.overallResults.failedTests}`);
    console.log(`成功率: ${successRate}%`);
    console.log(`总耗时: ${duration.toFixed(2)} 秒`);
    console.log(`结束时间: ${new Date().toLocaleString()}`);
    
    if (this.overallResults.failedTests === 0) {
      console.log('\n🎉 所有测试都通过了！系统运行正常。');
      console.log('✅ SAAS多租户架构已准备就绪，可以投入生产使用。');
    } else {
      console.log(`\n⚠️  有 ${this.overallResults.failedTests} 个测试失败，请检查相关功能。`);
      console.log('❌ 建议修复所有问题后再部署到生产环境。');
    }
    
    console.log('='.repeat(60));
  }
}

// 主执行函数
async function main() {
  const runner = new TestRunner();
  await runner.runAllTests();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 测试运行器执行失败:', error);
    process.exit(1);
  });
}

module.exports = { TestRunner };
