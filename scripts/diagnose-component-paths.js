/**
 * 微信小程序组件路径诊断脚本
 * 诊断和修复 "Component is not found in path wx://not-found" 错误
 */

const fs = require('fs');
const path = require('path');

class ComponentPathDiagnostic {
  constructor() {
    this.rootPath = process.cwd();
    this.issues = [];
    this.fixes = [];
    this.componentReferences = new Map();
  }

  /**
   * 执行组件路径诊断
   */
  async diagnose() {
    console.log('🔍 开始诊断组件路径问题...');
    
    // 1. 扫描所有JSON配置文件
    await this.scanConfigFiles();
    
    // 2. 验证组件路径存在性
    await this.validateComponentPaths();
    
    // 3. 检查组件实际使用情况
    await this.checkComponentUsage();
    
    // 4. 生成诊断报告
    this.generateDiagnosticReport();
    
    console.log('✅ 组件路径诊断完成！');
  }

  /**
   * 扫描所有JSON配置文件
   */
  async scanConfigFiles() {
    console.log('📂 扫描JSON配置文件...');
    
    const jsonFiles = this.findJsonFiles(this.rootPath);
    
    for (const jsonFile of jsonFiles) {
      try {
        const content = fs.readFileSync(jsonFile, 'utf8');
        const config = JSON.parse(content);
        
        if (config.usingComponents) {
          const relativePath = path.relative(this.rootPath, jsonFile);
          this.componentReferences.set(relativePath, {
            file: jsonFile,
            components: config.usingComponents,
            config: config
          });
        }
      } catch (error) {
        this.issues.push({
          type: 'JSON_PARSE_ERROR',
          file: jsonFile,
          error: error.message
        });
      }
    }
    
    console.log(`📋 找到 ${this.componentReferences.size} 个包含组件引用的文件`);
  }

  /**
   * 递归查找JSON文件
   */
  findJsonFiles(dir) {
    const jsonFiles = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过node_modules等目录
          if (!['node_modules', '.git', 'miniprogram_npm'].includes(item)) {
            jsonFiles.push(...this.findJsonFiles(fullPath));
          }
        } else if (item.endsWith('.json')) {
          jsonFiles.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`警告: 无法读取目录 ${dir}: ${error.message}`);
    }
    
    return jsonFiles;
  }

  /**
   * 验证组件路径存在性
   */
  async validateComponentPaths() {
    console.log('🔍 验证组件路径存在性...');
    
    for (const [configFile, info] of this.componentReferences) {
      const configDir = path.dirname(info.file);
      
      for (const [componentName, componentPath] of Object.entries(info.components)) {
        const issue = this.validateSingleComponentPath(
          componentName, 
          componentPath, 
          configFile, 
          configDir
        );
        
        if (issue) {
          this.issues.push(issue);
        }
      }
    }
  }

  /**
   * 验证单个组件路径
   */
  validateSingleComponentPath(componentName, componentPath, configFile, configDir) {
    // 处理不同类型的路径
    let actualPath;
    
    if (componentPath.startsWith('/')) {
      // 绝对路径（相对于项目根目录）
      actualPath = path.join(this.rootPath, componentPath.substring(1));
    } else if (componentPath.startsWith('./') || componentPath.startsWith('../')) {
      // 相对路径
      actualPath = path.resolve(configDir, componentPath);
    } else if (componentPath.startsWith('wx://')) {
      // 微信内置组件，跳过检查
      return null;
    } else if (componentPath.includes('tdesign-miniprogram') || componentPath.includes('vant-weapp')) {
      // 第三方组件库，跳过检查
      return null;
    } else {
      // 其他情况，当作相对路径处理
      actualPath = path.resolve(configDir, componentPath);
    }
    
    // 检查组件文件是否存在
    const requiredFiles = ['.js', '.wxml', '.json'];
    const existingFiles = [];
    
    for (const ext of requiredFiles) {
      const filePath = actualPath + ext;
      if (fs.existsSync(filePath)) {
        existingFiles.push(ext);
      }
    }
    
    if (existingFiles.length === 0) {
      return {
        type: 'COMPONENT_NOT_FOUND',
        configFile: configFile,
        componentName: componentName,
        componentPath: componentPath,
        resolvedPath: actualPath,
        message: `组件文件不存在: ${componentPath}`
      };
    }
    
    if (existingFiles.length < 3) {
      return {
        type: 'COMPONENT_INCOMPLETE',
        configFile: configFile,
        componentName: componentName,
        componentPath: componentPath,
        resolvedPath: actualPath,
        existingFiles: existingFiles,
        missingFiles: requiredFiles.filter(ext => !existingFiles.includes(ext)),
        message: `组件文件不完整: ${componentPath}，缺少: ${requiredFiles.filter(ext => !existingFiles.includes(ext)).join(', ')}`
      };
    }
    
    return null; // 组件路径正常
  }

  /**
   * 检查组件实际使用情况
   */
  async checkComponentUsage() {
    console.log('📊 检查组件实际使用情况...');
    
    for (const [configFile, info] of this.componentReferences) {
      const wxmlFile = info.file.replace('.json', '.wxml');
      
      if (fs.existsSync(wxmlFile)) {
        try {
          const wxmlContent = fs.readFileSync(wxmlFile, 'utf8');
          
          for (const componentName of Object.keys(info.components)) {
            const tagPattern = new RegExp(`<${componentName}[^>]*>`, 'g');
            const selfClosingPattern = new RegExp(`<${componentName}[^>]*/>`, 'g');
            
            if (!tagPattern.test(wxmlContent) && !selfClosingPattern.test(wxmlContent)) {
              this.issues.push({
                type: 'COMPONENT_UNUSED',
                configFile: configFile,
                componentName: componentName,
                componentPath: info.components[componentName],
                message: `组件已声明但未使用: ${componentName}`
              });
            }
          }
        } catch (error) {
          this.issues.push({
            type: 'WXML_READ_ERROR',
            configFile: configFile,
            wxmlFile: wxmlFile,
            error: error.message
          });
        }
      }
    }
  }

  /**
   * 生成诊断报告
   */
  generateDiagnosticReport() {
    const report = `# 微信小程序组件路径诊断报告

## 🔍 诊断概览
- 扫描文件: ${this.componentReferences.size}个
- 发现问题: ${this.issues.length}个
- 组件引用总数: ${Array.from(this.componentReferences.values()).reduce((sum, info) => sum + Object.keys(info.components).length, 0)}个

## ❌ 发现的问题

### 按问题类型分类
${this.generateIssuesByType()}

### 详细问题列表
${this.issues.map((issue, index) => `
#### ${index + 1}. ${issue.type}
- **文件**: \`${issue.configFile}\`
- **组件**: \`${issue.componentName || 'N/A'}\`
- **路径**: \`${issue.componentPath || 'N/A'}\`
- **问题**: ${issue.message}
${issue.resolvedPath ? `- **解析路径**: \`${issue.resolvedPath}\`` : ''}
${issue.missingFiles ? `- **缺少文件**: ${issue.missingFiles.join(', ')}` : ''}
`).join('\n')}

## 🔧 修复建议

### 1. 组件不存在的修复
对于 \`COMPONENT_NOT_FOUND\` 错误：
- 检查组件路径是否正确
- 确认组件文件是否存在
- 考虑移除未使用的组件引用

### 2. 组件不完整的修复
对于 \`COMPONENT_INCOMPLETE\` 错误：
- 补充缺失的组件文件（.js, .wxml, .json）
- 检查组件是否正确实现

### 3. 未使用组件的清理
对于 \`COMPONENT_UNUSED\` 警告：
- 从配置文件中移除未使用的组件声明
- 减少小程序包体积

## 📊 组件使用统计
${this.generateComponentStats()}

## 🛠️ 自动修复脚本

\`\`\`javascript
// 移除未使用的组件引用
const unusedComponents = ${JSON.stringify(this.issues.filter(issue => issue.type === 'COMPONENT_UNUSED'), null, 2)};

// 修复组件路径
const pathIssues = ${JSON.stringify(this.issues.filter(issue => issue.type === 'COMPONENT_NOT_FOUND'), null, 2)};
\`\`\`

## 🎯 修复优先级

### 🚨 高优先级（必须修复）
- COMPONENT_NOT_FOUND: ${this.issues.filter(issue => issue.type === 'COMPONENT_NOT_FOUND').length}个
- JSON_PARSE_ERROR: ${this.issues.filter(issue => issue.type === 'JSON_PARSE_ERROR').length}个

### ⚠️ 中优先级（建议修复）
- COMPONENT_INCOMPLETE: ${this.issues.filter(issue => issue.type === 'COMPONENT_INCOMPLETE').length}个

### 💡 低优先级（优化建议）
- COMPONENT_UNUSED: ${this.issues.filter(issue => issue.type === 'COMPONENT_UNUSED').length}个

---
诊断时间: ${new Date().toLocaleString()}
`;

    fs.writeFileSync(
      path.join(this.rootPath, 'docs/component-path-diagnostic-report.md'),
      report
    );
    
    console.log('\n📊 组件路径诊断报告:');
    console.log(`发现问题: ${this.issues.length}个`);
    console.log(`扫描文件: ${this.componentReferences.size}个`);
    
    // 按类型统计问题
    const issueTypes = {};
    this.issues.forEach(issue => {
      issueTypes[issue.type] = (issueTypes[issue.type] || 0) + 1;
    });
    
    console.log('问题分布:');
    Object.entries(issueTypes).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}个`);
    });
    
    console.log('详细报告已保存到: docs/component-path-diagnostic-report.md');
  }

  /**
   * 按类型生成问题统计
   */
  generateIssuesByType() {
    const issueTypes = {};
    this.issues.forEach(issue => {
      issueTypes[issue.type] = (issueTypes[issue.type] || 0) + 1;
    });
    
    return Object.entries(issueTypes).map(([type, count]) => 
      `- **${type}**: ${count}个`
    ).join('\n');
  }

  /**
   * 生成组件使用统计
   */
  generateComponentStats() {
    const componentCount = {};
    
    for (const info of this.componentReferences.values()) {
      Object.keys(info.components).forEach(componentName => {
        componentCount[componentName] = (componentCount[componentName] || 0) + 1;
      });
    }
    
    const sortedComponents = Object.entries(componentCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);
    
    return sortedComponents.map(([name, count]) => 
      `- **${name}**: 使用${count}次`
    ).join('\n');
  }
}

// 运行诊断
if (require.main === module) {
  const diagnostic = new ComponentPathDiagnostic();
  diagnostic.diagnose().catch(console.error);
}

module.exports = ComponentPathDiagnostic;
