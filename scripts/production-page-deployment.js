/**
 * 生产页面模块化部署脚本 - 第四阶段
 * Production Page Modularization Deployment Script - Phase 4
 * 
 * 将现有的production.js替换为模块化的enhanced-production.js
 */

const fs = require('fs');
const path = require('path');

/**
 * 生产页面模块化部署器
 */
class ProductionPageDeployer {
  constructor(rootPath = process.cwd()) {
    this.rootPath = rootPath;
    this.deploymentResults = [];
    
    // 部署配置
    this.deploymentConfig = {
      sourceFiles: {
        enhancedProduction: 'pages/production/enhanced-production.js',
        healthModule: 'pages/production/modules/enhanced-health-module.js',
        materialModule: 'pages/production/modules/enhanced-material-module.js'
      },
      targetFiles: {
        production: 'pages/production/production.js',
        productionWxml: 'pages/production/production.wxml',
        productionWxss: 'pages/production/production.wxss',
        productionJson: 'pages/production/production.json'
      },
      backupDir: '.production-backup'
    };
  }

  /**
   * 执行生产页面模块化部署
   */
  async deploy() {
    console.log('🚀 开始生产页面模块化部署...\n');

    try {
      // 1. 创建备份
      await this.createBackup();

      // 2. 验证模块文件
      await this.validateModuleFiles();

      // 3. 部署模块化页面
      await this.deployModularizedPage();

      // 4. 更新页面配置
      await this.updatePageConfiguration();

      // 5. 验证部署结果
      await this.validateDeployment();

      // 6. 生成部署报告
      this.generateDeploymentReport();

      console.log('✅ 生产页面模块化部署完成！');

    } catch (error) {
      console.error('❌ 部署失败:', error);
      await this.rollbackDeployment();
    }
  }

  /**
   * 创建备份
   */
  async createBackup() {
    console.log('📦 创建生产页面备份...');

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.rootPath, this.deploymentConfig.backupDir, timestamp);

    if (!fs.existsSync(backupPath)) {
      fs.mkdirSync(backupPath, { recursive: true });
    }

    // 备份现有的生产页面文件
    for (const [key, filePath] of Object.entries(this.deploymentConfig.targetFiles)) {
      const sourcePath = path.join(this.rootPath, filePath);
      if (fs.existsSync(sourcePath)) {
        const backupFilePath = path.join(backupPath, filePath);
        const backupFileDir = path.dirname(backupFilePath);
        
        if (!fs.existsSync(backupFileDir)) {
          fs.mkdirSync(backupFileDir, { recursive: true });
        }
        
        fs.copyFileSync(sourcePath, backupFilePath);
        console.log(`  ✅ 备份 ${filePath}`);
      }
    }

    this.backupPath = backupPath;
    console.log(`✅ 备份已创建: ${backupPath}\n`);
  }

  /**
   * 验证模块文件
   */
  async validateModuleFiles() {
    console.log('🔍 验证模块文件...');

    const missingFiles = [];

    for (const [key, filePath] of Object.entries(this.deploymentConfig.sourceFiles)) {
      const fullPath = path.join(this.rootPath, filePath);
      
      if (fs.existsSync(fullPath)) {
        // 验证文件语法
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          
          // 基本语法检查
          if (content.includes('module.exports') || content.includes('class ')) {
            console.log(`  ✅ ${filePath} 验证通过`);
          } else {
            console.warn(`  ⚠️  ${filePath} 可能存在格式问题`);
          }
        } catch (error) {
          console.error(`  ❌ ${filePath} 读取失败:`, error.message);
          missingFiles.push(filePath);
        }
      } else {
        console.error(`  ❌ 文件不存在: ${filePath}`);
        missingFiles.push(filePath);
      }
    }

    if (missingFiles.length > 0) {
      throw new Error(`缺少必要的模块文件: ${missingFiles.join(', ')}`);
    }

    console.log('✅ 所有模块文件验证通过\n');
  }

  /**
   * 部署模块化页面
   */
  async deployModularizedPage() {
    console.log('🔧 部署模块化页面...');

    // 1. 替换主页面文件
    await this.replaceMainPageFile();

    // 2. 确保模块目录存在
    await this.ensureModuleDirectory();

    // 3. 部署模块文件
    await this.deployModuleFiles();

    console.log('✅ 模块化页面部署完成\n');
  }

  /**
   * 替换主页面文件
   */
  async replaceMainPageFile() {
    const sourcePath = path.join(this.rootPath, this.deploymentConfig.sourceFiles.enhancedProduction);
    const targetPath = path.join(this.rootPath, this.deploymentConfig.targetFiles.production);

    if (fs.existsSync(sourcePath)) {
      // 读取增强版页面内容
      let content = fs.readFileSync(sourcePath, 'utf8');
      
      // 调整模块导入路径
      content = content.replace(
        /require\('\.\.\/\.\.\/utils\//g,
        "require('../../utils/"
      );
      
      content = content.replace(
        /require\('\.\/modules\//g,
        "require('./modules/"
      );

      // 写入目标文件
      fs.writeFileSync(targetPath, content);
      
      console.log('  ✅ 主页面文件已替换');
      
      this.deploymentResults.push({
        type: 'file_replacement',
        source: sourcePath,
        target: targetPath,
        status: 'success'
      });
    } else {
      throw new Error(`增强版页面文件不存在: ${sourcePath}`);
    }
  }

  /**
   * 确保模块目录存在
   */
  async ensureModuleDirectory() {
    const moduleDir = path.join(this.rootPath, 'pages/production/modules');
    
    if (!fs.existsSync(moduleDir)) {
      fs.mkdirSync(moduleDir, { recursive: true });
      console.log('  ✅ 创建模块目录');
    }
  }

  /**
   * 部署模块文件
   */
  async deployModuleFiles() {
    const moduleFiles = [
      'enhanced-health-module.js',
      'enhanced-material-module.js'
    ];

    for (const moduleFile of moduleFiles) {
      const sourcePath = path.join(this.rootPath, `pages/production/modules/${moduleFile}`);
      
      if (fs.existsSync(sourcePath)) {
        console.log(`  ✅ 模块文件已存在: ${moduleFile}`);
        
        this.deploymentResults.push({
          type: 'module_deployment',
          file: moduleFile,
          status: 'exists'
        });
      } else {
        console.warn(`  ⚠️  模块文件不存在: ${moduleFile}`);
        
        this.deploymentResults.push({
          type: 'module_deployment',
          file: moduleFile,
          status: 'missing'
        });
      }
    }
  }

  /**
   * 更新页面配置
   */
  async updatePageConfiguration() {
    console.log('⚙️  更新页面配置...');

    // 更新页面JSON配置
    await this.updatePageJson();

    // 更新页面样式
    await this.updatePageStyles();

    // 更新页面模板
    await this.updatePageTemplate();

    console.log('✅ 页面配置更新完成\n');
  }

  /**
   * 更新页面JSON配置
   */
  async updatePageJson() {
    const jsonPath = path.join(this.rootPath, this.deploymentConfig.targetFiles.productionJson);
    
    if (fs.existsSync(jsonPath)) {
      try {
        const config = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
        
        // 添加模块化页面的配置
        config.enablePullDownRefresh = true;
        config.onReachBottomDistance = 50;
        
        // 添加组件引用（如果需要）
        if (!config.usingComponents) {
          config.usingComponents = {};
        }

        fs.writeFileSync(jsonPath, JSON.stringify(config, null, 2));
        console.log('  ✅ 页面JSON配置已更新');
        
        this.deploymentResults.push({
          type: 'config_update',
          file: 'production.json',
          status: 'success'
        });
      } catch (error) {
        console.error('  ❌ 页面JSON配置更新失败:', error.message);
      }
    }
  }

  /**
   * 更新页面样式
   */
  async updatePageStyles() {
    const wxssPath = path.join(this.rootPath, this.deploymentConfig.targetFiles.productionWxss);
    
    if (fs.existsSync(wxssPath)) {
      let styles = fs.readFileSync(wxssPath, 'utf8');
      
      // 添加模块化页面的样式
      const moduleStyles = `
/* 模块化页面增强样式 */
.module-container {
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.module-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.module-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.module-loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

.module-error {
  text-align: center;
  padding: 40rpx;
  color: #ff4d4f;
}

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.quick-action-btn {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  background: #f0f0f0;
  border-radius: 10rpx;
  text-align: center;
}
`;

      if (!styles.includes('module-container')) {
        styles += moduleStyles;
        fs.writeFileSync(wxssPath, styles);
        console.log('  ✅ 页面样式已更新');
        
        this.deploymentResults.push({
          type: 'style_update',
          file: 'production.wxss',
          status: 'success'
        });
      } else {
        console.log('  ⏭️  页面样式已包含模块化样式');
      }
    }
  }

  /**
   * 更新页面模板
   */
  async updatePageTemplate() {
    const wxmlPath = path.join(this.rootPath, this.deploymentConfig.targetFiles.productionWxml);
    
    if (fs.existsSync(wxmlPath)) {
      let template = fs.readFileSync(wxmlPath, 'utf8');
      
      // 检查是否需要添加模块化结构
      if (!template.includes('module-container')) {
        console.log('  ⚠️  页面模板可能需要手动更新以支持模块化结构');
        
        this.deploymentResults.push({
          type: 'template_update',
          file: 'production.wxml',
          status: 'manual_required',
          message: '需要手动更新模板以支持模块化结构'
        });
      } else {
        console.log('  ✅ 页面模板已支持模块化结构');
      }
    }
  }

  /**
   * 验证部署结果
   */
  async validateDeployment() {
    console.log('🔍 验证部署结果...');

    const validationResults = [];

    // 验证主页面文件
    const productionPath = path.join(this.rootPath, this.deploymentConfig.targetFiles.production);
    if (fs.existsSync(productionPath)) {
      const content = fs.readFileSync(productionPath, 'utf8');
      
      if (content.includes('EnhancedHealthModule') && content.includes('EnhancedMaterialModule')) {
        validationResults.push({ file: 'production.js', status: 'valid' });
        console.log('  ✅ 主页面文件验证通过');
      } else {
        validationResults.push({ file: 'production.js', status: 'invalid', reason: '缺少模块导入' });
        console.error('  ❌ 主页面文件验证失败：缺少模块导入');
      }
    } else {
      validationResults.push({ file: 'production.js', status: 'missing' });
      console.error('  ❌ 主页面文件不存在');
    }

    // 验证模块文件
    const moduleFiles = ['enhanced-health-module.js', 'enhanced-material-module.js'];
    for (const moduleFile of moduleFiles) {
      const modulePath = path.join(this.rootPath, `pages/production/modules/${moduleFile}`);
      
      if (fs.existsSync(modulePath)) {
        validationResults.push({ file: moduleFile, status: 'valid' });
        console.log(`  ✅ 模块文件验证通过: ${moduleFile}`);
      } else {
        validationResults.push({ file: moduleFile, status: 'missing' });
        console.error(`  ❌ 模块文件不存在: ${moduleFile}`);
      }
    }

    const validCount = validationResults.filter(r => r.status === 'valid').length;
    const totalCount = validationResults.length;

    if (validCount === totalCount) {
      console.log('✅ 所有文件验证通过\n');
    } else {
      console.warn(`⚠️  ${totalCount - validCount}/${totalCount} 个文件验证失败\n`);
    }

    this.validationResults = validationResults;
  }

  /**
   * 生成部署报告
   */
  generateDeploymentReport() {
    console.log('📊 生成部署报告...');

    const report = {
      timestamp: new Date().toISOString(),
      deploymentStatus: this.validationResults.every(r => r.status === 'valid') ? 'success' : 'partial',
      summary: {
        filesDeployed: this.deploymentResults.filter(r => r.status === 'success').length,
        filesUpdated: this.deploymentResults.filter(r => r.type === 'config_update').length,
        validationPassed: this.validationResults.filter(r => r.status === 'valid').length,
        validationTotal: this.validationResults.length
      },
      deploymentResults: this.deploymentResults,
      validationResults: this.validationResults,
      backupPath: this.backupPath,
      features: [
        '模块化架构',
        '权限集成',
        '配置管理',
        '性能优化',
        '错误处理'
      ],
      nextSteps: [
        '测试页面功能',
        '验证模块交互',
        '检查性能表现',
        '更新文档'
      ]
    };

    // 保存报告
    const reportPath = path.join(this.rootPath, 'production-deployment-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log('='.repeat(60));
    console.log('📋 生产页面模块化部署报告');
    console.log('='.repeat(60));
    console.log(`部署状态: ${report.deploymentStatus}`);
    console.log(`文件部署: ${report.summary.filesDeployed} 个`);
    console.log(`配置更新: ${report.summary.filesUpdated} 个`);
    console.log(`验证通过: ${report.summary.validationPassed}/${report.summary.validationTotal} 个`);
    console.log(`备份路径: ${report.backupPath}`);
    console.log('='.repeat(60));

    if (report.deploymentStatus === 'success') {
      console.log('🎉 生产页面模块化部署成功！');
    } else {
      console.log('⚠️  部分功能可能需要手动调整');
    }

    console.log(`\n📋 详细报告已保存: ${reportPath}`);
  }

  /**
   * 回滚部署
   */
  async rollbackDeployment() {
    console.log('🔄 回滚部署更改...');
    
    if (this.backupPath && fs.existsSync(this.backupPath)) {
      // 从备份恢复文件
      for (const [key, filePath] of Object.entries(this.deploymentConfig.targetFiles)) {
        const backupFilePath = path.join(this.backupPath, filePath);
        const targetPath = path.join(this.rootPath, filePath);
        
        if (fs.existsSync(backupFilePath)) {
          fs.copyFileSync(backupFilePath, targetPath);
          console.log(`  ✅ 恢复 ${filePath}`);
        }
      }
      
      console.log('✅ 回滚完成');
    } else {
      console.log('⚠️  备份不存在，无法自动回滚');
    }
  }
}

// 导出部署器
module.exports = ProductionPageDeployer;

// 如果直接运行此脚本
if (require.main === module) {
  const deployer = new ProductionPageDeployer();
  
  deployer.deploy().then(() => {
    console.log('\n🎉 生产页面模块化部署完成！');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ 部署失败:', error);
    process.exit(1);
  });
}
