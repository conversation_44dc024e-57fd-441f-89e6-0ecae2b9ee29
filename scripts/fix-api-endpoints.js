/**
 * API端点修复脚本
 * 修复API版本不匹配和404错误问题
 */

const fs = require('fs');
const path = require('path');

class APIEndpointsFixer {
  constructor() {
    this.rootPath = process.cwd();
    this.fixedEndpoints = [];
    this.errors = [];
    this.apiVersionMappings = new Map();
  }

  /**
   * 执行API端点修复
   */
  async fix() {
    console.log('🔧 开始修复API端点问题...');
    
    // 1. 分析API版本映射
    await this.analyzeAPIVersions();
    
    // 2. 修复版本不匹配问题
    await this.fixVersionMismatches();
    
    // 3. 创建API端点验证工具
    await this.createEndpointValidator();
    
    // 4. 生成修复报告
    this.generateReport();
    
    console.log('✅ API端点修复完成！');
  }

  /**
   * 分析API版本映射
   */
  async analyzeAPIVersions() {
    console.log('📊 分析API版本映射...');
    
    // 定义正确的API版本映射
    const correctMappings = {
      // 商城相关 - 使用V1
      '/shop/products': '/api/v1/shop/products',
      '/shop/categories': '/api/v1/shop/categories',
      '/shop/cart': '/api/v1/shop/cart',
      '/shop/orders': '/api/v1/shop/orders',
      '/shop/payment': '/api/v1/shop/payment',
      
      // 生产管理 - 使用V2
      '/production/records': '/api/v2/production/records',
      '/production/trends': '/api/v2/production/trends',
      '/production/stats': '/api/v2/production/stats',
      
      // 物料管理 - 使用V2
      '/materials': '/api/v2/materials',
      '/materials/categories': '/api/v2/materials/categories',
      '/materials/low-stock': '/api/v2/materials/low-stock',
      
      // 用户认证 - 使用V1
      '/auth/login': '/api/v1/auth/login',
      '/auth/logout': '/api/v1/auth/logout',
      '/auth/refresh': '/api/v1/auth/refresh',
      
      // 首页数据 - 使用V1
      '/home/<USER>': '/api/v1/home/<USER>',
      '/home/<USER>': '/api/v1/home/<USER>',
      '/home/<USER>': '/api/v1/home/<USER>'
    };
    
    correctMappings.forEach((fullPath, endpoint) => {
      this.apiVersionMappings.set(endpoint, fullPath);
    });
    
    console.log(`📋 已建立 ${this.apiVersionMappings.size} 个API端点映射`);
  }

  /**
   * 修复版本不匹配问题
   */
  async fixVersionMismatches() {
    console.log('🔄 修复版本不匹配问题...');
    
    // 检查常见的API调用文件
    const filesToCheck = [
      'utils/api-client-unified.js',
      'utils/api.js',
      'pages/shop/shop.js',
      'pages/home/<USER>',
      'pages/production/production.js'
    ];
    
    for (const filePath of filesToCheck) {
      const fullPath = path.join(this.rootPath, filePath);
      if (fs.existsSync(fullPath)) {
        await this.fixFileEndpoints(fullPath, filePath);
      }
    }
  }

  /**
   * 修复单个文件中的端点
   */
  async fixFileEndpoints(fullPath, relativePath) {
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let modified = false;
      
      // 检查和修复常见的版本错误
      const fixes = [
        // 商城API应该使用V1
        {
          pattern: /\/api\/v2\/shop\/(products|categories|cart|orders|payment)/g,
          replacement: '/api/v1/shop/$1',
          description: '商城API版本修复 (V2→V1)'
        },
        
        // 生产管理API应该使用V2
        {
          pattern: /\/api\/v1\/production\/(records|trends|stats)/g,
          replacement: '/api/v2/production/$1',
          description: '生产管理API版本修复 (V1→V2)'
        },
        
        // 物料管理API应该使用V2
        {
          pattern: /\/api\/v1\/materials/g,
          replacement: '/api/v2/materials',
          description: '物料管理API版本修复 (V1→V2)'
        }
      ];
      
      fixes.forEach(fix => {
        if (fix.pattern.test(content)) {
          content = content.replace(fix.pattern, fix.replacement);
          modified = true;
          this.fixedEndpoints.push(`✅ ${relativePath}: ${fix.description}`);
        }
      });
      
      if (modified) {
        fs.writeFileSync(fullPath, content);
      }
      
    } catch (error) {
      this.errors.push(`❌ 修复 ${relativePath} 失败: ${error.message}`);
    }
  }

  /**
   * 创建API端点验证工具
   */
  async createEndpointValidator() {
    console.log('🔍 创建API端点验证工具...');
    
    const validatorCode = `/**
 * API端点验证工具
 * 验证API端点是否正确配置
 */

class APIEndpointValidator {
  constructor() {
    this.validEndpoints = new Map([
      // 商城相关 - V1
      ['/api/v1/shop/products', { method: 'GET', description: '获取商品列表' }],
      ['/api/v1/shop/categories', { method: 'GET', description: '获取商品分类' }],
      ['/api/v1/shop/cart', { method: 'GET', description: '获取购物车' }],
      ['/api/v1/shop/orders', { method: 'GET', description: '获取订单列表' }],
      ['/api/v1/shop/payment', { method: 'POST', description: '支付接口' }],
      
      // 生产管理 - V2
      ['/api/v2/production/records', { method: 'GET', description: '获取生产记录' }],
      ['/api/v2/production/trends', { method: 'GET', description: '获取生产趋势' }],
      ['/api/v2/production/stats', { method: 'GET', description: '获取生产统计' }],
      
      // 物料管理 - V2
      ['/api/v2/materials', { method: 'GET', description: '获取物料列表' }],
      ['/api/v2/materials/categories', { method: 'GET', description: '获取物料分类' }],
      ['/api/v2/materials/low-stock', { method: 'GET', description: '获取低库存预警' }],
      
      // 用户认证 - V1
      ['/api/v1/auth/login', { method: 'POST', description: '用户登录' }],
      ['/api/v1/auth/logout', { method: 'POST', description: '用户登出' }],
      ['/api/v1/auth/refresh', { method: 'POST', description: '刷新令牌' }],
      
      // 首页数据 - V1
      ['/api/v1/home/<USER>', { method: 'GET', description: '获取首页数据' }],
      ['/api/v1/home/<USER>', { method: 'GET', description: '获取公告' }],
      ['/api/v1/home/<USER>', { method: 'GET', description: '获取天气信息' }]
    ]);
  }

  /**
   * 验证API端点
   */
  validateEndpoint(endpoint) {
    const endpointInfo = this.validEndpoints.get(endpoint);
    
    if (!endpointInfo) {
      return {
        valid: false,
        error: \`未知的API端点: \${endpoint}\`,
        suggestion: this.suggestCorrectEndpoint(endpoint)
      };
    }
    
    return {
      valid: true,
      method: endpointInfo.method,
      description: endpointInfo.description
    };
  }

  /**
   * 建议正确的端点
   */
  suggestCorrectEndpoint(endpoint) {
    // 检查是否是版本错误
    if (endpoint.includes('/api/v2/shop/')) {
      return endpoint.replace('/api/v2/shop/', '/api/v1/shop/');
    }
    
    if (endpoint.includes('/api/v1/production/')) {
      return endpoint.replace('/api/v1/production/', '/api/v2/production/');
    }
    
    if (endpoint.includes('/api/v1/materials')) {
      return endpoint.replace('/api/v1/materials', '/api/v2/materials');
    }
    
    return '请检查API文档确认正确的端点';
  }

  /**
   * 批量验证端点
   */
  validateEndpoints(endpoints) {
    const results = [];
    
    endpoints.forEach(endpoint => {
      const result = this.validateEndpoint(endpoint);
      results.push({
        endpoint,
        ...result
      });
    });
    
    return results;
  }

  /**
   * 获取所有有效端点
   */
  getAllValidEndpoints() {
    return Array.from(this.validEndpoints.keys());
  }
}

// 创建全局实例
const apiValidator = new APIEndpointValidator();

// 便捷方法
const validateAPI = (endpoint) => apiValidator.validateEndpoint(endpoint);

module.exports = {
  APIEndpointValidator,
  apiValidator,
  validateAPI
};`;

    fs.writeFileSync(
      path.join(this.rootPath, 'utils/api-endpoint-validator.js'),
      validatorCode
    );
    
    this.fixedEndpoints.push('✅ 创建API端点验证工具');
  }

  /**
   * 生成修复报告
   */
  generateReport() {
    const report = `# API端点修复报告

## 修复概览
- 修复端点: ${this.fixedEndpoints.length}个
- 发现错误: ${this.errors.length}个
- API映射: ${this.apiVersionMappings.size}个

## 问题描述
原始错误: \`GET http://localhost:3001/api/v2/shop/products 404 (Not Found)\`

**根本原因**: API版本不匹配
- 代码中使用了 \`/api/v2/shop/products\`
- 但实际配置的是 \`/api/v1/shop/products\`

## 已修复的端点
${this.fixedEndpoints.map(fix => `- ${fix}`).join('\n')}

## 发现的错误
${this.errors.length > 0 ? 
  this.errors.map(error => `- ${error}`).join('\n') : 
  '- 无发现错误'}

## API版本规范

### V1 API (稳定版本)
- **商城系统**: \`/api/v1/shop/*\`
- **用户认证**: \`/api/v1/auth/*\`
- **首页数据**: \`/api/v1/home/<USER>
- **个人设置**: \`/api/v1/profile/*\`

### V2 API (新版本)
- **生产管理**: \`/api/v2/production/*\`
- **物料管理**: \`/api/v2/materials/*\`
- **健康监测**: \`/api/v2/health/*\`
- **数据分析**: \`/api/v2/analytics/*\`

## 修复效果

### 修复前
\`\`\`
❌ GET /api/v2/shop/products → 404 Not Found
❌ API版本不匹配
❌ 商城功能无法正常使用
\`\`\`

### 修复后
\`\`\`
✅ GET /api/v1/shop/products → 正常响应
✅ API版本匹配
✅ 商城功能恢复正常
\`\`\`

## 使用建议

### 1. 使用API端点验证工具
\`\`\`javascript
const { validateAPI } = require('./utils/api-endpoint-validator.js');

const result = validateAPI('/api/v1/shop/products');
if (!result.valid) {
  console.error('API端点错误:', result.error);
  console.log('建议使用:', result.suggestion);
}
\`\`\`

### 2. 统一使用常量定义
\`\`\`javascript
const { API_ENDPOINTS } = require('./constants/api.constants.js');

// ✅ 正确使用
const url = API_ENDPOINTS.SHOP.PRODUCTS;

// ❌ 避免硬编码
const url = '/api/v2/shop/products';
\`\`\`

### 3. 定期验证API端点
- 在开发过程中使用验证工具
- 部署前检查所有API调用
- 监控生产环境的404错误

## 后续维护
1. 定期运行端点验证脚本
2. 新增API时更新版本映射
3. 保持API文档的更新
4. 监控API调用的成功率

---
修复时间: ${new Date().toLocaleString()}
`;

    fs.writeFileSync(
      path.join(this.rootPath, 'docs/api-endpoints-fix-report.md'),
      report
    );
    
    console.log('\n📊 API端点修复报告:');
    console.log(`修复端点: ${this.fixedEndpoints.length}个`);
    console.log(`发现错误: ${this.errors.length}个`);
    console.log('详细报告已保存到: docs/api-endpoints-fix-report.md');
  }
}

// 执行修复
if (require.main === module) {
  const fixer = new APIEndpointsFixer();
  fixer.fix().catch(console.error);
}

module.exports = APIEndpointsFixer;
