/**
 * 统一组件库 - 标准化UI组件
 */

// 统一按钮组件
Component({
  properties: {
    type: {
      type: String,
      value: 'primary' // primary, secondary, success, warning, error
    },
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    disabled: {
      type: Boolean,
      value: false
    },
    loading: {
      type: Boolean,
      value: false
    },
    block: {
      type: Boolean,
      value: false
    }
  },
  
  methods: {
    onTap(e) {
      if (this.data.disabled || this.data.loading) {
        return;
      }
      this.triggerEvent('tap', e.detail);
    }
  }
});

// 统一输入框组件
Component({
  properties: {
    value: String,
    placeholder: String,
    type: {
      type: String,
      value: 'text'
    },
    maxlength: {
      type: Number,
      value: 140
    },
    required: {
      type: <PERSON>olean,
      value: false
    },
    disabled: {
      type: Boolean,
      value: false
    }
  },
  
  methods: {
    onInput(e) {
      this.setData({ value: e.detail.value });
      this.triggerEvent('input', e.detail);
    },
    
    onBlur(e) {
      this.triggerEvent('blur', e.detail);
    },
    
    onFocus(e) {
      this.triggerEvent('focus', e.detail);
    }
  }
});

// 统一加载组件
Component({
  properties: {
    loading: {
      type: Boolean,
      value: false
    },
    text: {
      type: String,
      value: '加载中...'
    },
    size: {
      type: String,
      value: 'medium'
    }
  }
});