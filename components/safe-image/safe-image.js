/**
 * 安全图片组件
 * 自动处理图片加载失败的情况
 */

const { ImageUtils } = require('../../utils/image-fallback-handler.js');

Component({
  properties: {
    // 图片路径
    src: {
      type: String,
      value: '',
      observer: function(newVal) {
        if (newVal) {
          this.setData({
            safeSrc: ImageUtils.getSafeImagePath(newVal),
            originalSrc: newVal
          });
        }
      }
    },
    
    // 图片模式
    mode: {
      type: String,
      value: 'aspectFit'
    },
    
    // 是否懒加载
    lazyLoad: {
      type: Boolean,
      value: false
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    
    // 默认图片
    defaultSrc: {
      type: String,
      value: '/images/icons/info.png'
    }
  },

  data: {
    safeSrc: '',
    originalSrc: '',
    loadFailed: false,
    retryCount: 0
  },

  methods: {
    /**
     * 图片加载成功
     */
    onImageLoad(e) {
      this.setData({
        loadFailed: false,
        retryCount: 0
      });
      
      this.triggerEvent('load', e.detail);
    },

    /**
     * 图片加载失败
     */
    onImageError(e) {
      console.warn('图片加载失败:', this.data.originalSrc);
      
      const { retryCount } = this.data;
      
      // 尝试使用后备图片
      if (retryCount === 0) {
        const fallbackSrc = ImageUtils.handleError(this.data.originalSrc);
        if (fallbackSrc && fallbackSrc !== this.data.safeSrc) {
          this.setData({
            safeSrc: fallbackSrc,
            retryCount: retryCount + 1
          });
          return;
        }
      }
      
      // 使用默认图片
      if (retryCount === 1 && this.data.defaultSrc) {
        this.setData({
          safeSrc: this.data.defaultSrc,
          retryCount: retryCount + 1
        });
        return;
      }
      
      // 标记加载失败
      this.setData({
        loadFailed: true
      });
      
      this.triggerEvent('error', {
        ...e.detail,
        originalSrc: this.data.originalSrc,
        retryCount: this.data.retryCount
      });
    },

    /**
     * 重试加载
     */
    retryLoad() {
      this.setData({
        safeSrc: this.data.originalSrc,
        loadFailed: false,
        retryCount: 0
      });
    }
  }
});
