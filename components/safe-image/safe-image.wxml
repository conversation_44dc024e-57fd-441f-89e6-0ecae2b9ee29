<!--安全图片组件-->
<view class="safe-image-container {{customClass}}">
  <image 
    wx:if="{{!loadFailed}}"
    src="{{safeSrc}}" 
    mode="{{mode}}"
    lazy-load="{{lazyLoad}}"
    class="safe-image"
    bindload="onImageLoad"
    binderror="onImageError"
  />
  
  <!-- 加载失败时的占位符 -->
  <view wx:else class="image-placeholder" bindtap="retryLoad">
    <view class="placeholder-icon">📷</view>
    <view class="placeholder-text">图片加载失败</view>
    <view class="placeholder-retry">点击重试</view>
  </view>
</view>
