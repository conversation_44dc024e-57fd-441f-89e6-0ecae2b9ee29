/**
 * 权限检查组件（优化版）
 * Permission Check Component (Optimized)
 *
 * 用于在页面中进行权限检查和条件渲染
 * 集成多层缓存策略，大幅提升性能
 */

const { getCurrentUserPermissions, hasPermission, FinancePermissionChecker } = require('../../utils/role-permission');
const permissionCacheManager = require('../../utils/permission-cache-manager');

Component({
  /**
   * 组件属性列表
   */
  properties: {
    // 必需的权限（字符串或数组）
    permission: {
      type: null, // 可以是String或Array
      value: null
    },
    
    // 是否需要所有权限（当permission为数组时）
    requireAll: {
      type: Boolean,
      value: false
    },
    
    // 权限检查模式
    mode: {
      type: String,
      value: 'show', // 'show' | 'hide' | 'disable'
      optionalTypes: [String]
    },
    
    // 无权限时的提示文本
    noPermissionText: {
      type: String,
      value: '权限不足'
    },
    
    // 是否显示无权限提示
    showNoPermissionTip: {
      type: Boolean,
      value: true
    },
    
    // 检查的资源ID（用于资源级权限控制）
    resourceId: {
      type: String,
      value: ''
    },
    
    // 资源拥有者ID（用于判断是否为资源拥有者）
    resourceOwnerId: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件数据
   */
  data: {
    hasPermission: false,
    loading: true,
    userInfo: null
  },

  /**
   * 组件方法列表
   */
  methods: {
    /**
     * 检查权限（优化版 - 使用缓存）
     */
    async checkPermission() {
      try {
        this.setData({ loading: true });

        const { permission, requireAll, resourceId, resourceOwnerId } = this.properties;

        // 如果没有权限要求，直接通过
        if (!permission) {
          const userPermissions = await permissionCacheManager.getUserPermissions(
            wx.getStorageSync('userId') || 'anonymous'
          );

          this.setData({
            hasPermission: true,
            loading: false,
            userInfo: userPermissions
          });
          return;
        }

        // 获取当前用户ID
        const currentUserId = wx.getStorageSync('userId') || 'anonymous';

        // 使用缓存管理器进行权限检查
        const hasRequiredPermission = await permissionCacheManager.checkPermission(
          currentUserId,
          permission,
          {
            requireAll,
            resourceId,
            resourceOwnerId
          }
        );

        // 获取用户信息（从缓存）
        const userPermissions = await permissionCacheManager.getUserPermissions(currentUserId);

        this.setData({
          hasPermission: hasRequiredPermission,
          loading: false,
          userInfo: userPermissions
        });

        // 触发权限检查完成事件
        this.triggerEvent('permissionChecked', {
          hasPermission: hasRequiredPermission,
          userInfo: userPermissions,
          cacheStats: permissionCacheManager.getStats() // 添加缓存统计
        });

      } catch (error) {
        console.error('权限检查失败:', error);
        this.setData({
          hasPermission: false,
          loading: false
        });

        // 触发错误事件
        this.triggerEvent('permissionError', { error });
      }
    },

    /**
     * 批量权限检查（新增方法）
     */
    async batchCheckPermissions(permissionChecks) {
      try {
        const currentUserId = wx.getStorageSync('userId') || 'anonymous';
        const results = await permissionCacheManager.batchCheckPermissions(
          currentUserId,
          permissionChecks
        );

        this.triggerEvent('batchPermissionChecked', {
          results,
          cacheStats: permissionCacheManager.getStats()
        });

        return results;
      } catch (error) {
        console.error('批量权限检查失败:', error);
        this.triggerEvent('permissionError', { error });
        return [];
      }
    },

    /**
     * 清除权限缓存
     */
    clearPermissionCache() {
      const currentUserId = wx.getStorageSync('userId') || 'anonymous';
      permissionCacheManager.clearUserPermissions(currentUserId);
      permissionCacheManager.clearPermissionChecks(currentUserId);

      // 重新检查权限
      this.checkPermission();
    },

    /**
     * 获取缓存统计信息
     */
    getCacheStats() {
      return permissionCacheManager.getStats();
    },

    /**
     * 处理无权限点击（静默处理）
     */
    onNoPermissionTap() {
      // 静默处理，不显示提示
      console.warn('权限不足:', this.properties.noPermissionText);

      this.triggerEvent('noPermission', {
        permission: this.properties.permission,
        userInfo: this.data.userInfo,
        cacheStats: permissionCacheManager.getStats()
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    /**
     * 组件实例被创建时执行
     */
    created() {
      // 组件实例被创建
    },

    /**
     * 组件实例进入页面节点树时执行
     */
    attached() {
      this.checkPermission();
    },

    /**
     * 组件实例被移动到节点树另一个位置时执行
     */
    moved() {
      // 组件实例被移动到节点树另一个位置
    },

    /**
     * 组件实例从页面节点树移除时执行
     */
    detached() {
      // 组件实例从页面节点树移除
    }
  },

  /**
   * 组件所在页面生命周期
   */
  pageLifetimes: {
    /**
     * 组件所在页面被显示时执行
     */
    show() {
      // 页面显示时重新检查权限
      this.checkPermission();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'permission, requireAll, resourceId, resourceOwnerId': function(permission, requireAll, resourceId, resourceOwnerId) {
      // 当权限相关属性变化时重新检查权限
      if (this.data.loading === false) {
        this.checkPermission();
      }
    }
  }
});