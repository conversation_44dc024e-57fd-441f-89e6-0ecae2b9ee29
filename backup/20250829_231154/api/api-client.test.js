/**
 * API客户端测试套件
 * API Client Test Suite
 */

const { suite, Assert, Mock } = require('../framework/test-runner.js');

// 模拟微信小程序环境
global.wx = {
  request: Mock.fn(),
  getStorageSync: Mock.fn(),
  setStorageSync: Mock.fn(),
  removeStorageSync: Mock.fn(),
  showToast: Mock.fn(),
  showModal: Mock.fn()
};

// 模拟getApp
global.getApp = Mock.fn(() => ({
  globalData: {
    userInfo: { id: 1, token: 'test-token' },
    systemInfo: { platform: 'devtools' }
  }
}));

suite('API客户端测试', (context) => {
  let apiClient;

  context.beforeAll(async () => {
    // 动态导入API客户端
    const { apiClient: client } = require('../../utils/api-client-final.js');
    apiClient = client;
  });

  context.beforeEach(() => {
    // 重置所有mock
    wx.request.calls = [];
    wx.request.callCount = 0;
    wx.getStorageSync.calls = [];
    wx.setStorageSync.calls = [];
  });

  context.test('应该正确初始化API客户端', () => {
    Assert.true(typeof apiClient === 'object', 'API客户端应该是对象');
    Assert.true(typeof apiClient.get === 'function', '应该有get方法');
    Assert.true(typeof apiClient.post === 'function', '应该有post方法');
    Assert.true(typeof apiClient.put === 'function', '应该有put方法');
    Assert.true(typeof apiClient.delete === 'function', '应该有delete方法');
  });

  context.test('GET请求应该正确发送', async () => {
    // 模拟成功响应
    wx.request.mockImplementation = (options) => {
      setTimeout(() => {
        options.success({
          statusCode: 200,
          data: { success: true, data: { id: 1, name: 'test' } }
        });
      }, 10);
    };

    const result = await apiClient.get('/api/test');
    
    Assert.equal(wx.request.callCount, 1, '应该调用一次wx.request');
    Assert.equal(wx.request.calls[0][0].method, 'GET', '请求方法应该是GET');
    Assert.true(result.success, '响应应该成功');
    Assert.deepEqual(result.data, { id: 1, name: 'test' }, '数据应该正确');
  });

  context.test('POST请求应该正确发送数据', async () => {
    const testData = { name: 'test', value: 123 };
    
    wx.request.mockImplementation = (options) => {
      setTimeout(() => {
        options.success({
          statusCode: 200,
          data: { success: true, data: { id: 2, ...options.data } }
        });
      }, 10);
    };

    const result = await apiClient.post('/api/create', testData);
    
    Assert.equal(wx.request.callCount, 1, '应该调用一次wx.request');
    Assert.equal(wx.request.calls[0][0].method, 'POST', '请求方法应该是POST');
    Assert.deepEqual(wx.request.calls[0][0].data, testData, '请求数据应该正确');
    Assert.true(result.success, '响应应该成功');
  });

  context.test('应该正确处理请求错误', async () => {
    wx.request.mockImplementation = (options) => {
      setTimeout(() => {
        options.fail({
          errMsg: 'request:fail network error'
        });
      }, 10);
    };

    try {
      await apiClient.get('/api/error');
      Assert.true(false, '应该抛出错误');
    } catch (error) {
      Assert.true(error.message.includes('network error'), '错误信息应该包含网络错误');
    }
  });

  context.test('应该正确处理HTTP错误状态码', async () => {
    wx.request.mockImplementation = (options) => {
      setTimeout(() => {
        options.success({
          statusCode: 404,
          data: { success: false, message: 'Not Found' }
        });
      }, 10);
    };

    try {
      await apiClient.get('/api/notfound');
      Assert.true(false, '应该抛出错误');
    } catch (error) {
      Assert.true(error.message.includes('404'), '错误信息应该包含状态码');
    }
  });

  context.test('应该正确添加认证头', async () => {
    wx.request.mockImplementation = (options) => {
      setTimeout(() => {
        options.success({
          statusCode: 200,
          data: { success: true, data: {} }
        });
      }, 10);
    };

    await apiClient.get('/api/protected');
    
    const requestOptions = wx.request.calls[0][0];
    Assert.true(requestOptions.header, '应该有请求头');
    Assert.true(requestOptions.header.Authorization, '应该有Authorization头');
    Assert.true(requestOptions.header.Authorization.includes('test-token'), '应该包含token');
  });

  context.test('应该正确处理超时', async () => {
    wx.request.mockImplementation = (options) => {
      // 不调用success或fail，模拟超时
    };

    try {
      await apiClient.get('/api/timeout', { timeout: 100 });
      Assert.true(false, '应该抛出超时错误');
    } catch (error) {
      Assert.true(error.message.includes('timeout') || error.message.includes('超时'), '错误信息应该包含超时');
    }
  });

  context.test('应该支持请求重试', async () => {
    let callCount = 0;
    wx.request.mockImplementation = (options) => {
      callCount++;
      setTimeout(() => {
        if (callCount < 3) {
          options.fail({ errMsg: 'request:fail network error' });
        } else {
          options.success({
            statusCode: 200,
            data: { success: true, data: {} }
          });
        }
      }, 10);
    };

    const result = await apiClient.get('/api/retry', { retries: 2 });
    
    Assert.equal(callCount, 3, '应该重试2次后成功');
    Assert.true(result.success, '最终应该成功');
  });

  context.test('应该正确缓存GET请求', async () => {
    wx.request.mockImplementation = (options) => {
      setTimeout(() => {
        options.success({
          statusCode: 200,
          data: { success: true, data: { timestamp: Date.now() } }
        });
      }, 10);
    };

    // 第一次请求
    const result1 = await apiClient.get('/api/cache', { cache: true });
    
    // 第二次请求（应该使用缓存）
    const result2 = await apiClient.get('/api/cache', { cache: true });
    
    Assert.equal(wx.request.callCount, 1, '应该只发送一次请求');
    Assert.deepEqual(result1.data, result2.data, '缓存的数据应该相同');
  });

  context.test('应该正确处理并发请求', async () => {
    wx.request.mockImplementation = (options) => {
      setTimeout(() => {
        options.success({
          statusCode: 200,
          data: { success: true, data: { url: options.url } }
        });
      }, Math.random() * 50);
    };

    const promises = [
      apiClient.get('/api/concurrent/1'),
      apiClient.get('/api/concurrent/2'),
      apiClient.get('/api/concurrent/3')
    ];

    const results = await Promise.all(promises);
    
    Assert.equal(results.length, 3, '应该返回3个结果');
    Assert.equal(wx.request.callCount, 3, '应该发送3次请求');
    results.forEach((result, index) => {
      Assert.true(result.success, `第${index + 1}个请求应该成功`);
    });
  });

  context.test('应该正确处理请求拦截器', async () => {
    let interceptorCalled = false;
    
    // 添加请求拦截器
    apiClient.addRequestInterceptor((config) => {
      interceptorCalled = true;
      config.header = config.header || {};
      config.header['X-Custom'] = 'test-value';
      return config;
    });

    wx.request.mockImplementation = (options) => {
      setTimeout(() => {
        options.success({
          statusCode: 200,
          data: { success: true, data: {} }
        });
      }, 10);
    };

    await apiClient.get('/api/interceptor');
    
    Assert.true(interceptorCalled, '请求拦截器应该被调用');
    const requestOptions = wx.request.calls[0][0];
    Assert.equal(requestOptions.header['X-Custom'], 'test-value', '自定义头应该被添加');
  });

  context.test('应该正确处理响应拦截器', async () => {
    let interceptorCalled = false;
    
    // 添加响应拦截器
    apiClient.addResponseInterceptor((response) => {
      interceptorCalled = true;
      response.data.intercepted = true;
      return response;
    });

    wx.request.mockImplementation = (options) => {
      setTimeout(() => {
        options.success({
          statusCode: 200,
          data: { success: true, data: {} }
        });
      }, 10);
    };

    const result = await apiClient.get('/api/interceptor');
    
    Assert.true(interceptorCalled, '响应拦截器应该被调用');
    Assert.true(result.intercepted, '响应应该被拦截器修改');
  });
});
