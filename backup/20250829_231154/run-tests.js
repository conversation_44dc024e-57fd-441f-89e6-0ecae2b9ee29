#!/usr/bin/env node

/**
 * 测试运行脚本
 * Test Runner Script
 */

const fs = require('fs');
const path = require('path');
const { testRunner, configure } = require('./framework/test-runner.js');
const testConfig = require('./test-config.js');

class TestSuiteRunner {
  constructor() {
    this.config = testConfig;
    this.results = {
      suites: {},
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        duration: 0
      }
    };
  }

  /**
   * 运行所有测试
   */
  async runAll() {
    console.log('🚀 智慧养鹅云开发 - 自动化测试套件');
    console.log('=' .repeat(60));
    
    const startTime = Date.now();
    
    try {
      // 设置测试环境
      this.setupTestEnvironment();
      
      // 运行不同类型的测试
      await this.runUnitTests();
      await this.runIntegrationTests();
      await this.runE2ETests();
      await this.runPerformanceTests();
      
      // 生成测试报告
      this.generateReports();
      
    } catch (error) {
      console.error('❌ 测试运行失败:', error);
      process.exit(1);
    }
    
    const endTime = Date.now();
    this.results.summary.duration = endTime - startTime;
    
    // 打印最终结果
    this.printFinalResults();
    
    // 检查是否通过
    const success = this.results.summary.failed === 0;
    process.exit(success ? 0 : 1);
  }

  /**
   * 设置测试环境
   */
  setupTestEnvironment() {
    console.log('🔧 设置测试环境...');
    
    // 设置全局变量
    if (this.config.environment.mockWx) {
      this.setupWxMock();
    }
    
    if (this.config.environment.mockGetApp) {
      this.setupGetAppMock();
    }
    
    // 配置测试运行器
    configure({
      timeout: this.config.runner.timeout,
      retries: this.config.runner.retries,
      parallel: this.config.runner.parallel,
      verbose: this.config.runner.verbose
    });
    
    console.log('✅ 测试环境设置完成\n');
  }

  /**
   * 设置微信API模拟
   */
  setupWxMock() {
    const { Mock } = require('./framework/test-runner.js');

    global.wx = {};
    Object.keys(this.config.mocks.wx).forEach(api => {
      const mockFn = Mock.fn();
      // 确保mock函数有正确的属性
      mockFn.calls = [];
      mockFn.callCount = 0;
      global.wx[api] = mockFn;
    });
  }

  /**
   * 设置getApp模拟
   */
  setupGetAppMock() {
    const { Mock } = require('./framework/test-runner.js');
    
    global.getApp = Mock.fn(() => this.config.mocks.getApp);
  }

  /**
   * 运行单元测试
   */
  async runUnitTests() {
    console.log('📦 运行单元测试...');
    
    // 加载单元测试文件
    this.loadTestFiles([
      './api/api-client.test.js',
      './business/health-module.test.js',
      './performance/cache-manager.test.js'
    ]);
    
    const results = await testRunner.run();
    this.results.suites.unit = results;
    this.updateSummary(results);
    
    console.log(`✅ 单元测试完成: ${results.passed}/${results.total} 通过\n`);
  }

  /**
   * 运行集成测试
   */
  async runIntegrationTests() {
    console.log('🔗 运行集成测试...');
    
    // 重置测试运行器
    testRunner.tests.clear();
    testRunner.suites.clear();
    
    // 这里可以加载集成测试文件
    // 暂时跳过，因为我们还没有创建集成测试
    
    const results = { total: 0, passed: 0, failed: 0, skipped: 0 };
    this.results.suites.integration = results;
    
    console.log('⏭️  集成测试跳过（暂未实现）\n');
  }

  /**
   * 运行端到端测试
   */
  async runE2ETests() {
    console.log('🎯 运行端到端测试...');
    
    // 重置测试运行器
    testRunner.tests.clear();
    testRunner.suites.clear();
    
    // 加载端到端测试文件
    this.loadTestFiles([
      './e2e/production-workflow.test.js'
    ]);
    
    const results = await testRunner.run();
    this.results.suites.e2e = results;
    this.updateSummary(results);
    
    console.log(`✅ 端到端测试完成: ${results.passed}/${results.total} 通过\n`);
  }

  /**
   * 运行性能测试
   */
  async runPerformanceTests() {
    console.log('⚡ 运行性能测试...');
    
    // 重置测试运行器
    testRunner.tests.clear();
    testRunner.suites.clear();
    
    // 这里可以加载性能测试文件
    // 暂时跳过，因为性能测试需要特殊环境
    
    const results = { total: 0, passed: 0, failed: 0, skipped: 0 };
    this.results.suites.performance = results;
    
    console.log('⏭️  性能测试跳过（需要特殊环境）\n');
  }

  /**
   * 加载测试文件
   */
  loadTestFiles(files) {
    files.forEach(file => {
      try {
        const filePath = path.resolve(__dirname, file);
        if (fs.existsSync(filePath)) {
          require(filePath);
          console.log(`📄 已加载: ${file}`);
        } else {
          console.warn(`⚠️  文件不存在: ${file}`);
        }
      } catch (error) {
        console.error(`❌ 加载测试文件失败: ${file}`, error.message);
      }
    });
  }

  /**
   * 更新汇总结果
   */
  updateSummary(results) {
    this.results.summary.total += results.total;
    this.results.summary.passed += results.passed;
    this.results.summary.failed += results.failed;
    this.results.summary.skipped += results.skipped;
  }

  /**
   * 生成测试报告
   */
  generateReports() {
    console.log('📊 生成测试报告...');
    
    // 确保报告目录存在
    const reportsDir = path.resolve(__dirname, 'reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // 生成JSON报告
    if (this.config.reporters.json.enabled) {
      this.generateJSONReport();
    }
    
    // 生成HTML报告
    if (this.config.reporters.html.enabled) {
      this.generateHTMLReport();
    }
    
    console.log('✅ 测试报告生成完成\n');
  }

  /**
   * 生成JSON报告
   */
  generateJSONReport() {
    const reportData = {
      timestamp: new Date().toISOString(),
      config: this.config,
      results: this.results,
      environment: {
        platform: this.config.environment.platform,
        nodeVersion: process.version,
        testFramework: 'custom'
      }
    };
    
    const outputFile = path.resolve(__dirname, this.config.reporters.json.outputFile);
    fs.writeFileSync(outputFile, JSON.stringify(reportData, null, 2));
    console.log(`📄 JSON报告: ${outputFile}`);
  }

  /**
   * 生成HTML报告
   */
  generateHTMLReport() {
    const htmlContent = this.generateHTMLContent();
    const outputDir = path.resolve(__dirname, this.config.reporters.html.outputDir);
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const outputFile = path.join(outputDir, 'index.html');
    fs.writeFileSync(outputFile, htmlContent);
    console.log(`🌐 HTML报告: ${outputFile}`);
  }

  /**
   * 生成HTML内容
   */
  generateHTMLContent() {
    const { summary, suites } = this.results;
    const successRate = summary.total > 0 ? (summary.passed / summary.total * 100).toFixed(2) : 0;
    
    return `
<!DOCTYPE html>
<html>
<head>
    <title>智慧养鹅云开发 - 测试报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { color: #28a745; }
        .failure { color: #dc3545; }
        .suite { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>智慧养鹅云开发 - 测试报告</h1>
        <p>生成时间: ${new Date().toLocaleString()}</p>
        <p>测试耗时: ${summary.duration}ms</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>总计</h3>
            <p>${summary.total}</p>
        </div>
        <div class="metric success">
            <h3>通过</h3>
            <p>${summary.passed}</p>
        </div>
        <div class="metric failure">
            <h3>失败</h3>
            <p>${summary.failed}</p>
        </div>
        <div class="metric">
            <h3>成功率</h3>
            <p>${successRate}%</p>
        </div>
    </div>
    
    <h2>测试套件详情</h2>
    ${Object.entries(suites).map(([name, suite]) => `
        <div class="suite">
            <h3>${name}</h3>
            <p>通过: ${suite.passed}/${suite.total}</p>
            <p>失败: ${suite.failed}</p>
        </div>
    `).join('')}
</body>
</html>`;
  }

  /**
   * 打印最终结果
   */
  printFinalResults() {
    const { summary } = this.results;
    const successRate = summary.total > 0 ? (summary.passed / summary.total * 100).toFixed(2) : 0;
    
    console.log('🏁 测试完成');
    console.log('=' .repeat(60));
    console.log(`📊 总计: ${summary.total}`);
    console.log(`✅ 通过: ${summary.passed}`);
    console.log(`❌ 失败: ${summary.failed}`);
    console.log(`⏭️  跳过: ${summary.skipped}`);
    console.log(`📈 成功率: ${successRate}%`);
    console.log(`⏱️  耗时: ${summary.duration}ms`);
    console.log('=' .repeat(60));
    
    if (summary.failed > 0) {
      console.log('❌ 测试失败，请检查错误信息');
    } else {
      console.log('🎉 所有测试通过！');
    }
  }
}

// 运行测试
if (require.main === module) {
  const runner = new TestSuiteRunner();
  runner.runAll().catch(error => {
    console.error('测试运行异常:', error);
    process.exit(1);
  });
}

module.exports = TestSuiteRunner;
