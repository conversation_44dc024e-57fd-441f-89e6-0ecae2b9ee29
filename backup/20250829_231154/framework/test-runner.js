/**
 * 智慧养鹅云开发 - 测试运行器
 * Test Runner for Smart Goose Cloud Development
 */

class TestRunner {
  constructor() {
    this.tests = new Map();
    this.suites = new Map();
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      errors: []
    };
    this.config = {
      timeout: 5000,
      retries: 0,
      parallel: false,
      verbose: true
    };
  }

  /**
   * 配置测试运行器
   */
  configure(options = {}) {
    this.config = { ...this.config, ...options };
    return this;
  }

  /**
   * 注册测试套件
   */
  suite(name, setupFn) {
    const suite = {
      name,
      tests: [],
      beforeEach: null,
      afterEach: null,
      beforeAll: null,
      afterAll: null,
      setup: setupFn
    };

    this.suites.set(name, suite);
    
    // 创建测试上下文
    const context = {
      test: (testName, testFn) => this.addTest(name, testName, testFn),
      beforeEach: (fn) => { suite.beforeEach = fn; },
      afterEach: (fn) => { suite.afterEach = fn; },
      beforeAll: (fn) => { suite.beforeAll = fn; },
      afterAll: (fn) => { suite.afterAll = fn; }
    };

    // 执行套件设置
    if (setupFn) {
      setupFn(context);
    }

    return this;
  }

  /**
   * 添加测试用例
   */
  addTest(suiteName, testName, testFn) {
    const suite = this.suites.get(suiteName);
    if (!suite) {
      throw new Error(`测试套件不存在: ${suiteName}`);
    }

    const test = {
      id: `${suiteName}::${testName}`,
      suiteName,
      name: testName,
      fn: testFn,
      timeout: this.config.timeout,
      retries: this.config.retries,
      status: 'pending'
    };

    suite.tests.push(test);
    this.tests.set(test.id, test);
    
    return this;
  }

  /**
   * 运行所有测试
   */
  async run() {
    console.log('🚀 开始运行测试...\n');
    
    this.resetResults();
    const startTime = Date.now();

    try {
      if (this.config.parallel) {
        await this.runParallel();
      } else {
        await this.runSequential();
      }
    } catch (error) {
      console.error('❌ 测试运行器异常:', error);
      this.results.errors.push({
        type: 'runner_error',
        message: error.message,
        stack: error.stack
      });
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    this.printResults(duration);
    return this.results;
  }

  /**
   * 顺序运行测试
   */
  async runSequential() {
    for (const [suiteName, suite] of this.suites.entries()) {
      await this.runSuite(suite);
    }
  }

  /**
   * 并行运行测试
   */
  async runParallel() {
    const promises = Array.from(this.suites.values()).map(suite => 
      this.runSuite(suite)
    );
    await Promise.allSettled(promises);
  }

  /**
   * 运行测试套件
   */
  async runSuite(suite) {
    console.log(`📦 运行测试套件: ${suite.name}`);

    try {
      // 执行 beforeAll
      if (suite.beforeAll) {
        await this.executeWithTimeout(suite.beforeAll, this.config.timeout);
      }

      // 运行套件中的所有测试
      for (const test of suite.tests) {
        await this.runTest(test, suite);
      }

      // 执行 afterAll
      if (suite.afterAll) {
        await this.executeWithTimeout(suite.afterAll, this.config.timeout);
      }

    } catch (error) {
      console.error(`❌ 测试套件 ${suite.name} 执行失败:`, error);
      this.results.errors.push({
        type: 'suite_error',
        suite: suite.name,
        message: error.message,
        stack: error.stack
      });
    }

    console.log(''); // 空行分隔
  }

  /**
   * 运行单个测试
   */
  async runTest(test, suite) {
    this.results.total++;
    
    let attempt = 0;
    const maxAttempts = test.retries + 1;

    while (attempt < maxAttempts) {
      try {
        // 执行 beforeEach
        if (suite.beforeEach) {
          await this.executeWithTimeout(suite.beforeEach, this.config.timeout);
        }

        // 执行测试
        const startTime = Date.now();
        await this.executeWithTimeout(test.fn, test.timeout);
        const duration = Date.now() - startTime;

        // 执行 afterEach
        if (suite.afterEach) {
          await this.executeWithTimeout(suite.afterEach, this.config.timeout);
        }

        // 测试通过
        test.status = 'passed';
        test.duration = duration;
        this.results.passed++;

        if (this.config.verbose) {
          console.log(`  ✅ ${test.name} (${duration}ms)`);
        }

        return;

      } catch (error) {
        attempt++;
        
        if (attempt >= maxAttempts) {
          // 所有重试都失败了
          test.status = 'failed';
          test.error = error;
          this.results.failed++;
          this.results.errors.push({
            type: 'test_error',
            suite: test.suiteName,
            test: test.name,
            message: error.message,
            stack: error.stack
          });

          if (this.config.verbose) {
            console.log(`  ❌ ${test.name} - ${error.message}`);
          }
        } else {
          // 重试
          if (this.config.verbose) {
            console.log(`  🔄 ${test.name} - 重试 ${attempt}/${test.retries}`);
          }
        }
      }
    }
  }

  /**
   * 带超时执行函数
   */
  async executeWithTimeout(fn, timeout) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`测试超时 (${timeout}ms)`));
      }, timeout);

      Promise.resolve(fn())
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * 重置测试结果
   */
  resetResults() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      errors: []
    };
  }

  /**
   * 打印测试结果
   */
  printResults(duration) {
    console.log('📊 测试结果汇总');
    console.log('='.repeat(50));
    console.log(`总计: ${this.results.total}`);
    console.log(`✅ 通过: ${this.results.passed}`);
    console.log(`❌ 失败: ${this.results.failed}`);
    console.log(`⏭️  跳过: ${this.results.skipped}`);
    console.log(`⏱️  耗时: ${duration}ms`);
    
    const successRate = this.results.total > 0 
      ? ((this.results.passed / this.results.total) * 100).toFixed(2)
      : 0;
    console.log(`📈 成功率: ${successRate}%`);

    if (this.results.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      this.results.errors.forEach((error, index) => {
        console.log(`${index + 1}. [${error.type}] ${error.message}`);
        if (error.suite) console.log(`   套件: ${error.suite}`);
        if (error.test) console.log(`   测试: ${error.test}`);
      });
    }

    console.log('='.repeat(50));
  }

  /**
   * 获取测试统计
   */
  getStats() {
    return {
      ...this.results,
      successRate: this.results.total > 0 
        ? (this.results.passed / this.results.total) * 100
        : 0,
      suiteCount: this.suites.size,
      testCount: this.tests.size
    };
  }
}

// 断言库
class Assert {
  static equal(actual, expected, message = '') {
    if (actual !== expected) {
      throw new Error(`断言失败: ${message}\n期望: ${expected}\n实际: ${actual}`);
    }
  }

  static notEqual(actual, expected, message = '') {
    if (actual === expected) {
      throw new Error(`断言失败: ${message}\n期望不等于: ${expected}\n实际: ${actual}`);
    }
  }

  static true(value, message = '') {
    if (value !== true) {
      throw new Error(`断言失败: ${message}\n期望: true\n实际: ${value}`);
    }
  }

  static false(value, message = '') {
    if (value !== false) {
      throw new Error(`断言失败: ${message}\n期望: false\n实际: ${value}`);
    }
  }

  static throws(fn, message = '') {
    try {
      fn();
      throw new Error(`断言失败: ${message}\n期望抛出异常，但没有抛出`);
    } catch (error) {
      // 预期的异常
    }
  }

  static async rejects(promise, message = '') {
    try {
      await promise;
      throw new Error(`断言失败: ${message}\n期望Promise被拒绝，但被解决了`);
    } catch (error) {
      // 预期的拒绝
    }
  }

  static deepEqual(actual, expected, message = '') {
    const actualStr = JSON.stringify(actual);
    const expectedStr = JSON.stringify(expected);
    
    if (actualStr !== expectedStr) {
      throw new Error(`断言失败: ${message}\n期望: ${expectedStr}\n实际: ${actualStr}`);
    }
  }

  static includes(array, item, message = '') {
    if (!Array.isArray(array) || !array.includes(item)) {
      throw new Error(`断言失败: ${message}\n数组不包含期望的项: ${item}`);
    }
  }
}

// 模拟库
class Mock {
  static fn(implementation) {
    const mockFn = function(...args) {
      if (!mockFn.calls) mockFn.calls = [];
      mockFn.calls.push(args);
      mockFn.callCount = (mockFn.callCount || 0) + 1;

      if (mockFn.mockImplementation) {
        return mockFn.mockImplementation(...args);
      }

      if (implementation) {
        return implementation(...args);
      }
    };

    mockFn.calls = [];
    mockFn.callCount = 0;
    mockFn.mockImplementation = null;

    mockFn.mockReturnValue = (value) => {
      mockFn.mockImplementation = () => value;
      return mockFn;
    };
    mockFn.mockResolvedValue = (value) => {
      mockFn.mockImplementation = () => Promise.resolve(value);
      return mockFn;
    };
    mockFn.mockRejectedValue = (error) => {
      mockFn.mockImplementation = () => Promise.reject(error);
      return mockFn;
    };

    // 重置mock
    mockFn.mockReset = () => {
      mockFn.calls = [];
      mockFn.callCount = 0;
      mockFn.mockImplementation = null;
    };

    return mockFn;
  }

  static object(obj = {}) {
    return new Proxy(obj, {
      get(target, prop) {
        if (!(prop in target)) {
          target[prop] = Mock.fn();
        }
        return target[prop];
      }
    });
  }
}

// 全局测试运行器实例
const testRunner = new TestRunner();

module.exports = {
  TestRunner,
  Assert,
  Mock,
  testRunner,
  
  // 便捷方法
  suite: (name, setupFn) => testRunner.suite(name, setupFn),
  run: () => testRunner.run(),
  configure: (options) => testRunner.configure(options)
};
