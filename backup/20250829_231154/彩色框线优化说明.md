# 卡片组件彩色框线优化说明

## 概述
参照首页今日鹅价组件的样式，为所有卡片组件添加了不同颜色的框线，并升级为真正的卡片式设计，提升视觉层次感和美观度。

## 今日鹅价组件参考样式
```css
.price-container {
  border: 2rpx solid #f0c040; /* 黄色框线 */
}
```

## 个人中心页面卡片式设计 (pages/profile/profile.wxss)

### 设计理念
- **真正的卡片式组件**: 不再依赖 `c-card` 组件，使用自定义的 `.card-module` 类
- **立体视觉效果**: 使用阴影、渐变和装饰条创建立体感
- **交互反馈**: 点击时的动画效果增强用户体验
- **颜色区分**: 不同功能模块使用不同颜色主题

### 1. 用户信息横幅卡片
- **背景**: 蓝绿渐变 `linear-gradient(135deg, #0066CC 0%, #00CC99 100%)`
- **顶部装饰条**: 白色渐变 `linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%)`
- **阴影**: `0 8rpx 32rpx rgba(0, 102, 204, 0.25)`
- **交互效果**: 悬停时上移2rpx并缩放0.98

### 2. 我的订单模块卡片
- **类名**: `.card-module.order-card`
- **边框**: 蓝色 `#0066cc`
- **背景**: 白色到淡蓝渐变 `linear-gradient(135deg, #ffffff 0%, #f8fbff 100%)`
- **顶部装饰条**: 蓝色渐变 `linear-gradient(90deg, #0066cc 0%, #1890ff 100%)`
- **阴影**: `0 8rpx 32rpx rgba(0, 102, 204, 0.15)`
- **CSS变量**: `--card-color: #0066cc`, `--card-color-light: #1890ff`

### 3. 办公管理模块卡片
- **类名**: `.card-module.oa-card`
- **边框**: 绿色 `#52c41a`
- **背景**: 白色到淡绿渐变 `linear-gradient(135deg, #ffffff 0%, #f6ffed 100%)`
- **顶部装饰条**: 绿色渐变 `linear-gradient(90deg, #52c41a 0%, #73d13d 100%)`
- **阴影**: `0 8rpx 32rpx rgba(82, 196, 26, 0.15)`
- **CSS变量**: `--card-color: #52c41a`, `--card-color-light: #73d13d`

### 4. 功能服务模块卡片
- **类名**: `.card-module.service-card`
- **边框**: 橙色 `#fa8c16`
- **背景**: 白色到淡橙渐变 `linear-gradient(135deg, #ffffff 0%, #fff7e6 100%)`
- **顶部装饰条**: 橙色渐变 `linear-gradient(90deg, #fa8c16 0%, #ffa940 100%)`
- **阴影**: `0 8rpx 32rpx rgba(250, 140, 22, 0.15)`
- **CSS变量**: `--card-color: #fa8c16`, `--card-color-light: #ffa940`

### 5. 退出登录按钮卡片
- **背景**: 红色渐变 `linear-gradient(135deg, #ff4757 0%, #ff3742 100%)`
- **顶部装饰条**: 白色渐变 `linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%)`
- **阴影**: `0 8rpx 32rpx rgba(255, 71, 87, 0.25)`

## 卡片式设计特点

### 基础卡片样式 (.card-module)
```css
.card-module {
  margin: 24rpx 16rpx;
  border-radius: 24rpx;
  background: #ffffff;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  padding: 32rpx;
}
```

### 视觉层次
- **立体阴影**: 使用 `0 8rpx 32rpx` 的阴影效果
- **顶部装饰条**: 每个卡片顶部都有6rpx高的渐变色装饰条
- **渐变背景**: 微妙的背景渐变增强立体感
- **边框强调**: 2rpx的彩色边框突出卡片边界

### 交互效果
- **悬停动画**: 点击时上移4rpx并缩放0.98
- **阴影变化**: 悬停时阴影加深，增强立体感
- **平滑过渡**: 所有动画使用0.3s的缓动效果

### CSS变量系统
使用CSS变量实现颜色主题的统一管理：
```css
.order-card {
  --card-color: #0066cc;
  --card-color-light: #1890ff;
}

.oa-card {
  --card-color: #52c41a;
  --card-color-light: #73d13d;
}

.service-card {
  --card-color: #fa8c16;
  --card-color-light: #ffa940;
}
```

### 颜色系统
- **蓝色系**: 用于订单、管理类功能
- **绿色系**: 用于办公、健康类功能
- **橙色系**: 用于服务、设置类功能
- **红色系**: 用于退出、警告类功能

## 其他页面卡片组件优化

### 首页 (pages/home/<USER>
- **公告栏**: 青色框线 `#13c2c2`
- **任务部分**: 紫色框线 `#722ed1`
- **知识库部分**: 粉色框线 `#eb2f96`

### 健康页面 (pages/health/health.wxss)
- **记录卡片**: 绿色框线 `#52c41a`

### 商城页面 (pages/shop/shop.wxss)
- **商品卡片**: 红色框线 `#ff4d4f`

### OA办公页面 (pages/oa/oa.wxss)
- **待审批统计卡片**: 蓝色框线 `#0066cc`
- **今日任务统计卡片**: 绿色框线 `#52c41a`
- **本月支出统计卡片**: 橙色框线 `#fa8c16`
- **未读通知统计卡片**: 紫色框线 `#722ed1`
- **功能模块项**: 青色框线 `#13c2c2`
- **活动项**: 粉色框线 `#eb2f96`

## 通用卡片组件 (components/common/card/card.wxss)
添加了多种彩色边框样式类：
- `.c-card--border-blue`: 蓝色 `#0066cc`
- `.c-card--border-green`: 绿色 `#52c41a`
- `.c-card--border-orange`: 橙色 `#fa8c16`
- `.c-card--border-purple`: 紫色 `#722ed1`
- `.c-card--border-red`: 红色 `#ff4d4f`
- `.c-card--border-cyan`: 青色 `#13c2c2`
- `.c-card--border-yellow`: 黄色 `#f0c040`
- `.c-card--border-pink`: 粉色 `#eb2f96`

## 技术实现
- **自定义卡片类**: 使用 `.card-module` 替代 `c-card` 组件
- **CSS变量**: 使用CSS变量管理颜色主题
- **伪元素装饰**: 使用 `::before` 伪元素创建顶部装饰条
- **渐变背景**: 使用 `linear-gradient` 创建渐变背景和装饰条
- **立体阴影**: 使用 `box-shadow` 创建立体阴影效果
- **交互动画**: 使用 `transform` 和 `transition` 创建交互动画
- **响应式设计**: 保持在不同屏幕尺寸下的适配性

## 效果
- 提升了页面的视觉层次感和立体感
- 增强了不同功能模块的区分度
- 提供了丰富的交互反馈
- 保持了整体设计的一致性和现代感
- 提升了用户体验和界面美观度
- 实现了真正的卡片式组件设计
