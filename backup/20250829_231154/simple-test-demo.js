/**
 * 简化测试演示
 * Simple Test Demo
 */

const { suite, Assert, Mock, run, configure } = require('./framework/test-runner.js');

// 配置测试运行器
configure({
  timeout: 5000,
  retries: 1,
  verbose: true
});

// 演示基础功能测试
suite('基础功能测试', (context) => {
  context.test('断言测试', () => {
    Assert.equal(1 + 1, 2, '1+1应该等于2');
    Assert.true(true, 'true应该是true');
    Assert.false(false, 'false应该是false');
    Assert.deepEqual({a: 1}, {a: 1}, '对象应该深度相等');
  });

  context.test('Mock函数测试', () => {
    const mockFn = Mock.fn();
    mockFn('test', 123);
    mockFn('hello');
    
    Assert.equal(mockFn.callCount, 2, '应该被调用2次');
    Assert.deepEqual(mockFn.calls[0], ['test', 123], '第一次调用参数正确');
    Assert.deepEqual(mockFn.calls[1], ['hello'], '第二次调用参数正确');
  });

  context.test('Mock返回值测试', () => {
    const mockFn = Mock.fn().mockReturnValue('mocked');
    const result = mockFn();
    
    Assert.equal(result, 'mocked', '应该返回mock的值');
    Assert.equal(mockFn.callCount, 1, '应该被调用1次');
  });

  context.test('异步测试', async () => {
    const mockFn = Mock.fn().mockResolvedValue('async result');
    const result = await mockFn();
    
    Assert.equal(result, 'async result', '应该返回异步结果');
  });
});

// 演示业务逻辑测试
suite('业务逻辑测试', (context) => {
  let calculator;

  context.beforeEach(() => {
    calculator = {
      add: (a, b) => a + b,
      multiply: (a, b) => a * b,
      divide: (a, b) => {
        if (b === 0) throw new Error('除数不能为0');
        return a / b;
      }
    };
  });

  context.test('加法运算', () => {
    Assert.equal(calculator.add(2, 3), 5, '2+3应该等于5');
    Assert.equal(calculator.add(-1, 1), 0, '-1+1应该等于0');
  });

  context.test('乘法运算', () => {
    Assert.equal(calculator.multiply(3, 4), 12, '3*4应该等于12');
    Assert.equal(calculator.multiply(0, 5), 0, '0*5应该等于0');
  });

  context.test('除法运算', () => {
    Assert.equal(calculator.divide(10, 2), 5, '10/2应该等于5');
    
    Assert.throws(() => {
      calculator.divide(10, 0);
    }, '除以0应该抛出错误');
  });
});

// 演示性能测试
suite('性能测试', (context) => {
  context.test('数组操作性能', () => {
    const startTime = Date.now();
    
    const arr = [];
    for (let i = 0; i < 10000; i++) {
      arr.push(i);
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    Assert.true(duration < 100, `数组操作应该在100ms内完成，实际耗时: ${duration}ms`);
    Assert.equal(arr.length, 10000, '数组长度应该是10000');
  });

  context.test('对象创建性能', () => {
    const startTime = Date.now();
    
    const objects = [];
    for (let i = 0; i < 1000; i++) {
      objects.push({
        id: i,
        name: `object-${i}`,
        data: { value: i * 2 }
      });
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    Assert.true(duration < 50, `对象创建应该在50ms内完成，实际耗时: ${duration}ms`);
    Assert.equal(objects.length, 1000, '对象数量应该是1000');
  });
});

// 演示错误处理测试
suite('错误处理测试', (context) => {
  context.test('同步错误处理', () => {
    const errorFunction = () => {
      throw new Error('测试错误');
    };
    
    Assert.throws(errorFunction, '应该抛出错误');
  });

  context.test('异步错误处理', async () => {
    const asyncErrorFunction = async () => {
      throw new Error('异步测试错误');
    };
    
    await Assert.rejects(asyncErrorFunction(), '应该拒绝Promise');
  });

  context.test('条件错误处理', () => {
    const conditionalError = (shouldError) => {
      if (shouldError) {
        throw new Error('条件错误');
      }
      return 'success';
    };
    
    Assert.equal(conditionalError(false), 'success', '不应该抛出错误');
    Assert.throws(() => conditionalError(true), '应该抛出条件错误');
  });
});

// 运行测试
if (require.main === module) {
  console.log('🚀 运行简化测试演示...\n');
  
  run().then(results => {
    console.log('\n📊 测试演示完成');
    console.log('=' .repeat(40));
    console.log(`总计: ${results.total}`);
    console.log(`✅ 通过: ${results.passed}`);
    console.log(`❌ 失败: ${results.failed}`);
    console.log(`📈 成功率: ${results.total > 0 ? (results.passed / results.total * 100).toFixed(2) : 0}%`);
    
    if (results.failed === 0) {
      console.log('\n🎉 所有测试通过！测试框架工作正常。');
      process.exit(0);
    } else {
      console.log('\n❌ 部分测试失败，请检查测试框架。');
      process.exit(1);
    }
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  // 导出测试套件供其他地方使用
  runDemo: () => run()
};
