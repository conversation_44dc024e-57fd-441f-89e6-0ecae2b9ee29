# SAAS多租户系统部署和配置指南

## 📋 部署概览

本指南详细说明了智慧养鹅云开发项目SAAS多租户架构的完整部署流程，包括环境配置、数据库初始化、权限配置、测试验证等关键步骤。

## 🛠️ 环境准备

### 1. 开发环境要求

**必需软件：**
- Node.js 16.0+ 
- 微信开发者工具 最新版
- Git 2.0+

**微信小程序云开发环境：**
- 已开通云开发服务
- 云开发环境ID（建议创建开发、测试、生产三个环境）
- 云开发资源配额充足

### 2. 项目结构检查

确保项目包含以下核心文件和目录：

```
智慧养鹅云开发/
├── utils/                          # 核心工具类
│   ├── role-permission.js          # 权限系统
│   ├── data-isolation.js           # 数据隔离
│   ├── api-standard.js             # API标准
│   ├── route-guard.js              # 路由守卫
│   └── data-scheduler.js           # 数据调度
├── cloudfunctions/                 # 云函数
│   ├── admin/                      # 平台级云函数
│   │   ├── goosePriceManagement/
│   │   ├── announcementManagement/
│   │   └── tenantManagement/
│   └── business/                   # 租户级云函数
│       ├── flockManagementV2/
│       └── materialManagement/
├── database/                       # 数据库配置
│   ├── collections/
│   └── security/
├── pages/                          # 前端页面
│   ├── admin/                      # 平台管理页面
│   └── business/                   # 租户业务页面
└── tests/                          # 测试文件
    ├── data-isolation-test.js
    └── integration-test.js
```

## 🗄️ 数据库初始化

### 1. 创建云数据库集合

在微信开发者工具的云开发控制台中创建以下集合：

**平台级集合：**
```bash
# 今日鹅价信息
goose_prices

# 平台公告
platform_announcements  

# 平台知识库
platform_knowledge_base

# 平台商城配置
platform_shop_config

# AI模型配置
ai_model_config

# 租户管理
tenants

# 平台操作日志
platform_operation_logs
```

**租户级集合：**
```bash
# 用户信息
users

# 鹅群管理
flocks

# 健康记录
health_records

# 生产记录
production_records

# 租户物料管理
tenant_materials

# 物料库存记录
material_stock_records

# 租户财务记录
tenant_finance_records

# 租户成本分析
tenant_cost_analysis

# 租户使用统计
tenant_usage_stats

# 订阅计费
subscription_billing

# API访问日志
api_access_logs
```

### 2. 配置数据库安全规则

将 `database/security/security-rules.json` 中的安全规则应用到云数据库：

```json
{
  "read": true,
  "write": false
}
```

**重要：** 在生产环境中，必须将上述临时规则替换为完整的安全规则。

### 3. 创建数据库索引

为提高查询性能，创建以下索引：

**租户级数据索引：**
```javascript
// flocks 集合
db.collection('flocks').createIndex({
  "tenant_id": 1,
  "user_id": 1,
  "created_at": -1
});

// tenant_materials 集合
db.collection('tenant_materials').createIndex({
  "tenant_id": 1,
  "material_type": 1,
  "current_stock": 1
});

// health_records 集合
db.collection('health_records').createIndex({
  "tenant_id": 1,
  "flock_id": 1,
  "record_date": -1
});
```

**平台级数据索引：**
```javascript
// goose_prices 集合
db.collection('goose_prices').createIndex({
  "region": 1,
  "breed": 1,
  "publish_date": -1
});

// platform_announcements 集合
db.collection('platform_announcements').createIndex({
  "target_tenants": 1,
  "publish_at": -1,
  "status": 1
});
```

## ☁️ 云函数部署

### 1. 部署云函数

使用微信开发者工具部署所有云函数：

**平台级云函数：**
```bash
# 部署今日鹅价管理
cloudfunctions/admin/goosePriceManagement

# 部署平台公告管理  
cloudfunctions/admin/announcementManagement

# 部署租户管理
cloudfunctions/admin/tenantManagement
```

**租户级云函数：**
```bash
# 部署鹅群管理V2
cloudfunctions/business/flockManagementV2

# 部署物料管理
cloudfunctions/business/materialManagement
```

### 2. 配置云函数环境变量

在云开发控制台为每个云函数配置环境变量：

```javascript
// 环境变量配置
{
  "API_VERSION": "v2",
  "LOG_LEVEL": "info",
  "CACHE_TTL": "300000",
  "MAX_RETRY_ATTEMPTS": "3"
}
```

### 3. 设置云函数触发器

为需要定时执行的云函数设置触发器：

```javascript
// 租户使用统计定时任务（每日凌晨执行）
{
  "triggers": [
    {
      "name": "dailyStats",
      "type": "timer",
      "config": "0 0 0 * * * *"
    }
  ]
}
```

## 🔐 权限系统配置

### 1. 初始化超级管理员

在 `users` 集合中手动创建超级管理员账户：

```javascript
{
  "_id": "super_admin_001",
  "openid": "your_openid_here",
  "tenant_id": null,
  "role": "super_admin",
  "nickname": "超级管理员",
  "email": "<EMAIL>",
  "status": "active",
  "permissions": ["ALL_PERMISSIONS"],
  "created_at": new Date(),
  "updated_at": new Date()
}
```

### 2. 创建测试租户

创建测试租户用于验证多租户隔离：

```javascript
// 租户A
{
  "_id": "tenant_test_a",
  "tenant_code": "TEST_A",
  "company_name": "测试租户A",
  "contact_name": "张三",
  "contact_phone": "13800138001",
  "contact_email": "<EMAIL>",
  "subscription_plan": "standard",
  "subscription_start": new Date(),
  "subscription_end": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
  "status": "active",
  "created_at": new Date()
}

// 租户B  
{
  "_id": "tenant_test_b",
  "tenant_code": "TEST_B", 
  "company_name": "测试租户B",
  "contact_name": "李四",
  "contact_phone": "13800138002",
  "contact_email": "<EMAIL>",
  "subscription_plan": "basic",
  "subscription_start": new Date(),
  "subscription_end": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
  "status": "active",
  "created_at": new Date()
}
```

### 3. 创建测试用户

为每个测试租户创建管理员和普通用户：

```javascript
// 租户A管理员
{
  "_id": "user_test_a_admin",
  "openid": "openid_test_a_admin",
  "tenant_id": "tenant_test_a",
  "role": "admin",
  "nickname": "租户A管理员",
  "status": "active",
  "permissions": ["FLOCK_VIEW_ALL", "FLOCK_EDIT", "MATERIAL_VIEW_ALL"],
  "created_at": new Date()
}

// 租户A普通用户
{
  "_id": "user_test_a_user",
  "openid": "openid_test_a_user", 
  "tenant_id": "tenant_test_a",
  "role": "user",
  "nickname": "租户A用户",
  "status": "active",
  "permissions": ["FLOCK_VIEW_OWN"],
  "created_at": new Date()
}
```

## 🧪 测试验证

### 1. 运行数据隔离测试

```bash
# 在项目根目录执行
node tests/data-isolation-test.js
```

**预期输出：**
```
🧪 开始多租户数据隔离测试...

📋 测试基础租户隔离...
  ✅ 租户A管理员查询鹅群
  ✅ 租户B管理员查询鹅群

👤 测试用户级隔离...
  ✅ 租户A普通用户查询鹅群

🚫 测试跨租户访问防护...
  ✅ 防止跨租户数据访问

📊 测试总结:
总测试数: 15
通过: 15
失败: 0
成功率: 100.00%

🎉 所有测试都通过了！多租户数据隔离机制工作正常。
```

### 2. 运行集成测试

```bash
# 运行完整集成测试
node tests/integration-test.js
```

### 3. 前端功能测试

在微信开发者工具中测试以下功能：

**平台管理功能：**
- [ ] 访问平台管理中心页面
- [ ] 今日鹅价发布和查看
- [ ] 平台公告管理
- [ ] 租户列表查看
- [ ] 系统配置管理

**租户业务功能：**
- [ ] 鹅群管理CRUD操作
- [ ] 物料管理和库存操作
- [ ] 健康记录管理
- [ ] 财务记录管理

**权限控制验证：**
- [ ] 不同角色用户的页面访问权限
- [ ] 跨租户数据访问防护
- [ ] API接口权限控制

## 🚀 生产环境部署

### 1. 环境配置

**生产环境变量：**
```javascript
{
  "NODE_ENV": "production",
  "API_VERSION": "v2", 
  "LOG_LEVEL": "warn",
  "CACHE_TTL": "600000",
  "MAX_RETRY_ATTEMPTS": "5",
  "ENABLE_DEBUG": "false"
}
```

### 2. 安全配置

**数据库安全规则：**
- 应用完整的 `database/security/security-rules.json` 规则
- 禁用临时的读写权限
- 启用严格的租户数据隔离

**云函数安全：**
- 启用云函数访问频率限制
- 配置IP白名单（如需要）
- 启用请求日志记录

### 3. 性能优化

**数据库优化：**
- 创建所有必要的索引
- 配置适当的连接池大小
- 启用查询缓存

**云函数优化：**
- 配置合适的内存和超时设置
- 启用并发限制
- 优化冷启动时间

### 4. 监控和日志

**监控配置：**
- 配置云函数监控告警
- 设置数据库性能监控
- 启用错误日志收集

**日志配置：**
- 配置结构化日志输出
- 设置日志轮转和清理
- 启用敏感信息脱敏

## 🔧 故障排除

### 常见问题解决

**1. 权限错误：**
```
错误：权限不足，无法访问该资源
解决：检查用户角色和权限配置，确认数据库安全规则正确
```

**2. 跨租户访问：**
```
错误：能看到其他租户的数据
解决：检查数据隔离中间件配置，确认tenant_id字段正确设置
```

**3. 云函数调用失败：**
```
错误：云函数执行超时或失败
解决：检查云函数日志，确认环境变量和依赖包正确配置
```

**4. 数据库连接问题：**
```
错误：数据库连接失败或查询超时
解决：检查云开发环境配置，确认数据库索引和查询优化
```

## 📞 技术支持

如遇到部署问题，请按以下步骤排查：

1. **检查环境配置**：确认所有环境变量和配置文件正确
2. **查看日志**：检查云函数和数据库操作日志
3. **运行测试**：执行完整的测试套件验证功能
4. **性能监控**：检查系统性能指标和资源使用情况

---

## ✅ 部署检查清单

- [ ] 云开发环境已创建并配置
- [ ] 所有数据库集合已创建
- [ ] 数据库安全规则已配置
- [ ] 数据库索引已创建
- [ ] 所有云函数已部署
- [ ] 云函数环境变量已配置
- [ ] 超级管理员账户已创建
- [ ] 测试租户和用户已创建
- [ ] 数据隔离测试通过
- [ ] 集成测试通过
- [ ] 前端功能测试通过
- [ ] 生产环境安全配置完成
- [ ] 监控和日志配置完成

**🎉 完成以上所有步骤后，SAAS多租户系统即可正式投入使用！**
