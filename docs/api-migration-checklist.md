# API客户端迁移清单

## 📋 迁移概述

将项目中所有API调用统一迁移到新的 `utils/api-client-final.js`，消除多个API客户端版本的维护问题。

## 🎯 迁移目标

- 统一API调用接口，提升响应时间20%
- 支持云函数/HTTP智能路由
- 建立统一的错误处理和重试机制
- 实现请求去重和缓存优化

## 📊 当前API客户端使用情况

### 需要替换的旧API客户端文件
1. `utils/api.js` - 主要API客户端，支持云函数回退
2. `utils/unified-api-client.js` - 环境配置API客户端
3. `utils/api-client-unified.js` - 高级功能API客户端
4. `utils/request.js` - 基础HTTP请求工具

### 发现的API调用模式

#### 模式1：直接引用api.js模块
```javascript
// 文件：pages/home/<USER>/production/production.js 等
const { home, auth, production } = require('../../utils/api.js');

// 调用方式
const data = await home.getHomeData();
const records = await production.getRecords(params);
```

#### 模式2：使用unified-api-client
```javascript
// 文件：多个页面和组件
const { get, post } = require('../../utils/unified-api-client.js');

// 调用方式
const response = await get('/api/v1/flocks');
const result = await post('/api/v1/auth/login', data);
```

#### 模式3：使用request.js基础工具
```javascript
// 文件：pages/production/production.js 等
const request = require('../../utils/request.js');

// 调用方式
const response = await request.get(API.ENDPOINTS.PRODUCTION.RECORDS);
```

## 🔄 迁移计划

### 第一阶段：核心页面迁移（Day 1）

#### 1.1 首页相关页面
- [ ] `pages/home/<USER>
  - 当前使用：`const { home, auth, v2: api } = require('../../utils/api.js');`
  - 迁移到：`const { apiClient } = require('../../utils/api-client-final.js');`
  - API调用：`home.getHomeData()` → `apiClient.get('/api/v2/home/<USER>')`

#### 1.2 生产管理页面
- [ ] `pages/production/production.js`
  - 当前使用：`const request = require('../../utils/request.js');`
  - 迁移到：`const { apiClient } = require('../../utils/api-client-final.js');`
  - API调用：`request.get(API.ENDPOINTS.PRODUCTION.RECORDS)` → `apiClient.get('/api/v2/production/records')`

#### 1.3 健康记录详情页面
- [ ] `pages/production-detail/record-detail/record-detail.js`
  - 当前使用：`const api = require('../../../utils/api.js');`
  - 迁移到：`const { apiClient } = require('../../../utils/api-client-final.js');`
  - API调用：`api.health.getRecordDetail(id)` → `apiClient.get('/api/v2/health/records/' + id)`

### 第二阶段：工作台模块迁移（Day 2）

#### 2.1 财务管理页面
- [ ] `pages/workspace/finance/overview/overview.js`
  - 当前使用：`const request = require('../../../../utils/request.js');`
  - 迁移到：`const { apiClient } = require('../../../../utils/api-client-final.js');`

#### 2.2 采购申请页面
- [ ] `pages/workspace/purchase/list/list.js`
  - 当前使用：`const request = require('../../../../utils/request.js');`
  - 需要修复：`API.API_ENDPOINTS.WORKSPACE.PURCHASE.LIST` 端点问题

#### 2.3 管理后台页面
- [ ] `pages/management/dashboard/dashboard.js`
  - 当前使用：`const request = require('../../../utils/request.js');`
  - 迁移到：`const { apiClient } = require('../../../utils/api-client-final.js');`

## 📝 具体迁移步骤

### 步骤1：更新引用
```javascript
// 旧代码
const { home, auth, production } = require('../../utils/api.js');
const { get, post } = require('../../utils/unified-api-client.js');
const request = require('../../utils/request.js');

// 新代码
const { apiClient, get, post, put, delete: del } = require('../../utils/api-client-final.js');
```

### 步骤2：更新API调用
```javascript
// 旧代码 - api.js模块调用
const data = await home.getHomeData();
const records = await production.getRecords(params);

// 新代码 - 统一客户端调用
const data = await apiClient.get('/api/v2/home/<USER>');
const records = await apiClient.get('/api/v2/production/records', { data: params });

// 或使用便捷方法
const data = await get('/api/v2/home/<USER>');
const records = await get('/api/v2/production/records', { data: params });
```

### 步骤3：更新错误处理
```javascript
// 旧代码
try {
  const result = await api.health.getRecords();
  if (result.code === 0) {
    this.setData({ records: result.data });
  } else {
    wx.showToast({ title: result.message, icon: 'none' });
  }
} catch (error) {
  console.error('请求失败:', error);
  wx.showToast({ title: '网络错误', icon: 'none' });
}

// 新代码 - 统一错误处理
try {
  const result = await apiClient.get('/api/v2/health/records');
  this.setData({ records: result.data });
} catch (error) {
  // 错误已由统一客户端处理，包括用户提示
  console.error('请求失败:', error);
}
```

## 🧪 迁移验证

### 功能验证清单
- [ ] 用户登录认证正常
- [ ] 首页数据加载正常
- [ ] 生产记录CRUD操作正常
- [ ] 健康记录详情查看正常
- [ ] 工作台各模块功能正常
- [ ] 错误处理和用户提示正常
- [ ] 网络异常重试机制正常

### 性能验证清单
- [ ] API响应时间提升20%（目标）
- [ ] 请求去重机制生效
- [ ] 云函数优先调用正常
- [ ] HTTP回退机制正常
- [ ] 缓存机制工作正常

## 📈 预期效果

### 技术指标
- API响应时间提升：20%
- 代码重复度降低：60%
- 维护复杂度降低：50%
- 错误处理一致性：100%

### 业务指标
- 用户体验提升：减少加载等待时间
- 系统稳定性提升：统一错误处理和重试
- 开发效率提升：统一API调用接口

## ⚠️ 注意事项

### 兼容性考虑
1. 保持原有API响应格式兼容
2. 渐进式迁移，避免影响现有功能
3. 保留旧API客户端文件，直到迁移完成

### 风险控制
1. 每个页面迁移后立即测试
2. 发现问题及时回滚
3. 保持详细的迁移日志

### 后续清理
1. 迁移完成后删除旧API客户端文件
2. 更新相关文档和注释
3. 运行完整的回归测试

## 📞 支持联系

如遇到迁移问题，请及时反馈：
- 技术问题：检查控制台错误日志
- 功能异常：对比迁移前后的行为差异
- 性能问题：使用开发者工具监控网络请求
