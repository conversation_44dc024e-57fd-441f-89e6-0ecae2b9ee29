# API架构优化方案

## 🎯 优化目标

基于微信小程序云开发最佳实践，优化智慧养鹅SaaS平台的API架构，提升接口规范性、响应性能和错误处理能力。

## 📊 当前API架构评估

### ✅ 架构优势

#### 1. 统一API客户端
```javascript
// utils/unified-api-client.js - 已实现
class UnifiedAPIClient {
  constructor() {
    this.config = environmentConfig.getRequestConfig();
    this.baseUrl = environmentConfig.getApiBaseUrl();
    this.pendingRequests = new Map();
    this.requestQueue = [];
  }
  
  // 智能请求去重
  async request(url, options = {}) {
    const requestId = this.generateRequestId(url, options);
    if (this.pendingRequests.has(requestId)) {
      return this.pendingRequests.get(requestId);
    }
    // ...
  }
}
```

#### 2. 多版本API支持
```javascript
// constants/api-unified.constants.js
const API_ENDPOINTS = {
  AUTH: {
    LOGIN: buildApiUrl('/auth/login'),
    LOGOUT: buildApiUrl('/auth/logout'),
    PROFILE: buildApiUrl('/auth/profile')
  },
  BUSINESS: {
    FLOCKS: buildApiUrl('/business/flocks'),
    HEALTH: buildApiUrl('/business/health')
  }
};
```

#### 3. 云函数适配器
```javascript
// utils/cloud-api-adapter.js
class CloudAPIAdapter {
  async request(method, url, data = {}, options = {}) {
    // 映射到云函数
    const { functionName, action } = this.mapToCloudFunction(method, url, data);
    
    // 调用云函数
    const result = await this.callCloudFunction(functionName, data);
    
    // 格式化响应
    return this.formatResponse(result);
  }
}
```

### ⚠️ 需要优化的问题

#### 1. API响应时间不一致
- 部分接口响应时间 > 2秒
- 缺少统一的超时处理
- 重试机制不完善

#### 2. 错误处理不统一
- 错误码不规范
- 错误信息不友好
- 缺少错误分类

#### 3. 数据调度机制待优化
- 缺少请求优先级
- 并发控制不足
- 缓存策略不统一

## 🚀 API架构优化方案

### 1. 统一API接口规范

#### 1.1 标准化响应格式
```javascript
// utils/api-response-standard.js
class APIResponseStandard {
  // 成功响应
  static success(data, message = 'success', meta = {}) {
    return {
      success: true,
      code: 200,
      message,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId(),
        ...meta
      }
    };
  }
  
  // 错误响应
  static error(code, message, details = null) {
    return {
      success: false,
      code,
      message,
      error: {
        details,
        type: this.getErrorType(code),
        severity: this.getErrorSeverity(code)
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    };
  }
  
  // 分页响应
  static paginated(data, pagination) {
    return this.success(data, 'success', {
      pagination: {
        page: pagination.page,
        pageSize: pagination.pageSize,
        total: pagination.total,
        totalPages: Math.ceil(pagination.total / pagination.pageSize),
        hasNext: pagination.page * pagination.pageSize < pagination.total,
        hasPrev: pagination.page > 1
      }
    });
  }
}
```

#### 1.2 统一错误码规范
```javascript
// constants/error-codes.js
const ERROR_CODES = {
  // 系统错误 (1000-1999)
  SYSTEM_ERROR: 1000,
  NETWORK_ERROR: 1001,
  TIMEOUT_ERROR: 1002,
  
  // 认证错误 (2000-2999)
  AUTH_REQUIRED: 2000,
  AUTH_EXPIRED: 2001,
  AUTH_INVALID: 2002,
  PERMISSION_DENIED: 2003,
  
  // 业务错误 (3000-3999)
  VALIDATION_ERROR: 3000,
  RESOURCE_NOT_FOUND: 3001,
  RESOURCE_CONFLICT: 3002,
  BUSINESS_RULE_VIOLATION: 3003,
  
  // 限流错误 (4000-4999)
  RATE_LIMIT_EXCEEDED: 4000,
  QUOTA_EXCEEDED: 4001
};

const ERROR_MESSAGES = {
  [ERROR_CODES.SYSTEM_ERROR]: '系统异常，请稍后重试',
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_CODES.AUTH_EXPIRED]: '登录已过期，请重新登录',
  [ERROR_CODES.PERMISSION_DENIED]: '权限不足，无法执行此操作',
  [ERROR_CODES.VALIDATION_ERROR]: '输入数据有误，请检查后重试'
};
```

### 2. 优化数据调度机制

#### 2.1 请求优先级管理
```javascript
// utils/request-priority-manager.js
class RequestPriorityManager {
  constructor() {
    this.queues = {
      critical: [], // 关键请求（登录、支付等）
      high: [],     // 高优先级（用户操作）
      normal: [],   // 普通请求（数据获取）
      low: []       // 低优先级（统计、日志等）
    };
    this.processing = false;
    this.maxConcurrent = 3;
    this.currentRequests = 0;
  }
  
  async addRequest(request, priority = 'normal') {
    return new Promise((resolve, reject) => {
      const requestWrapper = {
        request,
        resolve,
        reject,
        timestamp: Date.now(),
        timeout: this.getTimeoutByPriority(priority)
      };
      
      this.queues[priority].push(requestWrapper);
      this.processQueue();
    });
  }
  
  async processQueue() {
    if (this.processing || this.currentRequests >= this.maxConcurrent) {
      return;
    }
    
    this.processing = true;
    
    // 按优先级处理请求
    const priorities = ['critical', 'high', 'normal', 'low'];
    
    for (const priority of priorities) {
      while (this.queues[priority].length > 0 && 
             this.currentRequests < this.maxConcurrent) {
        const requestWrapper = this.queues[priority].shift();
        this.executeRequest(requestWrapper);
      }
    }
    
    this.processing = false;
  }
  
  async executeRequest(requestWrapper) {
    this.currentRequests++;
    
    try {
      const result = await Promise.race([
        requestWrapper.request(),
        this.createTimeoutPromise(requestWrapper.timeout)
      ]);
      
      requestWrapper.resolve(result);
    } catch (error) {
      requestWrapper.reject(error);
    } finally {
      this.currentRequests--;
      this.processQueue();
    }
  }
}
```

#### 2.2 智能并发控制
```javascript
// utils/concurrent-controller.js
class ConcurrentController {
  constructor() {
    this.maxConcurrent = this.getOptimalConcurrency();
    this.activeRequests = new Set();
    this.waitingQueue = [];
  }
  
  getOptimalConcurrency() {
    const systemInfo = wx.getSystemInfoSync();
    const networkType = systemInfo.networkType;
    
    // 根据网络类型调整并发数
    const concurrencyMap = {
      'wifi': 6,
      '4g': 4,
      '3g': 2,
      '2g': 1,
      'none': 0
    };
    
    return concurrencyMap[networkType] || 3;
  }
  
  async execute(requestFn) {
    if (this.activeRequests.size >= this.maxConcurrent) {
      await this.waitForSlot();
    }
    
    const requestId = this.generateRequestId();
    this.activeRequests.add(requestId);
    
    try {
      const result = await requestFn();
      return result;
    } finally {
      this.activeRequests.delete(requestId);
      this.processWaitingQueue();
    }
  }
  
  async waitForSlot() {
    return new Promise(resolve => {
      this.waitingQueue.push(resolve);
    });
  }
  
  processWaitingQueue() {
    if (this.waitingQueue.length > 0 && 
        this.activeRequests.size < this.maxConcurrent) {
      const resolve = this.waitingQueue.shift();
      resolve();
    }
  }
}
```

### 3. API响应时间优化

#### 3.1 智能重试机制
```javascript
// utils/smart-retry.js
class SmartRetry {
  constructor() {
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2,
      jitter: true
    };
  }
  
  async executeWithRetry(requestFn, options = {}) {
    const config = { ...this.retryConfig, ...options };
    let lastError;
    
    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        const result = await requestFn();
        return result;
      } catch (error) {
        lastError = error;
        
        // 判断是否应该重试
        if (!this.shouldRetry(error, attempt, config.maxRetries)) {
          throw error;
        }
        
        // 计算延迟时间
        const delay = this.calculateDelay(attempt, config);
        await this.sleep(delay);
      }
    }
    
    throw lastError;
  }
  
  shouldRetry(error, attempt, maxRetries) {
    if (attempt >= maxRetries) return false;
    
    // 网络错误可以重试
    if (error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT_ERROR') {
      return true;
    }
    
    // 5xx服务器错误可以重试
    if (error.statusCode >= 500 && error.statusCode < 600) {
      return true;
    }
    
    // 429限流错误可以重试
    if (error.statusCode === 429) {
      return true;
    }
    
    return false;
  }
  
  calculateDelay(attempt, config) {
    let delay = config.baseDelay * Math.pow(config.backoffFactor, attempt);
    delay = Math.min(delay, config.maxDelay);
    
    // 添加随机抖动
    if (config.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }
    
    return delay;
  }
}
```

#### 3.2 请求缓存优化
```javascript
// utils/api-cache-manager.js
class APICacheManager {
  constructor() {
    this.cache = new Map();
    this.cacheConfig = {
      defaultTTL: 5 * 60 * 1000, // 5分钟
      maxSize: 100,
      strategies: {
        'user-info': { ttl: 30 * 60 * 1000, priority: 'high' },
        'static-data': { ttl: 24 * 60 * 60 * 1000, priority: 'low' },
        'real-time': { ttl: 30 * 1000, priority: 'medium' }
      }
    };
  }
  
  async get(key, fetcher, strategy = 'default') {
    const cacheKey = this.buildCacheKey(key, strategy);
    const cached = this.cache.get(cacheKey);
    
    // 检查缓存是否有效
    if (cached && !this.isExpired(cached)) {
      return cached.data;
    }
    
    // 获取新数据
    const data = await fetcher();
    
    // 缓存数据
    this.set(cacheKey, data, strategy);
    
    return data;
  }
  
  set(key, data, strategy = 'default') {
    const config = this.cacheConfig.strategies[strategy] || 
                   { ttl: this.cacheConfig.defaultTTL, priority: 'medium' };
    
    const cacheItem = {
      data,
      timestamp: Date.now(),
      ttl: config.ttl,
      priority: config.priority,
      accessCount: 0
    };
    
    // 检查缓存大小限制
    if (this.cache.size >= this.cacheConfig.maxSize) {
      this.evictLeastUsed();
    }
    
    this.cache.set(key, cacheItem);
  }
  
  evictLeastUsed() {
    let leastUsedKey = null;
    let minAccessCount = Infinity;
    let oldestTimestamp = Infinity;
    
    for (const [key, item] of this.cache.entries()) {
      if (item.priority === 'low' && 
          (item.accessCount < minAccessCount || 
           (item.accessCount === minAccessCount && item.timestamp < oldestTimestamp))) {
        leastUsedKey = key;
        minAccessCount = item.accessCount;
        oldestTimestamp = item.timestamp;
      }
    }
    
    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
    }
  }
}
```

## 📊 优化目标

### 性能指标
- **API响应时间**：平均 < 500ms，95% < 1s
- **错误率**：< 0.1%
- **重试成功率**：> 90%
- **缓存命中率**：> 80%

### 可用性指标
- **接口可用性**：> 99.9%
- **并发处理能力**：支持1000+并发请求
- **错误恢复时间**：< 30秒

## 🔧 实施计划

### 第1周：接口规范统一
- [ ] 标准化响应格式
- [ ] 统一错误码体系
- [ ] 完善API文档

### 第2周：数据调度优化
- [ ] 实现请求优先级管理
- [ ] 优化并发控制
- [ ] 添加智能重试

### 第3周：缓存策略优化
- [ ] 实现多层缓存
- [ ] 优化缓存策略
- [ ] 添加缓存监控

### 第4周：监控和测试
- [ ] 添加性能监控
- [ ] 压力测试
- [ ] 优化调整

## ⚠️ 注意事项

1. **向后兼容**：确保API变更不影响现有功能
2. **渐进式部署**：分阶段上线，降低风险
3. **监控告警**：实时监控API性能和错误率
4. **文档更新**：及时更新API文档和使用指南
