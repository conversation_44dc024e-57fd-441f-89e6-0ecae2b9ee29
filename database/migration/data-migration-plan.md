# 数据迁移方案

## 📋 迁移概述

本文档描述了从传统MySQL数据库迁移到微信小程序云数据库的详细方案。

### 迁移目标
- 将现有MySQL数据完整迁移到云数据库
- 保持数据完整性和一致性
- 最小化业务中断时间
- 确保多租户数据隔离

## 🗄️ 数据库映射关系

### 用户相关表映射

#### users表 → users集合
```javascript
// MySQL表结构
users: {
  id: INT PRIMARY KEY,
  openid: VARCHAR(100) UNIQUE,
  unionid: VARCHAR(100),
  nickname: VARCHAR(100),
  avatar: TEXT,
  phone: VARCHAR(20),
  email: VARCHAR(100),
  tenant_id: INT,
  role: ENUM('user', 'admin', 'super_admin'),
  status: ENUM('active', 'inactive', 'suspended'),
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
}

// 云数据库集合结构
users: {
  _id: "自动生成",
  openid: "微信openid",
  unionid: "微信unionid",
  nickname: "用户昵称",
  avatar: "头像URL",
  phone: "手机号",
  email: "邮箱",
  tenant_id: "租户ID字符串",
  role: "用户角色",
  status: "用户状态",
  permissions: ["权限列表"], // 新增字段
  cart: [], // 新增购物车字段
  created_at: Date,
  updated_at: Date,
  last_login_at: Date // 新增字段
}
```

#### tenants表 → tenants集合
```javascript
// MySQL → 云数据库映射
tenants: {
  id → _id (转换为字符串)
  tenant_code → tenant_code
  company_name → company_name
  contact_* → contact_*
  subscription_* → subscription_*
  // 新增字段
  features: ["功能列表"],
  settings: {
    max_users: 100,
    max_flocks: 50,
    storage_limit: **********
  }
}
```

### 业务数据表映射

#### flocks表 → flocks集合
```javascript
// 字段映射
flocks: {
  id → _id
  tenant_id → tenant_id (转换为字符串)
  user_id → user_id (转换为字符串)
  name → name
  breed → breed
  count → count
  age → age
  health_status → health_status
  location → location
  // 新增字段
  description: "描述",
  images: ["图片URL列表"]
}
```

#### health_records表 → health_records集合
```javascript
// 字段映射和扩展
health_records: {
  id → _id
  tenant_id → tenant_id (字符串)
  user_id → user_id (字符串)
  flock_id → flock_id (字符串)
  record_type → record_type
  symptoms → symptoms
  diagnosis → diagnosis
  treatment → treatment
  cost → cost
  notes → notes
  // 新增AI诊断字段
  ai_diagnosis: {
    result: "AI诊断结果",
    confidence: 0.85,
    suggestions: ["建议列表"]
  },
  images: ["图片URL列表"],
  veterinarian: "兽医信息"
}
```

## 🔄 迁移步骤

### 第一阶段：数据导出
1. **备份MySQL数据库**
   ```sql
   mysqldump -u username -p database_name > backup.sql
   ```

2. **导出核心数据表**
   ```sql
   -- 导出用户数据
   SELECT * FROM users INTO OUTFILE '/tmp/users.csv' 
   FIELDS TERMINATED BY ',' ENCLOSED BY '"' LINES TERMINATED BY '\n';
   
   -- 导出租户数据
   SELECT * FROM tenants INTO OUTFILE '/tmp/tenants.csv'
   FIELDS TERMINATED BY ',' ENCLOSED BY '"' LINES TERMINATED BY '\n';
   
   -- 导出业务数据
   SELECT * FROM flocks INTO OUTFILE '/tmp/flocks.csv'
   FIELDS TERMINATED BY ',' ENCLOSED BY '"' LINES TERMINATED BY '\n';
   ```

### 第二阶段：数据转换
1. **创建数据转换脚本**
   ```javascript
   // data-converter.js
   const fs = require('fs');
   const csv = require('csv-parser');
   
   function convertUsers(csvFile) {
     const users = [];
     return new Promise((resolve, reject) => {
       fs.createReadStream(csvFile)
         .pipe(csv())
         .on('data', (row) => {
           users.push({
             openid: row.openid,
             unionid: row.unionid || null,
             nickname: row.nickname || '微信用户',
             avatar: row.avatar || '',
             phone: row.phone || '',
             email: row.email || '',
             tenant_id: row.tenant_id.toString(),
             role: row.role || 'user',
             status: row.status || 'active',
             permissions: ['basic'], // 默认权限
             cart: [], // 空购物车
             created_at: new Date(row.created_at),
             updated_at: new Date(row.updated_at),
             last_login_at: new Date(row.updated_at)
           });
         })
         .on('end', () => resolve(users))
         .on('error', reject);
     });
   }
   ```

### 第三阶段：数据导入
1. **创建云函数进行批量导入**
   ```javascript
   // 批量导入云函数
   exports.main = async (event, context) => {
     const { collection, data, batchSize = 100 } = event;
     const db = cloud.database();
     
     const results = [];
     for (let i = 0; i < data.length; i += batchSize) {
       const batch = data.slice(i, i + batchSize);
       try {
         const result = await db.collection(collection).add({
           data: batch
         });
         results.push(result);
       } catch (error) {
         console.error(`批次 ${i}-${i + batchSize} 导入失败:`, error);
       }
     }
     
     return { success: true, results };
   };
   ```

## 📊 数据验证

### 数据完整性检查
```javascript
// 验证脚本
async function validateMigration() {
  const db = cloud.database();
  
  // 检查记录数量
  const userCount = await db.collection('users').count();
  const tenantCount = await db.collection('tenants').count();
  const flockCount = await db.collection('flocks').count();
  
  console.log('迁移后数据统计:');
  console.log(`用户数量: ${userCount.total}`);
  console.log(`租户数量: ${tenantCount.total}`);
  console.log(`鹅群数量: ${flockCount.total}`);
  
  // 检查数据关联性
  const orphanFlocks = await db.collection('flocks')
    .aggregate()
    .lookup({
      from: 'users',
      localField: 'user_id',
      foreignField: '_id',
      as: 'user'
    })
    .match({
      user: { $size: 0 }
    })
    .end();
    
  if (orphanFlocks.list.length > 0) {
    console.warn(`发现 ${orphanFlocks.list.length} 个孤立的鹅群记录`);
  }
}
```

## ⚠️ 迁移注意事项

### 数据类型转换
1. **ID字段转换**
   - MySQL的INT类型ID → 云数据库的字符串_id
   - 需要更新所有外键引用

2. **日期时间转换**
   - MySQL的TIMESTAMP → JavaScript的Date对象
   - 注意时区处理

3. **枚举值处理**
   - MySQL的ENUM → 字符串常量
   - 确保值的一致性

### 性能优化
1. **批量操作**
   - 使用批量插入减少网络请求
   - 每批次建议100-500条记录

2. **并发控制**
   - 避免过多并发请求
   - 实现重试机制

3. **内存管理**
   - 大数据集分批处理
   - 及时释放内存

### 错误处理
1. **重复数据处理**
   ```javascript
   try {
     await db.collection('users').add({ data: userData });
   } catch (error) {
     if (error.errCode === -502002) {
       // 记录已存在，跳过或更新
       console.log('用户已存在，跳过插入');
     } else {
       throw error;
     }
   }
   ```

2. **数据验证**
   ```javascript
   function validateUserData(user) {
     if (!user.openid) {
       throw new Error('openid不能为空');
     }
     if (!user.tenant_id) {
       throw new Error('tenant_id不能为空');
     }
     return true;
   }
   ```

## 🔄 回滚方案

### 回滚准备
1. **保留原数据库**
   - 迁移期间保持MySQL数据库运行
   - 设置只读模式防止数据变更

2. **创建回滚脚本**
   ```javascript
   // 清空云数据库
   async function rollback() {
     const collections = ['users', 'tenants', 'flocks', 'health_records'];
     
     for (const collection of collections) {
       const result = await db.collection(collection).get();
       for (const doc of result.data) {
         await db.collection(collection).doc(doc._id).remove();
       }
     }
   }
   ```

### 切换策略
1. **灰度发布**
   - 部分用户先使用云数据库
   - 逐步扩大范围

2. **功能开关**
   - 通过配置控制数据源
   - 快速切换回传统数据库

## 📈 迁移时间表

### 准备阶段（1-2天）
- [ ] 数据库备份
- [ ] 迁移脚本开发
- [ ] 测试环境验证

### 执行阶段（1天）
- [ ] 数据导出（2小时）
- [ ] 数据转换（1小时）
- [ ] 数据导入（4小时）
- [ ] 数据验证（1小时）

### 验证阶段（1-2天）
- [ ] 功能测试
- [ ] 性能测试
- [ ] 用户验收测试

---

*此迁移方案确保数据的完整性和系统的稳定性，为云开发架构提供可靠的数据基础。*
