/**
 * 云数据库集合初始化脚本
 * 用于创建必要的数据库集合和索引
 */

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 集合定义
const collections = [
  {
    name: 'users',
    description: '用户信息集合',
    indexes: [
      { keys: { openid: 1 }, options: { unique: true } },
      { keys: { tenant_id: 1, role: 1 } },
      { keys: { status: 1 } },
      { keys: { created_at: -1 } }
    ]
  },
  {
    name: 'tenants',
    description: '租户信息集合',
    indexes: [
      { keys: { tenant_code: 1 }, options: { unique: true } },
      { keys: { status: 1 } },
      { keys: { subscription_plan: 1 } },
      { keys: { created_at: -1 } }
    ]
  },
  {
    name: 'flocks',
    description: '鹅群信息集合',
    indexes: [
      { keys: { tenant_id: 1, user_id: 1 } },
      { keys: { tenant_id: 1, health_status: 1 } },
      { keys: { breed: 1 } },
      { keys: { created_at: -1 } }
    ]
  },
  {
    name: 'health_records',
    description: '健康记录集合',
    indexes: [
      { keys: { tenant_id: 1, flock_id: 1 } },
      { keys: { tenant_id: 1, record_type: 1 } },
      { keys: { tenant_id: 1, created_at: -1 } },
      { keys: { user_id: 1, created_at: -1 } }
    ]
  },
  {
    name: 'production_records',
    description: '生产记录集合',
    indexes: [
      { keys: { tenant_id: 1, flock_id: 1 } },
      { keys: { tenant_id: 1, record_date: -1 } },
      { keys: { user_id: 1, record_date: -1 } }
    ]
  },
  {
    name: 'products',
    description: '商品信息集合',
    indexes: [
      { keys: { tenant_id: 1, status: 1 } },
      { keys: { tenant_id: 1, category: 1 } },
      { keys: { name: 'text' } }, // 文本搜索索引
      { keys: { created_at: -1 } }
    ]
  },
  {
    name: 'orders',
    description: '订单信息集合',
    indexes: [
      { keys: { tenant_id: 1, user_id: 1 } },
      { keys: { tenant_id: 1, status: 1 } },
      { keys: { order_no: 1 }, options: { unique: true } },
      { keys: { created_at: -1 } }
    ]
  },
  {
    name: 'oa_applications',
    description: 'OA申请集合',
    indexes: [
      { keys: { tenant_id: 1, user_id: 1 } },
      { keys: { tenant_id: 1, status: 1 } },
      { keys: { tenant_id: 1, type: 1 } },
      { keys: { current_approver_id: 1, status: 1 } },
      { keys: { created_at: -1 } }
    ]
  },
  {
    name: 'system_config',
    description: '系统配置集合',
    indexes: [
      { keys: { tenant_id: 1, category: 1, key: 1 }, options: { unique: true } },
      { keys: { category: 1 } },
      { keys: { is_public: 1 } }
    ]
  },
  {
    name: 'announcements',
    description: '公告信息集合',
    indexes: [
      { keys: { tenant_id: 1, status: 1 } },
      { keys: { tenant_id: 1, type: 1 } },
      { keys: { publish_at: -1 } },
      { keys: { priority: 1, publish_at: -1 } }
    ]
  },
  {
    name: 'knowledge_base',
    description: '知识库集合',
    indexes: [
      { keys: { tenant_id: 1, category: 1 } },
      { keys: { tenant_id: 1, status: 1 } },
      { keys: { title: 'text', content: 'text' } }, // 文本搜索索引
      { keys: { tags: 1 } },
      { keys: { created_at: -1 } }
    ]
  }
]

// 初始化集合和索引
async function initializeCollections() {
  console.log('开始初始化云数据库集合...')
  
  for (const collection of collections) {
    try {
      console.log(`正在处理集合: ${collection.name}`)
      
      // 创建集合（如果不存在）
      // 注意：云数据库会在第一次写入数据时自动创建集合
      
      // 创建索引
      if (collection.indexes && collection.indexes.length > 0) {
        for (const index of collection.indexes) {
          try {
            // 云数据库的索引创建需要通过控制台或API
            console.log(`  - 索引配置: ${JSON.stringify(index.keys)}`)
          } catch (indexError) {
            console.warn(`  - 创建索引失败: ${indexError.message}`)
          }
        }
      }
      
      console.log(`✓ 集合 ${collection.name} 处理完成`)
      
    } catch (error) {
      console.error(`✗ 处理集合 ${collection.name} 失败:`, error)
    }
  }
  
  console.log('云数据库集合初始化完成')
}

// 插入初始数据
async function insertInitialData() {
  console.log('开始插入初始数据...')
  
  try {
    // 插入默认系统配置
    const defaultConfigs = [
      {
        tenant_id: null, // 全局配置
        category: 'ai',
        key: 'diagnosis_enabled',
        value: true,
        description: 'AI诊断功能开关',
        type: 'boolean',
        is_public: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        tenant_id: null,
        category: 'payment',
        key: 'wechat_pay_enabled',
        value: true,
        description: '微信支付开关',
        type: 'boolean',
        is_public: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        tenant_id: null,
        category: 'general',
        key: 'max_upload_size',
        value: 10485760, // 10MB
        description: '最大上传文件大小（字节）',
        type: 'number',
        is_public: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]
    
    for (const config of defaultConfigs) {
      try {
        await db.collection('system_config').add({
          data: config
        })
        console.log(`✓ 插入系统配置: ${config.key}`)
      } catch (error) {
        if (error.errCode === -502002) {
          console.log(`- 系统配置已存在: ${config.key}`)
        } else {
          console.error(`✗ 插入系统配置失败: ${config.key}`, error)
        }
      }
    }
    
    // 插入默认知识库内容
    const defaultKnowledge = [
      {
        tenant_id: null, // 全局知识
        category: '养殖技术',
        title: '鹅群健康管理基础知识',
        content: `
# 鹅群健康管理基础知识

## 日常观察要点
1. 精神状态：活泼好动，反应敏捷
2. 食欲情况：采食正常，饮水充足
3. 粪便状态：成形，颜色正常
4. 呼吸状况：平稳，无异常声音

## 常见疾病预防
1. 定期疫苗接种
2. 保持环境卫生
3. 合理营养搭配
4. 及时隔离病鹅

## 紧急情况处理
1. 发现异常立即隔离
2. 记录症状详细信息
3. 及时联系兽医
4. 做好消毒工作
        `,
        tags: ['健康管理', '疾病预防', '基础知识'],
        author_id: 'system',
        status: 'published',
        view_count: 0,
        like_count: 0,
        attachments: [],
        created_at: new Date(),
        updated_at: new Date()
      }
    ]
    
    for (const knowledge of defaultKnowledge) {
      try {
        await db.collection('knowledge_base').add({
          data: knowledge
        })
        console.log(`✓ 插入知识库内容: ${knowledge.title}`)
      } catch (error) {
        console.error(`✗ 插入知识库内容失败: ${knowledge.title}`, error)
      }
    }
    
    console.log('初始数据插入完成')
    
  } catch (error) {
    console.error('插入初始数据失败:', error)
  }
}

// 验证数据库结构
async function validateDatabase() {
  console.log('开始验证数据库结构...')
  
  for (const collection of collections) {
    try {
      const result = await db.collection(collection.name).limit(1).get()
      console.log(`✓ 集合 ${collection.name} 可访问`)
    } catch (error) {
      console.error(`✗ 集合 ${collection.name} 访问失败:`, error)
    }
  }
  
  console.log('数据库结构验证完成')
}

// 主函数
async function main() {
  try {
    await initializeCollections()
    await insertInitialData()
    await validateDatabase()
    
    console.log('🎉 云数据库初始化完成！')
    
  } catch (error) {
    console.error('❌ 云数据库初始化失败:', error)
  }
}

// 导出函数供云函数调用
module.exports = {
  initializeCollections,
  insertInitialData,
  validateDatabase,
  main,
  collections
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}
