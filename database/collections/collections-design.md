# 云数据库集合设计文档

## 📊 数据库集合结构

### 1. 用户相关集合

#### users - 用户信息集合
```javascript
{
  _id: "user_id",
  openid: "微信openid",
  unionid: "微信unionid",
  nickname: "用户昵称",
  avatar: "头像URL",
  phone: "手机号",
  email: "邮箱",
  tenant_id: "租户ID",
  role: "用户角色", // user, admin, super_admin
  status: "用户状态", // active, inactive, suspended
  permissions: ["权限列表"],
  created_at: "创建时间",
  updated_at: "更新时间",
  last_login_at: "最后登录时间"
}
```

#### tenants - 租户信息集合
```javascript
{
  _id: "tenant_id",
  tenant_code: "租户代码",
  company_name: "公司名称",
  contact_name: "联系人",
  contact_phone: "联系电话",
  contact_email: "联系邮箱",
  subscription_plan: "订阅计划", // trial, basic, standard, premium, enterprise
  subscription_start: "订阅开始时间",
  subscription_end: "订阅结束时间",
  features: ["功能列表"],
  status: "租户状态", // active, inactive, suspended
  settings: {
    max_users: "最大用户数",
    max_flocks: "最大鹅群数",
    storage_limit: "存储限制"
  },
  created_at: "创建时间",
  updated_at: "更新时间"
}
```

### 2. 业务数据集合

#### flocks - 鹅群信息集合
```javascript
{
  _id: "flock_id",
  tenant_id: "租户ID",
  user_id: "用户ID",
  name: "鹅群名称",
  breed: "品种",
  count: "数量",
  age: "日龄",
  health_status: "健康状态", // healthy, sick, quarantine
  location: "位置信息",
  description: "描述",
  images: ["图片URL列表"],
  created_at: "创建时间",
  updated_at: "更新时间"
}
```

#### health_records - 健康记录集合
```javascript
{
  _id: "record_id",
  tenant_id: "租户ID",
  user_id: "用户ID",
  flock_id: "鹅群ID",
  record_type: "记录类型", // checkup, treatment, vaccination, diagnosis
  symptoms: "症状描述",
  diagnosis: "诊断结果",
  treatment: "治疗方案",
  ai_diagnosis: {
    result: "AI诊断结果",
    confidence: "置信度",
    suggestions: ["建议列表"]
  },
  images: ["图片URL列表"],
  veterinarian: "兽医信息",
  cost: "费用",
  notes: "备注",
  created_at: "创建时间",
  updated_at: "更新时间"
}
```

#### production_records - 生产记录集合
```javascript
{
  _id: "record_id",
  tenant_id: "租户ID",
  user_id: "用户ID",
  flock_id: "鹅群ID",
  record_date: "记录日期",
  egg_count: "产蛋数量",
  feed_consumption: "饲料消耗",
  water_consumption: "饮水量",
  weight_data: {
    average_weight: "平均体重",
    sample_count: "采样数量"
  },
  environment: {
    temperature: "温度",
    humidity: "湿度",
    air_quality: "空气质量"
  },
  mortality: "死亡数量",
  notes: "备注",
  created_at: "创建时间",
  updated_at: "更新时间"
}
```

### 3. 商城相关集合

#### products - 商品信息集合
```javascript
{
  _id: "product_id",
  tenant_id: "租户ID",
  name: "商品名称",
  description: "商品描述",
  price: "价格",
  original_price: "原价",
  stock: "库存",
  category: "分类",
  tags: ["标签列表"],
  images: ["图片URL列表"],
  specifications: {
    weight: "重量",
    size: "尺寸",
    material: "材质"
  },
  status: "商品状态", // active, inactive, out_of_stock
  sales_count: "销售数量",
  rating: "评分",
  created_at: "创建时间",
  updated_at: "更新时间"
}
```

#### orders - 订单信息集合
```javascript
{
  _id: "order_id",
  tenant_id: "租户ID",
  user_id: "用户ID",
  order_no: "订单号",
  products: [
    {
      product_id: "商品ID",
      name: "商品名称",
      price: "单价",
      quantity: "数量",
      subtotal: "小计"
    }
  ],
  total_amount: "总金额",
  discount_amount: "优惠金额",
  shipping_fee: "运费",
  final_amount: "实付金额",
  status: "订单状态", // pending, paid, shipped, delivered, cancelled
  payment_status: "支付状态", // unpaid, paid, refunded
  payment_method: "支付方式",
  transaction_id: "交易ID",
  shipping_address: {
    name: "收货人",
    phone: "电话",
    address: "地址",
    postal_code: "邮编"
  },
  shipping_info: {
    company: "物流公司",
    tracking_no: "快递单号",
    shipped_at: "发货时间"
  },
  created_at: "创建时间",
  updated_at: "更新时间"
}
```

### 4. OA办公集合

#### oa_applications - OA申请集合
```javascript
{
  _id: "application_id",
  tenant_id: "租户ID",
  user_id: "申请人ID",
  type: "申请类型", // leave, purchase, reimbursement, other
  title: "申请标题",
  content: "申请内容",
  amount: "金额",
  currency: "货币单位",
  category: "分类",
  attachments: ["附件URL列表"],
  status: "审批状态", // pending, approved, rejected, cancelled
  workflow: [
    {
      step: "步骤",
      approver_id: "审批人ID",
      approver_name: "审批人姓名",
      action: "操作", // approve, reject, forward
      comment: "意见",
      timestamp: "时间戳"
    }
  ],
  current_approver_id: "当前审批人ID",
  created_at: "创建时间",
  updated_at: "更新时间",
  completed_at: "完成时间"
}
```

### 5. 系统配置集合

#### system_config - 系统配置集合
```javascript
{
  _id: "config_id",
  tenant_id: "租户ID", // null表示全局配置
  category: "配置分类", // ai, payment, notification, general
  key: "配置键",
  value: "配置值",
  description: "配置描述",
  type: "数据类型", // string, number, boolean, object, array
  is_public: "是否公开",
  created_at: "创建时间",
  updated_at: "更新时间"
}
```

#### announcements - 公告信息集合
```javascript
{
  _id: "announcement_id",
  tenant_id: "租户ID", // null表示全局公告
  title: "公告标题",
  content: "公告内容",
  type: "公告类型", // system, maintenance, feature, promotion
  priority: "优先级", // low, normal, high, urgent
  target_users: ["目标用户ID列表"], // 空数组表示所有用户
  status: "状态", // draft, published, archived
  publish_at: "发布时间",
  expire_at: "过期时间",
  read_count: "阅读次数",
  created_at: "创建时间",
  updated_at: "更新时间"
}
```

#### knowledge_base - 知识库集合
```javascript
{
  _id: "knowledge_id",
  tenant_id: "租户ID", // null表示全局知识
  category: "分类",
  title: "标题",
  content: "内容",
  tags: ["标签列表"],
  author_id: "作者ID",
  status: "状态", // draft, published, archived
  view_count: "查看次数",
  like_count: "点赞次数",
  attachments: ["附件URL列表"],
  created_at: "创建时间",
  updated_at: "更新时间"
}
```

## 📋 索引设计

### 用户相关索引
- users: openid (唯一), tenant_id, role
- tenants: tenant_code (唯一), status

### 业务数据索引
- flocks: tenant_id + user_id, health_status
- health_records: tenant_id + flock_id, record_type, created_at
- production_records: tenant_id + flock_id, record_date

### 商城相关索引
- products: tenant_id + status, category, created_at
- orders: tenant_id + user_id, status, created_at

### OA办公索引
- oa_applications: tenant_id + user_id, status, type, created_at

### 系统配置索引
- system_config: tenant_id + category + key (唯一)
- announcements: tenant_id + status, publish_at
- knowledge_base: tenant_id + category, status, created_at

---

*此文档定义了云数据库的完整集合结构，为云开发项目提供数据存储规范。*
