# 智慧养鹅云开发平台 v3.0.0

## 📖 项目简介

智慧养鹅云开发平台是基于微信小程序云开发技术构建的现代化养鹅管理系统。通过云函数、云数据库、云存储等云开发能力，为养鹅企业提供全方位的数字化管理解决方案。

### 🌟 核心特性

- **🔐 微信登录认证** - 基于微信生态的安全登录体系
- **🦢 智能鹅群管理** - 全面的鹅群信息管理和健康监控
- **🏥 AI健康诊断** - 智能化的疾病诊断和治疗建议
- **🛒 电商购物系统** - 完整的商品展示、购买和订单管理
- **📋 OA办公流程** - 请假、采购、报销等审批流程
- **📊 数据分析统计** - 实时的业务数据分析和可视化
- **👥 多租户架构** - 支持多个养鹅企业独立使用
- **📱 响应式设计** - 适配各种屏幕尺寸的设备

## 🏗️ 技术架构

### 前端技术栈
- **微信小程序** - 原生小程序开发
- **云开发SDK** - 微信小程序云开发能力
- **组件化开发** - 可复用的UI组件库

### 后端技术栈
- **云函数** - 无服务器计算服务
- **云数据库** - NoSQL文档数据库
- **云存储** - 文件存储和CDN服务
- **云调用** - 微信API调用能力

### 架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   小程序前端     │    │   云开发后端     │    │   微信生态      │
│                │    │                │    │                │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │   页面层    │ │    │ │   云函数    │ │    │ │  微信登录   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │   组件层    │ │◄──►│ │  云数据库   │ │    │ │  微信支付   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │   工具层    │ │    │ │   云存储    │ │    │ │  消息推送   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求
- 微信开发者工具 1.05.0 或更高版本
- Node.js 14.0 或更高版本
- 已注册的微信小程序账号
- 已开通云开发服务

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 智慧养鹅云开发
   ```

2. **配置小程序信息**
   - 在 `project.config.json` 中配置你的小程序 AppID
   - 在微信公众平台配置服务器域名

3. **开通云开发**
   - 在微信开发者工具中开通云开发服务
   - 创建云开发环境并记录环境ID

4. **配置云开发环境**
   ```javascript
   // 在 app.js 中配置环境ID
   wx.cloud.init({
     env: 'your-env-id', // 替换为你的环境ID
     traceUser: true
   });
   ```

5. **部署云函数**
   - 在开发者工具中右键 `cloudfunctions` 目录
   - 选择"上传并部署：云端安装依赖"
   - 等待所有云函数部署完成

6. **初始化数据库**
   - 运行数据库初始化脚本
   - 创建必要的集合和索引
   - 插入初始数据

## 🛠️ 技术栈

### 前端技术
- **小程序框架**: 微信小程序原生开发
- **UI组件**: TDesign小程序组件库
- **状态管理**: 页面级状态管理
- **网络请求**: 封装的HTTP请求库

### 后端技术
- **运行环境**: Node.js 16+
- **Web框架**: Express.js
- **数据库**: MySQL 8.0
- **ORM框架**: Sequelize
- **身份认证**: JWT + BCrypt

### 开发工具
- **API测试**: 内置Playwright自动化测试
- **代码规范**: ESLint + Prettier
- **版本控制**: Git
- **部署工具**: Docker支持

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 8.0
- 微信开发者工具

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd 智慧养鹅V2.9.2
```

2. **安装依赖**
```bash
npm install
```

3. **配置数据库**
```bash
# 初始化数据库
npm run db:init

# 导入测试数据
npm run db:seed
```

4. **启动服务**
```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

5. **配置小程序**
- 在微信开发者工具中导入项目
- 修改`project.config.json`中的appid
- 配置服务器域名

## 📖 API文档

### 认证接口
```
POST /api/v1/auth/login     # 用户登录
POST /api/v1/auth/logout    # 用户登出
GET  /api/v1/auth/profile   # 获取用户信息
```

### 鹅群管理
```
GET    /api/v1/flocks           # 获取鹅群列表
POST   /api/v1/flocks           # 创建鹅群
GET    /api/v1/flocks/:id       # 获取鹅群详情
PUT    /api/v1/flocks/:id       # 更新鹅群信息
DELETE /api/v1/flocks/:id       # 删除鹅群
```

### 健康记录
```
GET  /api/v1/health/records     # 获取健康记录
POST /api/v1/health/records     # 创建健康记录
GET  /api/v1/health/ai-diagnosis # AI诊断接口
```

### 生产管理
```
GET  /api/v1/production/records     # 获取生产记录
POST /api/v1/production/records     # 创建生产记录
GET  /api/v1/production/environment # 环境数据
POST /api/v1/production/ai-inventory # AI盘点
```

## 🔧 配置说明

### 环境变量
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=smart_goose_saas
DB_USER=saas_admin
DB_PASSWORD=your_password

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# 微信配置
WECHAT_APPID=your_appid
WECHAT_SECRET=your_secret
```

### 小程序配置
```javascript
// app.js 全局配置
globalData: {
  baseUrl: 'https://your-domain.com/api/v1',
  appVersion: '2.9.2',
  env: 'production'
}
```

## 📁 目录结构详解

### 小程序端目录
```
pages/
├── home/           # 首页
├── login/          # 登录页
├── health/         # 健康管理
├── production/     # 生产管理
├── shop/           # 商城
├── orders/         # 订单管理
├── profile/        # 个人中心
└── oa/             # 办公自动化

components/
├── common/         # 通用组件
├── chart/          # 图表组件
├── form-builder/   # 表单构建器
└── weather/        # 天气组件

utils/
├── api.js          # API接口
├── auth.js         # 认证工具
├── storage.js      # 存储工具
└── common.js       # 通用工具
```

### 后端目录
```
backend/
├── controllers/    # 控制器
├── models/         # 数据模型
├── routes/         # 路由定义
├── middleware/     # 中间件
├── utils/          # 工具函数
├── config/         # 配置文件
└── docs/          # API文档

saas-admin/
├── models/         # SAAS管理模型
├── routes/         # 管理路由
└── public/         # 静态资源
```

## 🧪 测试

### 运行测试
```bash
# 单元测试
npm test

# 集成测试
npm run test:integration

# E2E测试（Playwright）
npm run test:e2e
```

### 测试覆盖率
- 后端API测试覆盖率: 85%+
- 前端组件测试覆盖率: 70%+

## 📦 部署

### Docker部署
```bash
# 构建镜像
docker build -t smart-goose-saas .

# 运行容器
docker run -d -p 3000:3000 smart-goose-saas
```

### 生产环境部署
1. 配置生产环境变量
2. 构建生产版本
3. 配置反向代理
4. 设置SSL证书
5. 配置数据库备份

## 🔒 安全特性

- **数据加密**: 敏感数据AES加密存储
- **SQL注入防护**: 参数化查询
- **XSS防护**: 输入输出过滤
- **CSRF防护**: Token验证
- **权限控制**: 基于角色的访问控制

## 📈 性能优化

- **缓存策略**: Redis缓存热点数据
- **图片优化**: WebP格式，CDN加速
- **数据库优化**: 索引优化，查询优化
- **小程序优化**: 分包加载，懒加载

## 🐛 常见问题

### Q: 小程序编译错误
A: 检查`project.config.json`配置，确保appid正确

### Q: 后端服务无法启动
A: 检查数据库连接配置和端口占用

### Q: 权限验证失败
A: 检查JWT token是否过期，重新登录

### Q: AI功能无法使用
A: 检查AI服务配置和网络连接

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情

## 📞 技术支持

- **技术文档**: [详细文档链接]
- **问题反馈**: [Issue链接]
- **技术交流**: [微信群二维码]

## 🔄 版本历史

### v2.9.2 (当前版本)
- ✅ 优化页面路由结构
- ✅ 统一组件命名规范  
- ✅ 修复数据库模型一致性问题
- ✅ 清理冗余文档和临时文件
- ✅ 优化代码质量和性能

### v2.9.1
- 🐛 修复OA权限系统问题
- 🔧 优化AI盘点功能
- 📱 改进小程序UI体验

### v2.9.0
- 🎉 新增多租户SAAS架构
- 🤖 集成AI诊断功能
- 💼 完整OA办公系统

---

**智慧养鹅SAAS平台 - 让养鹅管理更智能、更高效！** 🚀