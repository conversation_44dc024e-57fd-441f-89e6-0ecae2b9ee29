# 云开发重构文件清理计划

## 🗂️ 需要删除的文件和目录

### 1. 后端相关文件（完全删除）
```
backend/                    # 整个后端目录
├── app.js
├── server.js
├── unified-server.js
├── controllers/
├── models/
├── routes/
├── middleware/
├── services/
├── config/
├── utils/
├── docs/
├── tests/
├── migrations/
├── scripts/
├── admin/
├── saas-admin/
├── src/
├── shared/
├── logs/
├── node_modules/
├── package.json
├── package-lock.json
├── tsconfig.json
└── 所有其他后端文件
```

### 2. 数据库相关文件
```
database/                   # 数据库脚本目录
├── init.sql
├── schema.sql
├── standardized-schema.sql
├── unified-saas-schema.sql
├── saas-platform-init.sql
├── ai-config-tables.sql
├── field-normalization.sql
├── missing-tables.sql
├── 防疫流程数据库设计.sql
└── 防疫流程步骤数据.sql

database-unified-schema.sql
create-platform-users-table.sql
fix-users-table.sql
```

### 3. 服务器配置文件
```
nginx.conf
ecosystem.config.js
package.json                # 根目录的Node.js配置
```

### 4. 开发和测试相关文件
```
tests/                      # 测试目录
test-results/              # 测试结果
playwright-report/         # Playwright报告
playwright.config.js       # Playwright配置
eslint.config.js           # ESLint配置

# 测试和调试脚本
*.test.js
*-test.js
test-*.js
*-test-*.js
```

### 5. 部署和运维脚本
```
scripts/                   # 脚本目录
├── batch-syntax-fix.sh
├── cleanup-project.sh
├── consolidate-optimized.sh
├── eslint-final-report.sh
├── eslint-fix.sh
├── full-validation.sh
├── health-check.js
├── monitoring.sh
├── optimize-dependencies.sh
├── production-cleanup-safe.js
├── production-cleanup.js
├── production-deploy.sh
├── server-setup.sh
└── 其他脚本文件

# 根目录脚本
convert_icons.sh
deep-test-all-pages.sh
quick-validation.sh
restart-and-clear-cache.sh
saas-comprehensive-test.sh
setup-playwright-mcp.sh
start-local-dev.sh
start-production.sh
stop-services.sh
```

### 6. 日志和临时文件
```
logs/                      # 日志目录
cookies.txt
*.log
*.tmp
```

### 7. 管理后台视图文件
```
views/                     # 服务器端视图
├── auth/
├── health/
├── icons/
├── partials/
├── production/
├── settings/
└── users/
```

### 8. 开发工具和配置
```
admin-explorer.js
quick-login-server.js
simple-login-server.js
test-layout.html
test-login.html
update-admin-passwords.js
final-fix-system.js
fix-admin-user.js
```

### 9. 报告和文档文件（保留部分重要文档）
```
# 删除的文档
API_DOCUMENTATION.md
API接口文档.md
CODE_CLEANUP_EXECUTION_REPORT.md
COMPREHENSIVE_PROJECT_ANALYSIS_REPORT.md
DASHBOARD_OPTIMIZATION.md
DEPLOYMENT-CHECKLIST.md
DEPLOYMENT.md
DEPLOYMENT_CHECKLIST.md
DEPLOYMENT_OPERATIONS_MANUAL.md
FINAL_COMPREHENSIVE_TEST_REPORT.md
FINAL_PROJECT_REVIEW_REPORT.md
PERFORMANCE_VERIFICATION_GUIDE.md
PRODUCTION_READINESS_REPORT.md
PROJECT-COMPLETION-SUMMARY.md
PROJECT-STATUS-REPORT.md
PROJECT_STRUCTURE_OPTIMIZATION_REPORT.md
SAAS_ADMIN_TEST_REPORT.md
SAAS_INTEGRATION_TEST_REPORT.md
SUBSCRIPTION_MANAGEMENT_FIX.md
SYSTEM-COMPLETION-REPORT.md
SaaS-多租户架构实现总结.md

# 中文报告文档
智慧养鹅SAAS系统*.md
智慧养鹅全栈系统*.md
智慧养鹅后台管理*.md
智慧养鹅系统*.md
修改生效问题解决指南.md
服务配置规范文档.md

# JSON报告文件
*.json (除了必要的配置文件)
admin-exploration-report.json
admin-test-report.json
critical-modules-test-report.json
final-comprehensive-test-report.json
fix-test-report.json
module-test-report.json
saas-admin-final-report.json
```

### 10. 其他临时和开发文件
```
node_modules/              # Node.js依赖
screenshots/               # 截图文件
your-project/              # 临时项目文件
鼎晶1600只鹅科学防疫养殖流程完美.xlsx
VERSION.json
```

## 🔄 保留的文件和目录

### 小程序核心文件
```
app.js                     # 小程序入口
app.json                   # 小程序配置
app.wxss                   # 全局样式
project.config.json        # 项目配置
project.private.config.json # 私有配置
sitemap.json               # 站点地图
```

### 小程序页面和组件
```
pages/                     # 页面目录
components/                # 组件目录
styles/                    # 样式文件
utils/                     # 工具函数
constants/                 # 常量定义
constants-data/            # 常量数据
images/                    # 图片资源
assets/                    # 静态资源
```

### 重要文档
```
README.md                  # 项目说明
CLAUDE.md                  # Claude指导文档
QUICK_START.md             # 快速开始
云开发重构分析报告.md        # 重构分析
云开发架构设计方案.md        # 架构设计
文件清理计划.md             # 本文件
```

### 部分docs文件（选择性保留）
```
docs/user-manual.md        # 用户手册
docs/development-standards.md # 开发规范
docs/permission-system.md  # 权限系统文档
```

## 📋 清理执行步骤

1. **备份重要数据**
   - 导出数据库数据
   - 备份配置文件
   - 保存重要文档

2. **删除后端相关**
   - 删除backend目录
   - 删除数据库脚本
   - 删除服务器配置

3. **删除开发工具**
   - 删除测试文件
   - 删除构建脚本
   - 删除开发工具

4. **清理文档**
   - 删除过时报告
   - 保留核心文档
   - 整理文档结构

5. **验证清理结果**
   - 检查小程序文件完整性
   - 确认必要文件保留
   - 测试项目结构

## ⚠️ 注意事项

1. **数据安全**：删除前确保重要数据已备份
2. **渐进式清理**：分批次删除，避免误删
3. **功能验证**：每次清理后验证小程序功能
4. **版本控制**：使用Git管理清理过程
5. **回滚准备**：保留回滚能力以防意外

---

*此计划将指导云开发重构过程中的文件清理工作，确保只保留必要的小程序文件。*
