/**
 * 统一UI组件规范
 * 基于微信小程序云开发最佳实践
 */

// 统一颜色规范
const UI_COLORS = {
  // 主色调
  PRIMARY: '#0066CC',
  PRIMARY_LIGHT: '#4D94FF',
  PRIMARY_DARK: '#004499',
  
  // 功能色
  SUCCESS: '#52C41A',
  WARNING: '#FAAD14', 
  ERROR: '#FF4D4F',
  INFO: '#1890FF',
  
  // 中性色
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  GRAY_1: '#F5F5F5',
  GRAY_2: '#E8E8E8',
  GRAY_3: '#CCCCCC',
  GRAY_4: '#999999',
  GRAY_5: '#666666',
  
  // 背景色
  BG_PRIMARY: '#FFFFFF',
  BG_SECONDARY: '#F8F9FA',
  BG_TERTIARY: '#F5F5F5'
};

// 统一字体规范
const UI_FONTS = {
  SIZE_XS: '20rpx',
  SIZE_SM: '24rpx', 
  SIZE_MD: '28rpx',
  SIZE_LG: '32rpx',
  SIZE_XL: '36rpx',
  SIZE_XXL: '40rpx',
  
  WEIGHT_NORMAL: 400,
  WEIGHT_MEDIUM: 500,
  WEIGHT_BOLD: 600
};

// 统一间距规范
const UI_SPACING = {
  XS: '8rpx',
  SM: '16rpx',
  MD: '24rpx', 
  LG: '32rpx',
  XL: '48rpx',
  XXL: '64rpx'
};

// 统一圆角规范
const UI_RADIUS = {
  SM: '8rpx',
  MD: '12rpx',
  LG: '16rpx',
  XL: '24rpx',
  ROUND: '50%'
};

// 统一阴影规范
const UI_SHADOWS = {
  SM: '0 2rpx 8rpx rgba(0, 0, 0, 0.1)',
  MD: '0 4rpx 16rpx rgba(0, 0, 0, 0.1)',
  LG: '0 8rpx 24rpx rgba(0, 0, 0, 0.15)'
};

module.exports = {
  UI_COLORS,
  UI_FONTS,
  UI_SPACING,
  UI_RADIUS,
  UI_SHADOWS
};